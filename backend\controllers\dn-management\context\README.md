# Domain Name Management - Contact Controller

This directory contains the contact management functionality for domain name operations.

## Overview

The contact management system provides comprehensive functionality for managing contacts in the domain registration system, including:

- Creating and managing contacts
- Customer creation
- Contact search and retrieval
- Special handling for different TLD requirements

## Files Structure

```
backend/controllers/dn-management/
├── contactController.js    # Main controller with all contact operations
└── README.md              # This documentation file
```

## API Endpoints

### Contact Management

- `POST /contact/add` - Add a new contact
- `PUT /contact/modify` - Modify an existing contact  
- `GET /contact/:contactId` - Get contact details by ID
- `GET /contact/search` - Search contacts with criteria
- `GET /contact/default/:customerId` - Get default contact for a customer
- `POST /contact/associate-extra` - Associate extra details for specific TLDs
- `DELETE /contact/:contactId` - Delete a contact

### Customer Management

- `POST /contact/customer/create` - Create a new customer

## Usage Examples

### Adding a Contact

```javascript
// Request body
{
  "contactDetails": {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "company": "Example Corp",
    "address": "123 Main St",
    "city": "New York",
    "country": "US",
    "zipcode": "10001",
    "phoneCountryCode": "1",
    "phone": "5551234567",
    "type": "Contact" // Use "MXContact" for .lat domains
  },
  "customerId": "12345"
}
```

### Modifying a Contact

```javascript
// Request body
{
  "contactId": "67890",
  "updatedDetails": {
    "name": "John Smith",
    "email": "<EMAIL>"
  }
}
```

### Creating a Customer

```javascript
// Request body
{
  "email": "<EMAIL>",
  "name": "Customer Name",
  "company": "Customer Corp",
  "address": "456 Oak Ave",
  "city": "Los Angeles",
  "country": "US",
  "zipcode": "90210",
  "phoneCountryCode": "1",
  "phone": "5559876543"
}
```

## Special TLD Handling

### .lat Domains
For .lat domains, use `"type": "MXContact"` when creating contacts.

### .au Domains
Additional attributes are handled automatically in the domain registration process.

## Error Handling

All endpoints return standardized error responses:

```javascript
{
  "error": "Error description",
  "details": "Detailed error information from API",
  "success": false
}
```

## Dependencies

- `axios` - HTTP client for API requests
- `contactService` - Business logic layer
- `checkUserOrRefreshToken` - Authentication middleware

## Configuration

The system uses environment variables for API configuration:
- `API_BASE_URL_TEST` - Base URL for the domain API
- `AUTH_USERID_TEST` - Authentication user ID
- `API_KEY_TEST` - API key for authentication
