"use client";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import CloudMarocIntro from "@/components/cloud-maroc/cloudMarocIntro";
import CloudVps from "@/components/cloud-maroc/cloudVps";
import SharedCloud from "@/components/cloud-maroc/sharedCloud";
import ContactForm2 from "@/components/shared/contactForm2";
import { Alert } from "@material-tailwind/react";
import { Stars } from "lucide-react";

function CloudMaroc() {
  const t = useTranslations("cloud");
  const [showAlert, setShowAlert] = useState(true);

  const [data, setData] = useState({
    page: "Cloud au maroc",
    offerName: "No offer selected",
    id: 0,
    url: "/cloud-maroc",
  });

  return (
    
    <div className="min-h-screen w-full">
      {showAlert && (
      <Alert
        variant="gradient"
        color="blue"
        className="fixed top-24 right-4 w-auto max-w-sm z-50 rounded-xl shadow-lg"
        animate={{
          mount: { y: 0 },
          unmount: { y: -100 },
        }}
        dismissible={{
          onClose: () => setShowAlert(false),
        }}
      >
        <div className="flex items-center gap-2">
          <Stars className="w-5 h-5" />
          <h6 className="font-medium">{t('coming_soon')}</h6>
        </div>
      </Alert>
    )}
      <CloudMarocIntro t={t} />
      <CloudVps t={t} setData={setData} />
      <SharedCloud t={t} setData={setData} />
      <ContactForm2 data={data} setData={setData} t={t} />
    </div>
  );
}

export default CloudMaroc;
