# Domain Name Management System - Complete File Structure

This README provides a comprehensive overview of all files and their paths related to the domain name management functionality in the ZTech application.

## 📁 Backend Files

### 🎯 Controllers
**Path: `backend/controllers/dn-management/`**

#### `domainMngController.js`
- **Purpose**: Main domain management controller handling all domain operations
- **Contains**: 
  - Domain availability checking (standard, IDN, premium)
  - Domain name suggestions
  - Pricing management (TLD pricing sync, domain pricing)
  - Cart operations (add domain to cart)
  - Domain search functionality
  - Customer signup for domain services
  - Domain registration endpoint
- **Key Functions**: `checkDomainAvailability`, `handleDomainSearch`, `addDomainToCart`, `registerDomain`, `syncTldPricing`

#### `userContactController.js`
- **Purpose**: Manages user-specific domain contacts for registration
- **Contains**:
  - CRUD operations for domain contacts (registrant, admin, tech, billing)
  - Contact validation and formatting
  - Dual storage (MongoDB + external reseller API)
  - Contact synchronization between databases
- **Key Functions**: `createOrUpdateDomainContact`, `getUserDomainContacts`, `updateDomainContact`, `copyDomainContact`

#### `contactController.js`
- **Purpose**: General contact management for domain registration system
- **Contains**:
  - Contact creation and modification
  - Contact search and retrieval
  - Customer creation in reseller system
- **Key Functions**: `addContact`, `modifyContact`, `getContactDetails`, `createCustomer`

#### `README.md`
- **Purpose**: Documentation for the dn-management controller directory
- **Contains**: API endpoints documentation and file structure overview

### 🛠️ Services
**Path: `backend/services/`**

#### `domainRegistrationService.js`
- **Purpose**: Handles automatic domain registration after payment confirmation
- **Contains**:
  - Domain registration logic for paid orders
  - User contact retrieval and validation
  - API integration with Heberjahiz domain reseller
  - Nameserver configuration
- **Key Functions**: `registerDomain`, `getUserDomainContacts`

#### `contactService.js`
- **Purpose**: Service layer for contact operations with external API
- **Contains**:
  - Contact synchronization utilities
  - External API communication
  - Contact validation and formatting
- **Key Functions**: `ensureContactInResellerAPI`, contact CRUD operations

### 🗄️ Models
**Path: `backend/models/`**

#### `Contact.js`
- **Purpose**: MongoDB schema for domain contacts
- **Contains**:
  - Contact information fields (name, email, address, phone, etc.)
  - External contact ID mapping
  - Contact type enumeration (registrant, admin, tech, billing)
  - User association and indexing
- **Key Features**: Dual storage support, contact validation methods

#### `User.js` (Domain-related fields)
- **Purpose**: User model with domain contact references
- **Contains**:
  - `domainContacts` object with references to Contact documents
  - Contact type fields: registrant, admin, tech, billing
- **Key Features**: Population support for contact details

#### `TldPricing.js`
- **Purpose**: Schema for TLD (Top Level Domain) pricing information
- **Contains**:
  - TLD pricing data (register, renewal, transfer costs)
  - Raw pricing data storage
  - Currency and period information
- **Key Features**: Unique TLD indexing, pricing synchronization

#### `DomainPricing.js`
- **Purpose**: Schema for comprehensive domain pricing structure
- **Contains**:
  - Nested pricing periods (1 year, 2 years, etc.)
  - Multiple domain operations pricing
  - Dynamic TLD support
- **Key Features**: Flexible schema for various pricing structures

### 🛣️ Routes
**Path: `backend/routes/`**

#### `domainMngRouter.js`
- **Purpose**: Main routing for domain management endpoints
- **Contains**:
  - Domain availability and search routes
  - Pricing information routes
  - Cart management routes
  - Domain registration routes
- **Endpoints**: `/check-domain`, `/search-domains`, `/add-domain-to-cart`, `/register-domain`

#### `userContactRouter.js`
- **Purpose**: User-specific contact management routes
- **Contains**:
  - Contact CRUD operations
  - Contact copying functionality
  - Contact synchronization endpoints
- **Endpoints**: `/domain-contacts`, `/domain-contacts/:contactType`, `/domain-contacts/copy`

#### `contactRouter.js`
- **Purpose**: General contact management routes
- **Contains**:
  - Contact operations for domain registration
  - Customer creation endpoints
- **Endpoints**: `/add`, `/modify`, `/search`, `/customer/create`

### 🔧 Utilities
**Path: `backend/utils/`**

#### `tldMapping.js`
- **Purpose**: TLD to product key mapping and pricing utilities
- **Contains**:
  - TLD to API product key conversion
  - Pricing calculation functions
  - Supported TLD list
- **Key Functions**: `getProductKeyForTld`, `getTldPricing`, `getAllTlds`

### 📋 Configuration
**Path: `backend/config/`**

#### `domain-registration.env.example`
- **Purpose**: Environment variables template for domain registration
- **Contains**:
  - API credentials configuration
  - Company account settings
  - Default nameserver configuration
- **Key Variables**: `AUTH_USERID_PROD`, `COMPANY_CUSTOMER_ID`, nameserver settings

### 📚 Documentation
**Path: `backend/docs/`**

#### `domain-registration-user-contacts-update.md`
- **Purpose**: Documentation for user contact implementation
- **Contains**:
  - Architecture changes overview
  - Contact storage strategy
  - Implementation details
- **Key Topics**: Dual storage, contact synchronization, company customer ID usage

#### `contact-management-improvements.md`
- **Purpose**: Documentation for contact management improvements
- **Contains**:
  - Contact management flow
  - API endpoint documentation
  - Testing information
- **Key Topics**: Contact validation, error handling, testing scripts

#### `domain-registration-setup.md`
- **Purpose**: Setup guide for domain registration functionality
- **Contains**:
  - API endpoint documentation
  - Request/response examples
  - Configuration instructions
- **Key Topics**: Customer signup, domain registration, API usage

#### `auto-domain-registration.md`
- **Purpose**: Documentation for automatic domain registration
- **Contains**:
  - Automatic registration flow
  - Configuration requirements
  - Environment setup
- **Key Topics**: Payment integration, automatic processing

## 📱 Frontend Files

### 🎨 Components
**Path: `frontend/src/components/`**

#### `home/domainSearch.jsx`
- **Purpose**: Main domain search component for homepage
- **Contains**:
  - Domain availability checking
  - Search results display
  - Add to cart functionality
  - Period selection for domains
- **Key Features**: Real-time search, pricing display, cart integration

#### `home/DomaineSearch.jsx`
- **Purpose**: Alternative domain search component
- **Contains**:
  - Simplified domain search interface
  - Extension dropdown
  - Dummy data for testing
- **Key Features**: Basic search functionality, extension selection

#### `cart/domainContactModal.jsx`
- **Purpose**: Modal for managing domain contacts during checkout
- **Contains**:
  - Contact form for domain registration
  - Contact type tabs (registrant, admin, tech, billing)
  - Contact validation and saving
- **Key Features**: Multi-tab interface, form validation, contact management

#### `cart/domainCartItem.jsx`
- **Purpose**: Cart item component for domain products
- **Contains**:
  - Domain item display in cart
  - Contact management integration
  - Period and pricing display
- **Key Features**: Domain-specific cart handling, contact modal integration

### 📄 Pages
**Path: `frontend/src/app/[locale]/`**

#### `domains/page.jsx`
- **Purpose**: Dedicated domain services page
- **Contains**:
  - Domain search functionality
  - Domain services information
  - Feature highlights and benefits
- **Key Features**: Comprehensive domain services showcase, integrated search

### 🔌 Services
**Path: `frontend/src/app/services/`**

#### `domainMngService.js`
- **Purpose**: Frontend service for domain management API calls
- **Contains**:
  - Domain availability checking
  - Domain search and suggestions
  - Pricing information retrieval
  - Cart operations
  - Customer signup
- **Key Functions**: `checkDomainAvailability`, `searchDomains`, `addDomainToCart`

#### `userContactService.js`
- **Purpose**: Frontend service for user contact management
- **Contains**:
  - Contact CRUD operations
  - Contact copying functionality
- **Key Functions**: `getUserDomainContacts`, `createOrUpdateDomainContact`, `copyDomainContact`

### ⚙️ Configuration
**Path: `frontend/src/app/config/`**

#### `constant.js`
- **Purpose**: Frontend configuration constants
- **Contains**:
  - Backend URL configuration
  - Environment-specific settings
  - API endpoints base URLs
- **Key Constants**: `BACKEND_URL`, `FRONTEND_URL`, API configurations

## 🔗 Integration Points

### Server Configuration
**Path: `backend/server.js`**
- Domain routes integration: `/domainMng`, `/contact`, `/user-contact`

### Environment Variables Required
- `AUTH_USERID_TEST` / `AUTH_USERID_PROD` - API authentication
- `API_KEY_TEST` / `API_KEY_PROD` - API key
- `COMPANY_CUSTOMER_ID` - Company account for registrations
- `API_BASE_URL_TEST` - Domain API base URL

## 🎯 Key Features Implemented

1. **Domain Search & Availability**: Real-time domain checking with multiple TLD support
2. **Pricing Management**: Dynamic pricing sync with reseller API
3. **Contact Management**: Dual storage system (local + external)
4. **Cart Integration**: Domain-specific cart handling
5. **Automatic Registration**: Post-payment domain registration
6. **User Interface**: Dedicated domain services page and search components
7. **Contact Validation**: TLD-specific contact requirements
8. **Multi-language Support**: Internationalization ready

## 📊 Database Collections

- `contacts` - Domain contact information
- `users` - User accounts with domain contact references  
- `tldpricings` - TLD pricing information
- `domainpricings` - Comprehensive domain pricing data

This system provides a complete domain name management solution integrated with Heberjahiz's domain reseller API, supporting the full domain lifecycle from search to registration.
