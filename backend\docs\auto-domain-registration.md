# Automatic Domain Registration

This document explains how the automatic domain registration system works after successful payment.

## Overview

When a user purchases a domain name through your website, the following process occurs:

1. User adds domain(s) to cart
2. User completes checkout and makes payment
3. After successful payment, the system automatically:
   - Processes the payment
   - Registers the domain(s) using the company account
   - Updates the domain suborder status to ACTIVE
   - Sends order confirmation emails

## Configuration

### Environment Variables

Copy the example configuration file and add your credentials:

```bash
cp backend/config/domain-registration.env.example .env
```

Then edit the `.env` file with your actual credentials:

```
# Domain Registration API Credentials
AUTH_USERID_PROD=your_auth_userid
API_KEY_PROD=your_api_key

# Company Account for Domain Registrations
COMPANY_CUSTOMER_ID=your_customer_id
COMPANY_REG_CONTACT_ID=your_reg_contact_id
COMPANY_ADMIN_CONTACT_ID=your_admin_contact_id
COMPANY_TECH_CONTACT_ID=your_tech_contact_id
COMPANY_BILLING_CONTACT_ID=your_billing_contact_id

# Default Nameservers
DEFAULT_NS1=ns1.domain-name-api.dynv6.net
DEFAULT_NS2=ns2.domain-name-api.dynv6.net
```

### Setting Up the Company Account

1. Log in to your reseller control panel
2. Create a new customer account for your company
3. Create contact profiles for the company (registrant, admin, technical, and billing)
4. Note the IDs for the customer and all contacts
5. Add these IDs to your environment variables

## How It Works

### Domain Detection

The system identifies domain items in orders by checking if the package reference starts with `domain-`. This is automatically set when a domain is added to the cart.

### Registration Process

When a payment is successful:

1. The `markOrderAsPaid` function in `orderService.js` is called
2. This function calls `processDomainRegistrations` from `domainRegistrationService.js`
3. The service identifies all domain suborders in the order
4. For each domain suborder, it:
   - Extracts the domain name from the package name
   - Calls the domain registration API using the company account
   - Updates the suborder status to ACTIVE if successful
   - Logs the result

### Error Handling

If domain registration fails:
- The error is logged but doesn't prevent the order from being processed
- The suborder status remains as PROCESSING
- You can manually register the domain later if needed

## Customization

### Default Settings

By default, domains are registered with:
- Privacy protection enabled
- Auto-renewal disabled
- Default nameservers (configurable in environment variables)

### Changing Default Settings

To modify these defaults, edit the `registerDomain` function in `domainRegistrationService.js`.

## Troubleshooting

If domain registration fails, check:

1. Company account credentials in environment variables
2. API credentials
3. Domain availability (it may have been registered by someone else)
4. Server logs for detailed error messages

## Manual Registration

If automatic registration fails, you can manually register the domain:

1. Go to the order details in your admin panel
2. Find the domain suborder
3. Use the domain registration API directly or through your reseller control panel
4. Update the suborder status to ACTIVE once registered
