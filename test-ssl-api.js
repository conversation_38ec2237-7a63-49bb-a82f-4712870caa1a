// Simple test to verify SSL API endpoint
const https = require('https');
const http = require('http');

function testSSLAPI() {
  const options = {
    hostname: 'localhost',
    port: 5000, // Adjust port as needed
    path: '/admin/dashboard/ssl-stats',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers: ${JSON.stringify(res.headers)}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const jsonData = JSON.parse(data);
        console.log('\n=== SSL API Response ===');
        console.log(JSON.stringify(jsonData, null, 2));
        
        if (jsonData.data && jsonData.data.monthlyTrends && jsonData.data.monthlyTrends.series) {
          console.log('\n=== Series Data ===');
          jsonData.data.monthlyTrends.series.forEach((series, index) => {
            console.log(`${index + 1}. ${series.name}: [${series.data.join(', ')}]`);
          });
        }
      } catch (error) {
        console.error('Error parsing JSON:', error);
        console.log('Raw response:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('Request error:', error);
  });

  req.end();
}

console.log('Testing SSL Certificate Stats API...');
testSSLAPI();
