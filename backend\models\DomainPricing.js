const mongoose = require('mongoose');

// Define a schema for the nested pricing structure (e.g., {'1': 514.8, '2': 514.8})
const PricingPeriodSchema = new mongoose.Schema({
  // Using a Map to handle dynamic string keys ('1', '2', etc.)
  // The values will be Numbers
}, { _id: false, strict: false }); // strict: false allows arbitrary keys

// Define the schema for the main pricing data structure
const DomainPricingSchema = new mongoose.Schema({
  // Using a Map for the top-level keys like 'dotdate', 'addtransferdomain', etc.
  // The values will be objects conforming to PricingPeriodSchema
  dotdate: {
    addtransferdomain: PricingPeriodSchema,
    restoredomain: PricingPeriodSchema,
    addnewdomain: PricingPeriodSchema,
    renewdomain: PricingPeriodSchema,
    // Add other domain operations here if they appear in the API response
  },
  // Add other TLDs here if they appear in the API response (e.g., dotcom, dotnet)
  // Using a Map for dynamic TLD keys
}, { timestamps: true, strict: false }); // strict: false allows arbitrary TLD keys

// Create the model
const DomainPricing = mongoose.model('DomainPricing', DomainPricingSchema);

module.exports = DomainPricing;