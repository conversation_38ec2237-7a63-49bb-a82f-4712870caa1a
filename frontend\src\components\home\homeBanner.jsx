"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>og,
  <PERSON>Xml,
  <PERSON><PERSON>og,
  Shield<PERSON>heck,
} from "lucide-react";
import { motion } from "framer-motion";
import Image from "next/image";
import homeHeroImage from "/public/images/home/<USER>/bg7.webp";
import { useRouter } from "next/navigation";
import <PERSON><PERSON> from "lottie-react";
import homeHeroImg from "src/assets/home-banner-img.json";
import Countdown from "../shared/countdown";
import DomainSearch from "./domainSearch";

export default function HomeBanner({ t }) {
  const router = useRouter();

  // Set the target date (April 12, 2025)
  const targetDate = new Date("2025-07-11T00:00:00").getTime();
  const scrollToClaimOffer = () => {
    document
      .getElementById("claim-offer")
      ?.scrollIntoView({ behavior: "smooth", block: "start" });
  };

  return (
    <div className="w-full mx-auto font-inter relative md:h-[80vh]">
      <div className="relative z-10 max-w-[1900px] mx-auto">
        {/* Background Image with Gradient Overlay */}
        <div className="hidden md:block absolute inset-0 -z-50 md:h-[80vh]">
          <Image
            src={homeHeroImage}
            alt="homeBanner Background image"
            layout="fill"
            objectFit="cover"
            className="opacity-20"
            placeholder="blur"
            priority
          />
          <div className="absolute inset-0"></div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-5 lg:px-16 3xl:px-20 3xl:py-12 z-50">
          {/* Left Column */}
          <div className="flex flex-col gap-y-6">
            <h1 className="text-primary text-3xl md:text-4xl 3xl:text-5xl font-bold leading-tight">
              {t("banner.secure_speedy_website")}
            </h1>

            <div className="flex flex-col gap-y-2 text-blue-gray-900 font-medium">
              {[
                t("banner.run_wordpress_cms"),
                t("banner.reliable_uptime_guarantee"),
                t("banner.secure_ddos_protection"),
                t("banner.customer_support_24_7"),
                t("banner.free_ssl_certificate"),
                t("banner.fast_ssd_storage"),
              ].map((item, index) => (
                <div key={index} className="flex items-start gap-2">
                  <CheckCheck className="h-5 w-5 text-[#606af5] flex-shrink-0" />
                  <div className="flex items-center gap-1">
                    <span className="text-sm 3xl:text-base">{item}</span>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex items-baseline font-inter">
              <span className="text-gray-600 text-sm">
                {t("banner.starting_at")}
              </span>
              <span className="text-primary text-5xl font-bold mx-2">18</span>
              <span className="text-gray-800 text-base font-medium">
                {t("banner.currency_mad")}
              </span>
              <span className="text-gray-600 text-sm ml-2">
                {t("banner.per_month")}
              </span>
            </div>

            <div className="flex flex-col md:flex-row justify-start gap-2 w-full">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={scrollToClaimOffer}
                className="bg-[#606af5] uppercase hover:bg-blue-800 text-white font-medium py-3 px-6 rounded-md transition-all w-full sm:w-auto"
              >
                {t("banner.claim_deal")}
              </motion.button>

              {/* Use the Countdown component here */}
              <Countdown targetDate={targetDate} />
            </div>

            <div className="flex items-center gap-1 text-sm text-blue-900 font-medium font-inter">
              <ShieldCheck className="h-4 w-4 text-blue-900 font-semibold" />
              <span>{t("banner.money_back_guarantee")}</span>
            </div>
          </div>
          <div className="flex justify-center items-center my-auto">
            <Lottie
              animationData={homeHeroImg}
              loop={true}
              className="w-full max-w-sm lg:max-w-md"
            />
          </div>
          <div className="relative z-10 hidden md:flex flex-col gap-y-6 justify-center">
            <div className="flex flex-col gap-4">
              {[
                {
                  icon: CodeXml,
                  title: t("banner.web_development_title"),
                  desc: t("banner.web_development_desc"),
                  color: "bg-blue-400",
                  url: "/web-development",
                },
                {
                  icon: ServerCog,
                  title: t("banner.hosting_solutions_title"),
                  desc: t("banner.hosting_solutions_desc"),
                  color: "bg-purple-400",
                  url: "/hosting",
                },
                {
                  icon: ShieldCheck,
                  title: t("banner.ssl_certificates_title"),
                  desc: t("banner.ssl_certificates_desc"),
                  color: "bg-green-400",
                  url: "/ssl",
                },
                {
                  icon: CloudCog,
                  title: t("banner.cloud_services_title"),
                  desc: t("banner.cloud_services_desc"),
                  color: "bg-orange-400",
                  url: "/cloud-maroc",
                },
              ].map((service, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  onClick={() => {
                    router.push(service.url);
                  }}
                  className="bg-white/95 cursor-pointer backdrop-blur-sm rounded-lg p-5 shadow-lg border border-gray-300 hover:shadow-xl transition-all group"
                >
                  <div className="flex gap-4">
                    <div
                      className={`${service.color} w-14 h-14 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform`}
                    >
                      <service.icon className="h-7 w-7 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg text-gray-900 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-sm text-gray-600">{service.desc}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <DomainSearch t={t} />
    </div>
  );
}
