"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx":
/*!*******************************************************!*\
  !*** ./src/app/[locale]/client/domains/[id]/page.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/domains/NameserverManager */ \"(app-pages-browser)/./src/components/domains/NameserverManager.jsx\");\n/* harmony import */ var _components_domains_PrivacyProtectionManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/domains/PrivacyProtectionManager */ \"(app-pages-browser)/./src/components/domains/PrivacyProtectionManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction DomainDetailPage(param) {\n    let { params } = param;\n    _s();\n    const { id } = params;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"client.domainWrapper\");\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [apiError, setApiError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getDomainDetails = async ()=>{\n            try {\n                var _domainsRes_data;\n                setLoading(true);\n                // First, get the user's domains to find the domain name by ID\n                const domainsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getUserDomains();\n                const userDomains = ((_domainsRes_data = domainsRes.data) === null || _domainsRes_data === void 0 ? void 0 : _domainsRes_data.domains) || [];\n                // Find the domain with the matching ID\n                const userDomain = userDomains.find((d)=>d.id === id);\n                if (!userDomain) {\n                    console.error(\"Domain not found with ID:\", id);\n                    setLoading(false);\n                    return;\n                }\n                console.log(\"Found user domain:\", userDomain);\n                // Get comprehensive domain details from the registration system API\n                try {\n                    var _detailsRes_data;\n                    // Use the domains/details-by-name.json endpoint with comprehensive options\n                    const detailsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getDomainDetailsByName(userDomain.name, \"All\" // Get all available details including OrderDetails, ContactIds, NsDetails, DomainStatus, etc.\n                    );\n                    console.log(\"Domain details from registration API:\", detailsRes.data);\n                    const apiDomain = ((_detailsRes_data = detailsRes.data) === null || _detailsRes_data === void 0 ? void 0 : _detailsRes_data.domain) || {};\n                    // Parse nameservers from API response\n                    const nameservers = [];\n                    if (apiDomain.ns1) nameservers.push(apiDomain.ns1);\n                    if (apiDomain.ns2) nameservers.push(apiDomain.ns2);\n                    if (apiDomain.ns3) nameservers.push(apiDomain.ns3);\n                    if (apiDomain.ns4) nameservers.push(apiDomain.ns4);\n                    // Parse contact details from API response\n                    const contacts = {\n                        registrant: apiDomain.registrantcontact || {\n                            name: dt(\"not_available\"),\n                            email: dt(\"not_available\"),\n                            phone: dt(\"not_available\"),\n                            address: dt(\"not_available\")\n                        },\n                        admin: apiDomain.admincontact || {\n                            name: dt(\"not_available\"),\n                            email: dt(\"not_available\"),\n                            phone: dt(\"not_available\"),\n                            address: dt(\"not_available\")\n                        },\n                        tech: apiDomain.techcontact || {\n                            name: dt(\"not_available\"),\n                            email: dt(\"not_available\"),\n                            phone: dt(\"not_available\"),\n                            address: dt(\"not_available\")\n                        },\n                        billing: apiDomain.billingcontact || {\n                            name: dt(\"not_available\"),\n                            email: dt(\"not_available\"),\n                            phone: dt(\"not_available\"),\n                            address: dt(\"not_available\")\n                        }\n                    };\n                    // Format dates from API response\n                    const formatDate = (timestamp)=>{\n                        if (!timestamp) return dt(\"not_available\");\n                        try {\n                            return new Date(timestamp * 1000).toLocaleDateString();\n                        } catch (e) {\n                            return timestamp;\n                        }\n                    };\n                    // Combine user domain data with comprehensive API details\n                    const combinedDomain = {\n                        id: userDomain.id,\n                        name: userDomain.name,\n                        // Use API status if available, fallback to user status\n                        status: apiDomain.currentstatus || userDomain.status || \"unknown\",\n                        registrationDate: formatDate(apiDomain.creationtime) || userDomain.registrationDate || \"Unknown\",\n                        expiryDate: formatDate(apiDomain.endtime) || userDomain.expiryDate || \"Unknown\",\n                        autoRenew: apiDomain.recurring === \"true\" || userDomain.autoRenew || false,\n                        registrar: userDomain.registrar || \"ZTech Domains\",\n                        nameservers: nameservers.length > 0 ? nameservers : userDomain.nameservers || [\n                            dt(\"no_nameservers_configured\")\n                        ],\n                        privacyProtection: apiDomain.isprivacyprotected === \"true\" || userDomain.privacyProtection || false,\n                        period: userDomain.period || 1,\n                        price: userDomain.price || \"N/A\",\n                        orderId: apiDomain.orderid || userDomain.orderId || \"N/A\",\n                        orderStatus: apiDomain.orderstatus || userDomain.orderStatus || \"unknown\",\n                        // Additional API details\n                        productCategory: apiDomain.productcategory || \"Domain\",\n                        productKey: apiDomain.productkey || \"N/A\",\n                        customerId: apiDomain.customerid || \"N/A\",\n                        domainSecret: apiDomain.domsecret || \"N/A\",\n                        raaVerificationStatus: apiDomain.raaVerificationStatus || \"Unknown\",\n                        gdprProtection: apiDomain.gdpr || {\n                            enabled: \"false\",\n                            eligible: \"false\"\n                        },\n                        privacyProtectionExpiry: apiDomain.privacyprotectendtime ? formatDate(apiDomain.privacyprotectendtime) : \"N/A\",\n                        isOrderSuspendedUponExpiry: apiDomain.isOrderSuspendedUponExpiry === \"true\",\n                        orderSuspendedByParent: apiDomain.orderSuspendedByParent === \"true\",\n                        allowDeletion: apiDomain.allowdeletion === \"true\",\n                        contacts,\n                        // DNS records placeholder (would need separate API call)\n                        dnsRecords: [\n                            {\n                                id: \"rec1\",\n                                type: \"A\",\n                                name: \"@\",\n                                content: dt(\"use_dns_management_to_view_edit_records\"),\n                                ttl: 3600\n                            }\n                        ]\n                    };\n                    setDomain(combinedDomain);\n                } catch (apiError) {\n                    console.warn(\"Could not fetch domain details from API:\", apiError);\n                    setApiError(dt(\"could_not_fetch_complete_domain_details\"));\n                    // Fallback to user domain data only\n                    const fallbackDomain = {\n                        id: userDomain.id,\n                        name: userDomain.name,\n                        status: userDomain.status,\n                        registrationDate: userDomain.registrationDate,\n                        expiryDate: userDomain.expiryDate,\n                        autoRenew: userDomain.autoRenew,\n                        registrar: userDomain.registrar || \"ZTech Domains\",\n                        nameservers: userDomain.nameservers || [\n                            \"moha1280036.earth.orderbox-dns.com\",\n                            \"moha1280036.mars.orderbox-dns.com\",\n                            \"moha1280036.mercury.orderbox-dns.com\",\n                            \"moha1280036.venus.orderbox-dns.com\"\n                        ],\n                        privacyProtection: userDomain.privacyProtection,\n                        period: userDomain.period,\n                        price: userDomain.price,\n                        orderId: userDomain.orderId,\n                        orderStatus: userDomain.orderStatus,\n                        contacts: {\n                            registrant: {\n                                name: dt(\"contact_information_not_available\"),\n                                email: dt(\"contact_information_not_available\"),\n                                phone: dt(\"contact_information_not_available\"),\n                                address: dt(\"contact_information_not_available\")\n                            },\n                            admin: {\n                                name: dt(\"contact_information_not_available\"),\n                                email: dt(\"contact_information_not_available\"),\n                                phone: dt(\"contact_information_not_available\"),\n                                address: dt(\"contact_information_not_available\")\n                            },\n                            tech: {\n                                name: dt(\"contact_information_not_available\"),\n                                email: dt(\"contact_information_not_available\"),\n                                phone: dt(\"contact_information_not_available\"),\n                                address: dt(\"contact_information_not_available\")\n                            },\n                            billing: {\n                                name: dt(\"contact_information_not_available\"),\n                                email: dt(\"contact_information_not_available\"),\n                                phone: dt(\"contact_information_not_available\"),\n                                address: dt(\"contact_information_not_available\")\n                            }\n                        },\n                        dnsRecords: [\n                            {\n                                id: \"rec1\",\n                                type: \"A\",\n                                name: \"@\",\n                                content: dt(\"dns_information_not_available\"),\n                                ttl: 3600\n                            }\n                        ]\n                    };\n                    setDomain(fallbackDomain);\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error getting domain details\", error);\n                setLoading(false);\n            }\n        };\n        getDomainDetails();\n    }, [\n        id\n    ]);\n    const handleAutoRenewToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.toggleAutoRenewal(id, value);\n            setDomain({\n                ...domain,\n                autoRenew: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling auto renewal\", error);\n        }\n    };\n    const handlePrivacyToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.togglePrivacyProtection(id, value);\n            setDomain({\n                ...domain,\n                privacyProtection: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling privacy protection\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-6 w-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-gray-600\",\n                        children: [\n                            t(\"loading\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 253,\n            columnNumber: 7\n        }, this);\n    }\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                    variant: \"h4\",\n                    className: \"text-gray-800 font-bold mb-2\",\n                    children: t(\"domain_not_found\", {\n                        defaultValue: \"Domain Not Found\"\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    className: \"mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 268,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"text\",\n                    className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this),\n                apiError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5 text-yellow-400\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    className: \"text-sm text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: [\n                                                dt(\"warning\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" \",\n                                        apiError,\n                                        \". \",\n                                        dt(\"some_information_may_be_limited\"),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                lineNumber: 303,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 297,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 296,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                            variant: \"h1\",\n                                            className: \"text-2xl font-bold text-gray-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize mr-2 \".concat(domain.status === \"active\" ? \"bg-green-100 text-green-800\" : domain.status === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : domain.status === \"expired\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: dt(domain.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        dt(\"registrar\"),\n                                                        \": \",\n                                                        domain.registrar\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outlined\",\n                                    className: \"border-blue-600 text-blue-600 hover:bg-blue-50 flex items-center gap-2\",\n                                    onClick: ()=>window.open(\"http://\".concat(domain.name), \"_blank\"),\n                                    children: [\n                                        dt(\"visit_website\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                    onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/renew\")),\n                                    children: [\n                                        dt(\"renew_domain\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                    value: activeTab,\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsHeader, {\n                            className: \"bg-gray-100 rounded-lg p-1\",\n                            indicatorProps: {\n                                className: \"bg-white shadow-md rounded-md\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"overview\",\n                                    onClick: ()=>setActiveTab(\"overview\"),\n                                    className: activeTab === \"overview\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"overview\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"dns\",\n                                    onClick: ()=>setActiveTab(\"dns\"),\n                                    className: activeTab === \"dns\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"dns_settings\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"contacts\",\n                                    onClick: ()=>setActiveTab(\"contacts\"),\n                                    className: activeTab === \"contacts\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"domain_contacts\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"privacy\",\n                                    onClick: ()=>setActiveTab(\"privacy\"),\n                                    className: activeTab === \"privacy\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"privacy\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsBody, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"overview\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"domain_details\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"domain_name\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 408,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"status\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize \".concat(domain.status === \"active\" ? \"bg-green-100 text-green-800\" : domain.status === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : domain.status === \"expired\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                            children: dt(domain.status)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 419,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"registration_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 433,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: new Date(domain.registrationDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"expiry_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 443,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: new Date(domain.expiryDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"auto_renew\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 451,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                            checked: domain.autoRenew,\n                                                                            onChange: (e)=>handleAutoRenewToggle(e.target.checked),\n                                                                            color: \"blue\",\n                                                                            disabled: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"whois_privacy\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 464,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                            checked: domain.privacyProtection,\n                                                                            onChange: (e)=>handlePrivacyToggle(e.target.checked),\n                                                                            color: \"blue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 467,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                domain.orderId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"order_id\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-xs\",\n                                                                            children: domain.orderId\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.customerId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"customer_id\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 487,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-xs\",\n                                                                            children: domain.customerId\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.raaVerificationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"raa_verification\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(domain.raaVerificationStatus === \"Verified\" ? \"bg-green-100 text-green-800\" : domain.raaVerificationStatus === \"Pending\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                            children: domain.raaVerificationStatus\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 500,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.gdprProtection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"gdpr_protection\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 514,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(domain.gdprProtection.enabled === \"true\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                            children: domain.gdprProtection.enabled === \"true\" ? dt(\"enabled\") : dt(\"disabled\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"nameservers\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                domain.nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"NS \",\n                                                                                    index + 1\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 542,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"font-medium\",\n                                                                                children: ns\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 545,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 538,\n                                                                        columnNumber: 25\n                                                                    }, this)),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outlined\",\n                                                                        className: \"w-full border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                                                        onClick: ()=>setActiveTab(\"dns\"),\n                                                                        children: dt(\"update_nameservers\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"dns\",\n                                    className: \"p-0 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                domain: domain,\n                                                onUpdate: (updatedDomain)=>setDomain(updatedDomain)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: dt(\"manage_dns_records\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                                onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/add\")),\n                                                                children: t(\"add_record\", {\n                                                                    defaultValue: \"Add Record\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"bg-gray-50 border-b border-gray-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"type\", {\n                                                                                    defaultValue: \"Type\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 592,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"name\", {\n                                                                                    defaultValue: \"Name\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 595,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"content\", {\n                                                                                    defaultValue: \"Content\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 598,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"ttl\", {\n                                                                                    defaultValue: \"TTL\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 601,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-500\",\n                                                                                children: t(\"actions\", {\n                                                                                    defaultValue: \"Actions\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 604,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"divide-y divide-gray-200\",\n                                                                    children: domain.dnsRecords.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            className: \"hover:bg-gray-50\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm font-medium text-gray-900\",\n                                                                                    children: record.type\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 612,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 615,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.content\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 618,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.ttl\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 621,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-right\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        size: \"sm\",\n                                                                                        variant: \"text\",\n                                                                                        className: \"text-blue-600\",\n                                                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/\").concat(record.id)),\n                                                                                        children: t(\"edit\", {\n                                                                                            defaultValue: \"Edit\"\n                                                                                        })\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 625,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 624,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, record.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"contacts\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: dt(\"domain_contacts\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"registrant\", {\n                                                                        defaultValue: \"Registrant Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.registrant.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 662,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.registrant.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 665,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.registrant.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 668,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.registrant.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 671,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"admin\", {\n                                                                        defaultValue: \"Administrative Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.admin.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 681,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.admin.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.admin.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 687,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.admin.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"technical\", {\n                                                                        defaultValue: \"Technical Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 696,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.tech.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 700,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.tech.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 703,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.tech.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 706,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.tech.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 709,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"billing\", {\n                                                                        defaultValue: \"Billing Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 715,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.billing.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 719,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.billing.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 722,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.billing.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 725,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.billing.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 728,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 718,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/contacts\")),\n                                                        children: dt(\"update_contacts\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"privacy\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: t(\"privacy\", {\n                                                        defaultValue: \"Privacy Protection\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 756,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: t(\"privacy_content_coming_soon\", {\n                                                                defaultValue: \"Privacy protection settings will be available soon.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: t(\"privacy_description\", {\n                                                                defaultValue: \"Manage your domain privacy protection and WHOIS information visibility.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 763,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 748,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 362,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 285,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n        lineNumber: 284,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainDetailPage, \"+EnspOi5i8G7ORbhH3TPg4wkUtU=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DomainDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DomainDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx\n"));

/***/ })

});