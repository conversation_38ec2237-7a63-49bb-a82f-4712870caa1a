# Contact API Fixes

## Issues Identified

Based on the error log showing:
```
<PERSON>rror adding contact: { status: 'ERRO<PERSON>', message: 'HTTP 404 Not Found' }
Full API URL: https://domain-name-api.dynv6.net/test/api/contacts/add.json?...
```

Two main issues were identified:

### 1. Incorrect API Endpoint
- **Problem**: Using `/api/contacts/add.json` endpoint
- **Solution**: Changed to `/contacts/add.json` (removed `/api` prefix)
- **Reason**: The domain registration API uses `/domains/register.json` without `/api` prefix, indicating the contact endpoints should follow the same pattern

### 2. Invalid Country Code Generation
- **Problem**: Country code "RR" was being generated for invalid country inputs
- **Solution**: Improved country validation to return empty string for invalid inputs
- **Reason**: The fallback logic was taking first 2 characters of invalid country names, creating invalid ISO codes

## Files Modified

### 1. `backend/services/contactService.js`

#### API Endpoint Fixes:
```javascript
// Before (404 error)
`${API_BASE_URL}/api/contacts/add.json`
`${API_BASE_URL}/api/contacts/modify.json`
`${API_BASE_URL}/api/contacts/delete.json`

// After (correct endpoints)
`${API_BASE_URL}/contacts/add.json`
`${API_BASE_URL}/contacts/modify.json`
`${API_BASE_URL}/contacts/delete.json`
```

#### Customer Creation Endpoint Priority:
```javascript
// Now tries correct endpoint first
`${API_BASE_URL}/customers/add.json` // Primary
`${API_BASE_URL}/api/customers/add.json` // Fallback
```

### 2. `backend/controllers/dn-management/userContactController.js`

#### Country Code Validation Fix:
```javascript
// Before (could generate "RR")
return (
  countryMappings[normalizedInput] ||
  countryInput.toUpperCase().substring(0, 2)
);

// After (prevents invalid codes)
const mappedCountry = countryMappings[normalizedInput];

if (mappedCountry) {
  return mappedCountry;
}

// Return empty string for invalid inputs (triggers validation error)
console.warn(`Invalid country input: "${countryInput}"`);
return "";
```

## API Endpoint Structure

Based on working domain registration endpoints, the correct structure is:

### Working Endpoints:
- Domain Registration: `${API_BASE_URL}/domains/register.json`
- Domain Check: `${API_BASE_URL}/domaincheck/available.json`

### Fixed Contact Endpoints:
- Contact Add: `${API_BASE_URL}/contacts/add.json`
- Contact Modify: `${API_BASE_URL}/contacts/modify.json`
- Contact Delete: `${API_BASE_URL}/contacts/delete.json`

### Customer Endpoints:
- Customer Add: `${API_BASE_URL}/customers/add.json` (primary)
- Customer Add: `${API_BASE_URL}/api/customers/add.json` (fallback)

## Country Code Validation

### Supported Country Mappings:
- `morocco`, `maroc` → `MA`
- `france` → `FR`
- `united states`, `usa` → `US`
- `canada` → `CA`
- `united kingdom`, `uk` → `GB`
- And many more...

### Validation Behavior:
1. **Valid 2-letter ISO codes**: Returned as-is (uppercase)
2. **Known country names**: Mapped to correct ISO codes
3. **Invalid inputs**: Return empty string (triggers validation error)

## Testing

Created test script: `backend/scripts/test-contact-api-fix.js`

### Test Cases:
1. **Country Code Mapping**: Tests various country name formats
2. **Contact Creation**: Tests with fixed API endpoints
3. **Validation**: Tests error handling for invalid inputs

### Run Tests:
```bash
cd backend
node scripts/test-contact-api-fix.js
```

## Expected Results

### Before Fixes:
- ❌ 404 errors on contact creation
- ❌ Invalid country codes like "RR"
- ❌ Contact creation failures

### After Fixes:
- ✅ Successful contact creation with valid contact IDs
- ✅ Proper country code validation
- ✅ Clear error messages for invalid inputs

## Environment Configuration

Ensure these environment variables are set:
```env
# API Configuration
API_BASE_URL_TEST=https://domain-name-api.dynv6.net/test
AUTH_USERID_TEST=1280036
API_KEY_TEST=WKFl46hPtgrBCuArAv33YRXVixlEI1dM

# Company Customer ID
COMPANY_CUSTOMER_ID=31174676
```

## Next Steps

1. **Test the fixes**: Run the test script to verify contact creation works
2. **Update frontend**: Ensure frontend sends valid country data
3. **Monitor logs**: Check for successful contact creation in application logs
4. **Domain registration**: Test end-to-end domain registration flow

## Error Handling

The system now provides better error messages:

### Country Validation:
```javascript
// Invalid country input triggers clear error
"Valid country code is required (e.g., US, CA, FR, MA)"
```

### API Failures:
```javascript
// Contact creation failures return proper errors
{
  "success": false,
  "error": "Failed to create contact in reseller API",
  "details": "API Error details",
  "message": "Contact creation failed. Please check your contact details and try again."
}
```

## Conclusion

These fixes address the core issues preventing contact creation:
1. **Correct API endpoints** ensure requests reach the right service
2. **Improved country validation** prevents invalid ISO codes
3. **Better error handling** provides clear feedback to users

The contact management system should now work correctly with the reseller API.
