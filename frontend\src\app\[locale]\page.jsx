"use client";


import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import HomeBanner from "@/components/home/<USER>";

const HPackagesPlanGrid = dynamic(
  () => import("../../components/hosting/hPackagesPlanGrid"),
  {
    ssr: false,
  }
);
const Services = dynamic(() => import("../../components/home/<USER>"), {
  ssr: false,
});
const OtherServices = dynamic(
  () => import("../../components/home/<USER>"),
  { ssr: false }
);
// const AboutUs = dynamic(() => import("../../components/home/<USER>"), {
//   ssr: false,
// });
// const WhyChooseUs = dynamic(() => import("../../components/home/<USER>"), {
//   ssr: false,
// });
const ContactForm2 = dynamic(
  () => import("../../components/shared/contactForm2"),
  { ssr: false }
);

const FAQ2 = dynamic(() => import("../../components/shared/faq2"), {
  ssr: false,
});

const TestimonialsSection = dynamic(
  () => import("../../components/home/<USER>"),
  {
    ssr: false,
  }
);

const CloudMaroc = dynamic(() => import("../../components/home/<USER>"), {
  ssr: false,
});
// const WhoTrustUs = dynamic(() => import("../../components/home/<USER>"), {
//   ssr: false,
// });
const CompanyIntro = dynamic(() => import("../../components/home/<USER>"), {
  ssr: false,
});

export default function Home() {
  const [scrollY, setScrollY] = useState(0);
  const [visibleSections, setVisibleSections] = useState({
    HPackagesPlanGrid: true,
    services: true,
    otherServices: true,
    chat: false,
    testimonials: false,
    cloud: false,
    companyIntro: true,
    trust: false,
    about: false,
    whyChoose: false,
    cloudAdvantages: false,
    contact: false,
    faq: false,
  });

  const t = useTranslations("Home");

  // Track scroll position and determine when to load components
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);

      const checkVisibility = (id) => {
        const element = document.getElementById(id);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= window.innerHeight && rect.bottom >= 0;
        }
        return false;
      };

      setVisibleSections((prev) => ({
        HPackagesPlanGrid:
          prev.HPackagesPlanGrid || checkVisibility("HPackages-planGrid"),
        services: prev.services || checkVisibility("services-section"),
        otherServices:
          prev.otherServices || checkVisibility("other-services-section"),
        chat: prev.chat || checkVisibility("chat-section"),
        testimonials:
          prev.testimonials || checkVisibility("testimonials-section"),
        cloud: prev.cloud || checkVisibility("cloud-section"),
        companyIntro:
          prev.companyIntro || checkVisibility("company-intro-section"),
        trust: prev.trust || checkVisibility("trust-section"),
        about: prev.about || checkVisibility("about-section"),
        whyChoose: prev.whyChoose || checkVisibility("why-choose-section"),
        cloudAdvantages:
          prev.cloudAdvantages || checkVisibility("cloud-advantages-section"),
        contact: prev.contact || checkVisibility("contact-section"),
        faq: prev.faq || checkVisibility("faq-section"),
      }));
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const [data, setData] = useState({
    page: "Home",
    offerName: "No offer selected",
    id: 0,
    url: "/",
  });

  return (
    <div className="w-full min-h-screen p-0 m-0 font-inter mb-0 bg-gradient-to-b from-white to-transparent ">
      {/* Banner Section */}
      <HomeBanner t={t} />
      <div className="max-w-6xl mx-auto mt-2" id="HPackages-planGrid">
        {visibleSections.HPackagesPlanGrid && (
          <HPackagesPlanGrid brandName={"shared hosting"} />
        )}
      </div>

      {/* Services Section */}
      <div id="services-section">
        {visibleSections.services && <Services t={t} />}
      </div>

      {/* Other Services Section */}
      <div id="other-services-section">
        {visibleSections.otherServices && <OtherServices t={t} />}
      </div>

      {/* Testimonials Section */}
      <div id="testimonials-section">
        {/* {visibleSections.testimonials && <Testimonials />} */}
        {visibleSections.testimonials && <TestimonialsSection />}
      </div>

      {/* CloudMaroc Section */}
      <div id="cloud-section">
        {visibleSections.cloud && <CloudMaroc t={t} />}
      </div>

      {/* Company Intro Section */}
      <div id="company-intro-section">
        {visibleSections.companyIntro && <CompanyIntro t={t} />}
      </div>

      {/* Contact Form Section */}
      <div id="contact-section">
        {visibleSections.contact && (
          <ContactForm2 data={data} setData={setData} />
        )}
      </div>

      {/* FAQ Section */}
      <div id="faq-section">{visibleSections.faq && <FAQ2 t={t} />}</div>
    </div>
  );
}
