{"name": "ztech-express", "version": "1.0.0", "description": "", "main": "server", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon server", "start": "node server", "deploy": "pm2 start npm --name ztech-backend -- start -- --port=5002"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "axios": "^1.8.1", "body-parser": "^1.20.2", "bull": "^4.12.9", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.7", "express": "^4.18.2", "express-fileupload": "^1.4.0", "express-form-data": "^2.0.22", "express-formidable": "^1.2.0", "express-jwt": "^6.1.2", "express-recaptcha": "^5.1.0", "express-validator": "^5.3.1", "firebase-admin": "^11.10.1", "google-auth-library": "^9.7.0", "googleapis": "^149.0.0", "i18next": "^23.6.0", "i18next-fs-backend": "^2.2.0", "i18next-http-middleware": "^3.4.1", "joi": "^17.12.2", "jsonwebtoken": "^9.0.1", "mongoose": "^7.6.10", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "node-schedule": "^2.1.1", "nodemailer": "^6.9.4", "nodemailer-sendinblue-transport": "^2.0.1", "nodemon": "^3.1.0", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "serve-static": "^1.15.0", "socket.io": "^4.7.2", "swiper": "^10.1.0", "uuid": "^9.0.0", "winston": "^3.13.1", "xss": "^1.0.15", "ztech-express": "file:"}, "devDependencies": {"daisyui": "^3.5.1"}}