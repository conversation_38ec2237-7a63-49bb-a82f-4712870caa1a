const Joi = require('joi');

/**
 * Validation schema for general settings
 */
const generalSettingsSchema = Joi.object({
  siteName: Joi.string()
    .min(1)
    .max(100)
    .required()
    .label('Site Name'),
  
  siteDescription: Joi.string()
    .max(500)
    .allow('', null)
    .optional()
    .label('Site Description'),
  
  contactEmail: Joi.string()
    .email()
    .required()
    .label('Contact Email'),
  
  phone: Joi.string()
    .allow('', null)
    .max(20)
    .optional()
    .label('Phone'),

  address: Joi.string()
    .allow('', null)
    .max(500)
    .optional()
    .label('Address'),
  
  socialLinks: Joi.object({
    linkedin: Joi.string()
      .uri({ scheme: ['http', 'https'] })
      .allow('', null)
      .optional()
      .label('LinkedIn URL'),

    insatgram: Joi.string()
      .uri({ scheme: ['http', 'https'] })
      .allow('', null)
      .optional()
      .label('Insatgram URL'),

    facebook: Joi.string()
      .uri({ scheme: ['http', 'https'] })
      .allow('', null)
      .optional()
      .label('Facebook URL')
  }).optional().label('Social Links')
});

/**
 * Validation schema for SEO settings
 */
const seoSettingsSchema = Joi.object({
  defaultTitle: Joi.string()
    .min(1)
    .max(60)
    .required()
    .label('Default Title'),
  
  defaultDescription: Joi.string()
    .min(1)
    .max(160)
    .required()
    .label('Default Description'),
  
  defaultKeywords: Joi.string()
    .allow('', null)
    .max(500)
    .optional()
    .label('Default Keywords'),

  favicon: Joi.string()
    .uri({ scheme: ['http', 'https'] })
    .allow('', null)
    .optional()
    .label('Favicon URL'),

  googleTagManagerId: Joi.string()
    .allow('', null)
    .pattern(/^(GTM-[A-Z0-9]+|UA-[0-9]+-[0-9]+|)?$/)
    .optional()
    .label('Google Analytics ID'),

  googleSiteVerification: Joi.string()
    .allow('', null)
    .max(100)
    .optional()
    .label('Google Site Verification'),

  bingVerification: Joi.string()
    .allow('', null)
    .max(100)
    .optional()
    .label('Bing Verification'),

  robotsTxt: Joi.string()
    .allow('', null)
    .max(2000)
    .optional()
    .label('Robots.txt Content'),

  sitemapEnabled: Joi.boolean()
    .optional()
    .label('Sitemap Enabled')
});

/**
 * Validation schema for complete site settings
 */
const siteSettingsSchema = Joi.object({
  general: generalSettingsSchema.optional(),
  seo: seoSettingsSchema.optional()
}).min(1); // At least one section must be provided

/**
 * Middleware to validate site settings update request
 */
exports.validateSiteSettings = async (req, res, next) => {
  try {
    const { error, value } = siteSettingsSchema.validate(req.body, {
      abortEarly: false,
      allowUnknown: true,
      stripUnknown: false
    });

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors
      });
    }

    req.body = value;
    next();
  } catch (err) {
    console.error('Validation error:', err);
    return res.status(500).json({
      success: false,
      message: 'Internal validation error'
    });
  }
};

/**
 * Middleware to validate general settings section update
 */
exports.validateGeneralSettings = async (req, res, next) => {
  try {
    const { error, value } = generalSettingsSchema.validate(req.body, {
      abortEarly: false,
      allowUnknown: true,
      stripUnknown: false
    });

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors
      });
    }

    req.body = value;
    next();
  } catch (err) {
    console.error('Validation error:', err);
    return res.status(500).json({
      success: false,
      message: 'Internal validation error'
    });
  }
};

/**
 * Middleware to validate SEO settings section update
 */
exports.validateSeoSettings = async (req, res, next) => {
  try {
    const { error, value } = seoSettingsSchema.validate(req.body, {
      abortEarly: false,
      allowUnknown: true,
      stripUnknown: false
    });

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors
      });
    }

    req.body = value;
    next();
  } catch (err) {
    console.error('Validation error:', err);
    return res.status(500).json({
      success: false,
      message: 'Internal validation error'
    });
  }
};
