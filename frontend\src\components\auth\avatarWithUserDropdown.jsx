"use client";
import React, { useEffect, useRef, useState } from "react";
import { Button } from "@material-tailwind/react";
import { UserCircleIcon, TrashIcon } from "@heroicons/react/24/solid";
import { usePathname, useRouter } from "next/navigation";
import { useAuth } from "../../app/context/AuthContext";
import cartService from "../../app/services/cartService";
import { AccountState } from "../../app/config/AccountState";
import { useTranslations } from "next-intl";
import {
  LayoutIcon,
  ServerIcon,
  ShieldIcon,
  ShoppingCart,
  SquareChevronRight,
} from "lucide-react";
import { AvatarIcon } from "../avatar/AvatarIcon";
import UserNotifications from "../user/UserNotifications";
import CartItemDisplay from "../cart/CartItemDisplay";

// Mapping of category names to their corresponding icon components.
const categoryIcons = {
  Hosting: ServerIcon,
  SSL: ShieldIcon,
  Promotions: LayoutIcon,
};

// A small wrapper for the icon container.
const IconWrapper = ({ children }) => (
  <div className="h-12 w-12 flex-shrink-0 bg-blue-100 rounded-lg flex items-center justify-center">
    {children}
  </div>
);

export function AvatarWithUserDropdown() {
  const t = useTranslations("client");
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isCartMenuOpen, setIsCartMenuOpen] = useState(false);
  const { user, logout, cartCount, setCartCount } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isClient, setIsClient] = useState(false);
  const [cartData, setCartData] = useState({});
  const [imgError, setImgError] = useState(false);

  const cartRef = useRef(null);

  useEffect(() => {
    setIsClient(true);

    const fetchCartData = async () => {
      try {
        const cartData = await cartService.getCart();
        if (cartData.data.success) {
          setCartCount(cartData.data.cart.cartCount);
          setCartData(cartData.data.cart);
          console.log("pathname: ", pathname);
          if (
            !pathname.includes("/client/") &&
            !pathname.includes("/auth/") &&
            cartCount > 0
          ) {
            setIsCartMenuOpen(true);
          }
          // console.log('cartData.data.cart.items   = ', cartData.data.cart);
        } else {
          setCartCount(0);
        }
      } catch (error) {
        console.error("Error fetching cart data", error);
      }
    };

    fetchCartData();
  }, [cartCount]);

  // Close the dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (cartRef.current && !cartRef.current.contains(event.target)) {
        setIsCartMenuOpen(false);
      }
    }

    // Add event listener when cart is open
    if (isCartMenuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isCartMenuOpen]);

  const handleLogout = async () => {
    await logout();
    closeMenu();
    window.location.href = "/auth/login";
  };

  const navigate = (toPage) => {
    router.push("/auth/" + toPage);
  };

  const handleQuantityChange = async (itemId, change, quantity) => {
    // Find the item safely (only for package items)
    const foundItem = cartData?.items?.find(
      (item) => item.package && item.package._id === itemId
    );
    let newQuantity = foundItem?.quantity + change;

    if (quantity == "delete") {
      quantity = 1;
      newQuantity = 5;
    }

    if (newQuantity < 1 || newQuantity > 10) {
      return;
    }

    try {
      let res;
      if (change > 0) {
        res = await cartService.addItemToCart({ packageId: itemId, quantity });
      } else if (change < 0) {
        res = await cartService.removeItemFromCart({
          packageId: itemId,
          quantity,
        });
      } else {
        return;
      }

      setCartCount(res.data.cartCount);
      setCartData(res.data);
    } catch (error) {
      console.error("Error updating cart", error);
    }
  };

  const closeMenu = () => setIsMenuOpen(false);

  if (!isClient) {
    return null;
  }

  return (
    <div
      ref={cartRef}
      className="flex space-x-6 justify-center h-full items-center p-0 flex-shrink-0 max-w-[140px] min-w-[110px]"
    >
      {/* User Notifications */}
      {user?.state !== AccountState.GUEST && <UserNotifications />}
      <div
        className="relative font-poppins"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Cart Button */}
        <button
          name="Cart"
          className="relative flex items-center rounded-full p-0"
          onClick={() => setIsCartMenuOpen(!isCartMenuOpen)}
        >
          <div className="absolute -top-2 -right-3">
            <span className="bg-red-500 text-white text-xs font-bold px-1 rounded-full">
              {cartCount || 0}
            </span>
          </div>
          <ShoppingCart className="text-secondary w-6 h-6" />
        </button>

        {/* Cart Dropdown */}
        {isCartMenuOpen && (
          <div
            className="absolute sm:-right-9 -right-[80px] xlg:top-[36px] lg:top-[37px] top-[45px] p-4 w-[350px] sm:w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="font-semibold px-4 py-2 border-b border-gray-300 text-gray-800">
              <p className="font-poppins text-lg">{t("cart")}</p>
            </div>

            {/* Scrollable Items Section */}
            <div className="max-h-64 overflow-y-auto">
              {cartCount === 0 ? (
                <div className="flex items-center justify-center h-40">
                  <p className="text-center text-gray-500">
                    {t("no_items_in_cart")}
                  </p>
                </div>
              ) : (
                cartData?.items?.map((item) => (
                  <CartItemDisplay
                    key={item._id}
                    item={item}
                    imgError={imgError}
                    handleQuantityChange={handleQuantityChange}
                    t={t}
                  />
                ))
              )}
            </div>

            {/* Footer */}
            {cartCount !== 0 && (
              <div className="relative flex justify-between text-sm mt-5">
                <span className="text-gray-600">{t("total")}</span>
                <span className="font-medium">
                  <span className="text-[12px]">MAD</span>{" "}
                  {(isNaN(cartData?.totalPrice)
                    ? 0
                    : cartData?.totalPrice
                  ).toFixed(2)}{" "}
                  <small className="font-medium">(HT)</small>
                </span>
                {cartData?.totalDiscount > 0 && (
                  <div className="absolute -right-[1px] -top-[18px] ">
                    <small className="font-medium pl-0 text-gray-800">
                      <del>
                        <span className="text-[12px]">MAD</span>{" "}
                        {(isNaN(cartData?.totalPrice + cartData?.totalDiscount)
                          ? 0
                          : cartData?.totalPrice + cartData?.totalDiscount
                        ).toFixed(2)}
                      </del>
                    </small>
                    <small className="ml-2 font-medium pl-0 text-green-800">
                      (-
                      {isNaN(
                        (cartData?.totalDiscount /
                          (cartData?.totalPrice + cartData?.totalDiscount)) *
                          100
                      )
                        ? 0
                        : (
                            (cartData?.totalDiscount /
                              (cartData?.totalPrice +
                                cartData?.totalDiscount)) *
                            100
                          ).toFixed(0)}
                      %)
                    </small>
                  </div>
                )}
              </div>
            )}
            <button
              name="Open Menu"
              className="mt-4 bg-blue-600 text-white hover:bg-blue-700 rounded-lg w-full py-2 text-sm font-medium font-poppins uppercase"
              onClick={() => {
                setIsCartMenuOpen(false);
                router.push("/client/cart");
              }}
            >
              {t("go_to_cart")}
            </button>
          </div>
        )}
      </div>

      {/* User Avatar and Dropdown Menu for Logged-in Users */}
      {user?.state !== AccountState.GUEST ? (
        <AvatarIcon />
      ) : (
        <Button
          id="Login"
          name="Login"
          aria-label="Login"
          variant="text"
          color="blue-gray"
          className="flex items-center justify-center rounded-full p-0 m-1 hover:border-secondary border-2 border-transparent flex-shrink-0 h-fit my-auto"
          onClick={() => navigate("login")}
        >
          <UserCircleIcon className="w-8 h-8" />
        </Button>
      )}
    </div>
  );
}
