"use client";
import React, { useState} from "react";
import { useTranslations } from "next-intl";
import ManagedServicesIntro from "@/components/managed-services/managedServicesIntro";
import ManagedServicesMain from "@/components/managed-services/managedServicesMain";
import ManagedServicesDetails from "@/components/managed-services/ManagedServicesDetails";
import ManagedServicesReasons from "@/components/managed-services/ManagedServicesReasons";


// Lazy load the contact form
const ContactForm2 = React.lazy(() => import("@/components/shared/contactForm2"));

// Animation variants
const animations = {
  fadeInLeft: {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  },
  fadeInRight: {
    hidden: { opacity: 0, x: 50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  },
  fadeInUp: {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  },
};

// Constants
const DEFAULT_DATA = Object.freeze({
  page: "Infogerance",
  offerName: "No offer selected",
  id: 0,
});





function ManagedServices() {
  const t = useTranslations("infogerance");
  const [data, setData] = useState(DEFAULT_DATA);

  return (
    <main className="min-h-screen w-full">
      <ManagedServicesIntro t={t} animations={animations} />

      <ManagedServicesMain t={t} animations={animations} />

      <ManagedServicesDetails t={t} animations={animations} />

      <ManagedServicesReasons t={t} animations={animations} />

      <ContactForm2 data={data} setData={setData} t={t} />
    </main>
  );
}

export default React.memo(ManagedServices);