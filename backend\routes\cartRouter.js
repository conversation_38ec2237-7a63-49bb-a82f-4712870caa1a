const express = require("express");
const {
  addItemToCart,
  removeItemFromCart,
  clearCart,
  getCart,
  updateItemPeriod,
  removeDomainFromCart,
  updateDomainPeriod,
  updateDomainOptions,
} = require("../controllers/cartControllers");
const {
  fetchUserMiddleware,
  checkUserOrRefreshToken,
} = require("../midelwares/authorization");
const { asyncBackFrontEndLang } = require("../midelwares/sharedMidd");

const cartRouter = express.Router();

// Route to get the user's cart
cartRouter.get("/get-cart", checkUserOrRefreshToken, getCart);

// Route to add an item to the cart
cartRouter.post(
  "/add-item",
  checkUserOrRefreshToken,
  asyncBackFrontEndLang,
  addItemToCart
);

// Route to remove an item from the cart
cartRouter.post("/remove-item", checkUserOrRefreshToken, removeItemFromCart);

// Route to clear the cart
cartRouter.post("/clear", clearCart);

// Route to update an item's period in the cart
cartRouter.put(
  "/update-item-period",
  checkUserOrRefreshToken,
  updateItemPeriod
);

// Route to remove domain from cart
cartRouter.post(
  "/remove-domain",
  checkUserOrRefreshToken,
  removeDomainFromCart
);

// Route to update domain period in cart
cartRouter.post(
  "/update-domain-period",
  checkUserOrRefreshToken,
  updateDomainPeriod
);

// Route to update domain options in cart
cartRouter.post(
  "/update-domain-options",
  checkUserOrRefreshToken,
  updateDomainOptions
);

module.exports = cartRouter;
