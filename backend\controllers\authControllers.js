const AccountRole = require("../constants/enums/account-role");
const AccountState = require("../constants/enums/account-state");
const { AccountNotVerifiedCode, isProd } = require("../constants/constant");
const {
  sendVerificationEmail,
  sendForgotPassword,
} = require("../routes/sendEmail/sendEmail");
const User = require("../models/User");
const jwt = require("jsonwebtoken");
const { v4: uuidv4 } = require("uuid");
const crypto = require("crypto");
const mongoose = require("mongoose");
const i18next = require("i18next");
const { ObjectId } = require("bson");
const Cart = require("../models/Cart");
const { clearGuestToken } = require("../midelwares/sharedMidd");
const { getCookieConfig } = require("../helpers/helpers");
const cartService = require("../services/cartService");

const cryptUniqueString = (uniqueString, userId) => {
  return crypto.createHash("sha256", userId).update(uniqueString).digest("hex");
};

exports.registerController = async (req, res) => {
  try {
    const { firstName, lastName, email, phone, country, address, password } =
      req.body;

    const userId = req.user?._id;

    // Generate random 11-character ID (alphanumeric)
    let randomID = crypto.randomBytes(4).toString("hex");

    // Add first 3 characters of email to the random ID
    randomID = email.substring(0, 3) + randomID;
    console.log("🚀 ~ exports.registerController= ~ randomID:", randomID);

    // Construct user data
    const userData = {
      _id: userId ? userId : new mongoose.Types.ObjectId(),
      identifiant: randomID.toUpperCase(),
      firstName,
      lastName,
      email,
      password,
      billingInfo: {
        BillToName: `${firstName} ${lastName}`,
        email,
        phone: phone || "",
        address: address || "",
        country: country || "",
      },
      role: AccountRole.Customer,
      state: AccountState.NOT_VERIFIED,
    };

    // Create new user
    const user = new User(userData);

    const savedUser = await user.save();

    // Associate user with cart
    const cart = await Cart.findOne({ user: req.user?._id });
    if (cart) {
      cart.user = savedUser._id;
      await cart.save();
      console.log("Cart updated successfully:", cart);
    } else {
      console.log("No cart found for the current user.");
    }

    // Send verification email
    const uniqueString = uuidv4().replace(/-/g, Math.random());
    const hashedUniqueString = cryptUniqueString(uniqueString, savedUser._id);

    await sendVerificationEmail(
      req,
      savedUser.email,
      uniqueString,
      savedUser._id
    );
    await User.findOneAndUpdate(
      { _id: savedUser._id },
      { hashedUniqueStringVerifyEmail: hashedUniqueString },
      { new: true }
    );

    // Send real-time notification for user registration
    if (global.io) {
      setImmediate(() => {
        try {
          const socketService = require('../services/socketService')(global.io);
          socketService.notifyUserRegistration(savedUser);
          console.log('[SOCKET DEBUG] Registration notification sent for user:', savedUser._id);
        } catch (error) {
          console.error('Failed to send registration notification:', error);
        }
      });
    }

    // Remove sensitive data
    savedUser.salt = undefined;
    savedUser.hashed_password = undefined;
    savedUser.hashedUniqueStringVerifyEmail = undefined;

    const token = jwt.sign(
      {
        id: savedUser._id,
        role: savedUser.role,
        firstName: savedUser.firstName,
        photo: savedUser.photo,
      },
      process.env.JWT_SECRET,
      { expiresIn: "1h" }
    );

    res.cookie("token", token, {
      maxAge: 24 * 60 * 60 * 1000, // 1 day
      ...getCookieConfig(isProd),
    });

    const refreshToken = jwt.sign(
      {
        id: savedUser._id,
        role: savedUser.role,
      },
      process.env.JWT_REFRESH_SECRET,
      { expiresIn: "7d" }
    );

    res.cookie("refreshToken", refreshToken, {
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 day
      ...getCookieConfig(isProd),
    });
    console.log("sign up:  guest token before : ", req.cookies);
    clearGuestToken(req, res);
    return res.status(201).send({
      user: savedUser,
      code: AccountNotVerifiedCode,
      message: "Check your inbox for email verification.",
    });
  } catch (err) {
    console.error(err);

    if (err.code === 11000) {
      return res
        .status(400)
        .send({ errors: [{ key: "email", msg: "Email already exists" }] });
    }

    return res.status(500).send({
      errors: [{ key: "server", msg: "An unexpected server error occurred." }],
    });
  }
};

exports.loginController = async (req, res) => {
  const { email, password } = req.body;
  try {
    const user = await User.findOne({ email }).select("+hashed_password");
    if (!user) {
      return res.status(400).json({ message: req.t("login_failed") });
    }

    const isAuthenticated = await user.authenticate(password);
    if (!isAuthenticated) {
      return res.status(400).json({ message: req.t("login_failed") });
    }
    if (user.state === AccountState.DELETED) {
      return res.status(400).json({ message: req.t("account_deleted") });
    }

    // Merge guest cart to user cart
    await cartService.mergeGuestCartToUserCart(req.user?._id, user._id);

    // Generate JWT Token
    const token = jwt.sign(
      {
        id: user._id,
        name: user.firstName,
        email: user.email,
        photo: user.photo,
        role: user.role,
      },
      process.env.JWT_SECRET,
      { expiresIn: "1d" }
    );
    res.cookie("token", token, {
      maxAge: 24 * 60 * 60 * 1000, // 1 day
      ...getCookieConfig(isProd),
    });

    const refreshToken = jwt.sign(
      {
        id: user._id,
        name: user.firstName,
        email: user.email,
        photo: user.photo,
        role: user.role,
      },
      process.env.JWT_REFRESH_SECRET,
      { expiresIn: "7d" }
    );

    res.cookie("refreshToken", refreshToken, {
      expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      ...getCookieConfig(isProd),
    });

    let message = "user email is already verified";

    if (user.state == AccountState.NOT_VERIFIED) {
      console.log("user not verified", user.state);
      try {
        const uniqueString = uuidv4().replace(/-/g, Math.random());
        await sendVerificationEmail(req, user.email, uniqueString, user._id);
        await User.findOneAndUpdate(
          { _id: user._id },
          {
            hashedUniqueStringVerifyEmail: cryptUniqueString(
              uniqueString,
              user._id
            ),
          },
          { new: true }
        );
      } catch (error) {
        console.log("email not sended", error);
      }
      message = req.t("check_your_inbox_email_to_verify_account");
    }

    console.log("Login :  guest token before : ", req.cookies);
    clearGuestToken(req, res);
    res.json({
      message,
      code: AccountNotVerifiedCode,
      // token,
      // refreshToken,
      user,
    });
  } catch (err) {
    console.error(err);
    return res.status(500).json({ message: "Server error" });
  }
};

exports.logout = async (req, res) => {
  // Clear token with same settings as when it was set
  res.clearCookie("token", {
    ...getCookieConfig(isProd),
  });

  // Clear refresh token with same settings
  res.clearCookie("refreshToken", {
    ...getCookieConfig(isProd),
  });

  return res.send({
    message: req.t("logout_success"),
  });
};

exports.checkAuth = async (req, res) => {
  try {
    const { token, refreshToken, guest_token } = req.cookies;
    // const { guestId } = req.params;

    console.log("Checking auth...");
    console.log("guest_token... ", guest_token ? guest_token : "null");

    // If no tokens are present, handle guest user
    if (!token && !refreshToken) {
      console.log("No tokens found. Assigning guest user.");

      // Create a new guest user if no guestId is provided
      if (!guest_token) {
        const newGuestId = new ObjectId().toString();
        const guestToken = jwt.sign(
          {
            id: newGuestId,
            state: AccountState.GUEST,
            role: AccountRole.Guest,
          },
          process.env.JWT_SECRET,
          { expiresIn: "1d" }
        );

        // Set a distinct HTTP-only cookie for guest token
        res.cookie("guest_token", guestToken, {
          expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 7 days
          ...getCookieConfig(isProd),
        });

        return res.status(200).send({
          message: "Guest user created",
          user: {
            _id: newGuestId,
            state: AccountState.GUEST,
            role: AccountRole.Guest,
            authenticated: false,
          },
        });
      }
      const guest_tokenDecoded = jwt.verify(
        guest_token,
        process.env.JWT_SECRET
      );
      console.log("guest_tokenDecoded:  ", guest_tokenDecoded);
      // If a guestId is provided, return the existing guest user
      return res.status(200).send({
        message: "Guest user already exists",
        user: {
          _id: guest_tokenDecoded.id,
          state: guest_tokenDecoded.state,
          authenticated: false,
        },
      });
    }

    // Verify the access token if present
    let decoded;
    if (token) {
      try {
        decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id).select(
          "-hashed_password -salt"
        );
        if (!user) {
          console.log("User not found for valid token.");
          return res
            .status(205)
            .send({ message: "User not found", user: null });
        }
        return res.status(200).send({ user });
      } catch (err) {
        if (err.name !== "TokenExpiredError") {
          console.error("Access token verification failed:", err);
          return res.status(401).send({ message: "Unauthorized", user: null });
        }
      }
    }

    // If access token is expired or missing, check refresh token
    if (refreshToken) {
      try {
        const decodedRefresh = jwt.verify(
          refreshToken,
          process.env.JWT_REFRESH_SECRET
        );
        const user = await User.findById(decodedRefresh.id).select(
          "-hashed_password -salt"
        );
        if (!user) {
          console.log("User not found for valid refresh token.");
          return res
            .status(205)
            .send({ message: "User not found", user: null });
        }

        // Generate new tokens
        const newAccessToken = jwt.sign(
          {
            id: user._id,
            role: user.role,
          },
          process.env.JWT_SECRET,
          { expiresIn: "1h" }
        );
        const newRefreshToken = jwt.sign(
          {
            id: user._id,
            role: user.role,
          },
          process.env.JWT_REFRESH_SECRET,
          { expiresIn: "7d" }
        );

        // Set cookies with new tokens
        res.cookie("token", newAccessToken, {
          expires: new Date(Date.now() + 3600000),
          ...getCookieConfig(isProd),
        });

        res.cookie("refreshToken", newRefreshToken, {
          expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
          ...getCookieConfig(isProd),
        });

        console.log(
          "New tokens generated using refresh token. User authenticated:",
          user
        );
        return res.status(200).send({ user });
      } catch (err) {
        console.error("Refresh token verification failed:", err);
        return res.status(401).send({ message: "Unauthorized", user: null });
      }
    }

    // If no valid tokens exist, fallback to guest user creation
    console.log("No valid tokens. Assigning guest user.");
    if (!guestId) {
      const newGuestId = new ObjectId().toString();
      const guestToken = jwt.sign(
        {
          id: newGuestId,
          role: AccountRole.Guest,
        },
        process.env.JWT_SECRET,
        { expiresIn: "7d" }
      );

      // Set guest token cookie
      res.cookie("guest_token", guestToken, {
        expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        ...getCookieConfig(isProd),
      });

      return res.status(200).send({
        message: "Guest user created",
        user: {
          _id: newGuestId,
          state: AccountState.GUEST,
          role: AccountRole.Guest,
          authenticated: false,
        },
      });
    }

    return res.status(200).send({
      message: "Guest user already exists",
      user: {
        _id: guestId,
        state: AccountState.GUEST,
        role: AccountRole.Guest,
        authenticated: false,
      },
    });
  } catch (err) {
    console.error("Server error:", err);
    return res.status(500).send({ message: "Server error" });
  }
};

exports.refreshToken = async (req, res) => {
  try {
    const refreshToken = req.cookies["refresh_token"];
    console.log("Refreshing token...", refreshToken);

    // Return 401 if refresh token is not provided
    if (!refreshToken) {
      return res.status(401).json({ message: "Refresh token is required" });
      // return
    }

    // Verify the refresh token
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
    const user = await User.findById(decoded._id);

    if (!user) {
      return res.status(401).json({ message: "User not found" });
    }

    // Generate a new access token (using JWT_SECRET)
    const newAccessToken = jwt.sign({ id: user._id }, process.env.JWT_SECRET, {
      expiresIn: "1h",
    });

    // Optionally, generate a new refresh token (if desired, or you can keep the same one)
    const newRefreshToken = jwt.sign(
      { id: user._id },
      process.env.JWT_REFRESH_SECRET,
      { expiresIn: "7d" }
    );

    // Set the new refresh token cookie (set an expiration for the refresh token too)
    res.cookie("refresh_token", newRefreshToken, {
      expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      ...getCookieConfig(isProd),
    });

    // Set the new access token cookie (optional)
    res.cookie("token", newAccessToken, {
      expires: new Date(Date.now() + 3600000), // 1 hour
      ...getCookieConfig(isProd),
    });

    res.json({ token: newAccessToken });
  } catch (err) {
    console.error(err);
    return res.status(401).json({ message: "Invalid refresh token" });
  }
};

exports.verifyEmailAccount = async (req, res) => {
  const activationField = req.query.activationField;
  const userId = req.query.userId;
  const locale = req.query.locale ? req.query.locale : "fr";
  console.log("🚀 ~ exports.verifyEmailAccount= ~ locale:", locale);

  try {
    const user = await User.findOne({ _id: userId }).select(
      "+hashedUniqueStringVerifyEmail"
    );
    if (!user) {
      console.log("user not found, foreign_error");
      return res.status(203).send(req.t("foreign_error"));
    }

    if (
      cryptUniqueString(activationField, userId) ===
      user.hashedUniqueStringVerifyEmail
    ) {
      user.state = AccountState.VERIFIED;
      user.hashedUniqueStringVerifyEmail = undefined;
      await user.save();

      // Send real-time notification for user verification
      if (global.io) {
        setImmediate(() => {
          try {
            const socketService = require('../services/socketService')(global.io);
            socketService.notifyUserVerification(user);
            console.log('[SOCKET DEBUG] Verification notification sent for user:', user._id);
          } catch (error) {
            console.error('Failed to send verification notification:', error);
          }
        });
      }

      console.log("user?emailActivation=success");
      const destinationURL = `${process.env.FRONTEND_URL}/${locale}/auth/verification?emailActivation=success`;
      return res.redirect(destinationURL);
    }
    console.log("user?emailActivation=failed");
    const destinationURL = `${process.env.FRONTEND_URL}/${locale}/auth/verification?emailActivation=failed`;
    return res.redirect(destinationURL);
  } catch (error) {
    console.log("error", error);
    console.log("user?emailActivation=error");
    const destinationURL = `${process.env.FRONTEND_URL}/${locale}/auth/verification?emailActivation=error`;
    return res.redirect(destinationURL);
  }
};

exports.forgotPassword = async (req, res) => {
  const { email } = req.body;
  console.log("email", email);
  const user = await User.findOne({ email });
  if (!user) {
    return res.status(400).json({
      message: req.t("send_code_email_error"),
      errors: [{ key: "email", msg: req.t("send_code_email_error") }],
    });
  }
  try {
    const uniqueString = uuidv4().replace(/-/g, Math.random());
    await User.findOneAndUpdate(
      { _id: user._id }, // The criteria to find the user
      {
        hashedUniqueStringResetPassword: cryptUniqueString(
          uniqueString,
          user._id
        ),
      }, // The criteria to find the user
      { new: true } // To return the updated document
    );
    await sendForgotPassword(req, user.email, uniqueString, user._id);
    console.log("send forgot to email", user.email);
    res
      .status(200)
      .send({ msg: req.t("check_your_inbox_email_to_reset_password") });
  } catch (error) {
    console.log(error);
    res.status(500).send(req.t("server_error"));
  }
};

exports.verifyEmailAccountToResetPassword = async (req, res) => {
  try {
    const { activationField, userId, locale } = req.query;
    const user = await User.findOne({ _id: userId }).select(
      "+hashedUniqueStringResetPassword"
    );
    if (!user) {
      return res.status(404).send(req.t("foreign_error"));
    }
    if (
      cryptUniqueString(activationField, userId) ==
      user.hashedUniqueStringResetPassword
    ) {
      const destinationURL =
        process.env.FRONTEND_URL +
        `/${
          locale || "fr"
        }/auth/reset-password?changePassword=true&userId=${userId}&activationField=${activationField}`;
      return res.redirect(destinationURL);
    }
    return res.status(404).send(req.t("activeted_error"));
  } catch (error) {
    console.log("122", error);
    return res.status(404).send(req.t("foreign_error"));
  }
};

exports.resetPassword = async (req, res) => {
  try {
    const { password, userId, activationField } = req.body;

    // Validate required fields
    if (!password || !userId || !activationField) {
      return res
        .status(400)
        .send({ message: req.t("errors.unauthorized_action") });
    }

    // Fetch user with required field
    const user = await User.findOne({ _id: userId }).select(
      "+hashedUniqueStringResetPassword"
    );
    if (!user) {
      return res
        .status(404)
        .send({ message: req.t("errors.unauthorized_action") });
    }

    // Check if activation field matches the stored hash
    const isFieldValid =
      cryptUniqueString(activationField, user._id) ===
      user.hashedUniqueStringResetPassword;
    if (!isFieldValid) {
      return res
        .status(401)
        .send({ message: req.t("errors.invalid_reset_token") });
    }

    // Update password and clear reset token
    user.password = password;
    user.hashedUniqueStringResetPassword = undefined;
    await user.save();

    return res
      .status(200)
      .send({ message: req.t("errors.password_reset_successful") });
  } catch (error) {
    console.error("Reset password error:", error);

    // Differentiating between validation and internal errors
    if (error.name === "ValidationError") {
      return res.status(400).send({
        message: req.t("errors.validation_error"),
        details: error.errors,
      });
    }

    return res.status(500).send({ message: req.t("errors.server_error") });
  }
};

exports.verifyPhoneNumber = async (req, res) => {
  const userId = req.auth.id;
  try {
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).send({ message: req.t("login_failed") });
    }
    const userFound = await User.findById(new mongoose.Types.ObjectId(userId));
    userFound.role = AccountRole.Vendor;
    userFound.phoneNumber = req.body.phone;
    userFound.phonVerifyAt = new Date();

    const data = {
      title: "account_to_vendor_title",
      subject: "account_to_vendor_subject",
      navigationCode: NotificationNavCode.NAVIGATION_ADS_PAGE,
    };
    await sendNotificationFromAdmin(data, userFound._id);
    const userUpdated = await userFound.save();
    return res.send({
      message: req.t("edited_success"),
      userUpdated,
    });
  } catch (error) {
    console.log(error);
    return res.status(404).send(req.t("server_error"));
  }
};
