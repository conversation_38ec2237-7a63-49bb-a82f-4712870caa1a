const express = require("express");
const {
  createOrder,
  getOrder,
  updateOrder<PERSON>tatus,
  markOrderAsPaid,
  refundOrder,
  getMyOrders,
  getSubOrdersByUserIdAndCategory,
  getSubOrderById,
  getUserDomainOrders,
} = require("../controllers/orderControllers");
const { fetchUserMiddleware } = require("../midelwares/authorization");
const { billingInfoValidator } = require("../midelwares/requests/userRequest");
const { asyncBackFrontEndLang } = require("../midelwares/sharedMidd");

const orderRouter = express.Router();

// Route to create a new order from the cart
orderRouter.post(
  "/create-order",
  asyncBackFrontEndLang,
  billingInfoValidator,
  fetchUserMiddleware,
  createOrder
);

// Route to get a specific order by ID
orderRouter.get("/get-order/:orderId", getOrder);

orderRouter.get(
  "/get-my-orders",
  asyncBackFrontEndLang,
  fetchUserMiddleware,
  getMyOrders
);

orderRouter.get(
  "/get-suborders-by-category/:categoryName",
  asyncBackFrontEndLang,
  fetchUserMiddleware,
  getSubOrdersByUserIdAndCategory
);

// Route to get a specific suborder by ID
orderRouter.get(
  "/get-suborder/:subOrderId",
  asyncBackFrontEndLang,
  fetchUserMiddleware,
  getSubOrderById
);

// Route to update the order status
orderRouter.put("/update-status/:id", updateOrderStatus);

// Route to mark the order as paid
orderRouter.put("/mark-paid/:id", markOrderAsPaid);

// Route to process a refund for the order
orderRouter.put("/refund/:id", refundOrder);

// Route to get user's domain orders
orderRouter.get(
  "/get-domain-orders",
  asyncBackFrontEndLang,
  fetchUserMiddleware,
  getUserDomainOrders
);

module.exports = orderRouter;
