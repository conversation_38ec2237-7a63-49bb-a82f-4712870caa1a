exports.translateTextService = async (text, targetLang) => {
  try {
    const API_KEY = process.env.GOOGLE_AI_API_KEY;
    const MODEL = "google/gemini-2.0-flash-lite-preview-02-05:free";

    const response = await fetch(
      "https://openrouter.ai/api/v1/chat/completions",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${API_KEY}`,
          "HTTP-Referer": "https://ztechengineering.com",
          "X-Title": "ZTech Engineering Assistant",
        },
        body: JSON.stringify({
          model: MODEL,
          messages: [
            {
              role: "system",
              content:
                "You are a professional translator. Follow these rules strictly:\n" +
                "1. Translate the given text to French\n" +
                "2. Maintain exactly the same structure and formatting as the input\n" +
                "3. Keep any special characters, numbers, or technical terms intact\n" +
                "4. Return ONLY the translated text without quotes or additional context\n" +
                "5. If there are bullet points or numbering, preserve them exactly",
            },
            {
              role: "user",
              content: text,
            },
          ],
          temperature: 0.2,
          max_tokens: 500,
        }),
      }
    );

    const data = await response.json();
    const translatedText = data.choices[0].message.content.trim();

    return translatedText;
  } catch (error) {
    console.error("Error translating text:", error);
    throw new Error("Failed to translate text");
  }
};
