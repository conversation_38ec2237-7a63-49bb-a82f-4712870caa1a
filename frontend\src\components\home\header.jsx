"use client";

import { useTranslations } from "next-intl";
import TopNavbar from "./topNavbar";
import SiteBranding from "./siteBranding";
import MainNavbar from "./mainNavbar";

export default function Header() {
  const t = useTranslations("shared");
  return (
    <header className="flex sticky top-0 z-50 right-0 left-0 p-0 w-full rounded-md bg-white border-b shadow-sm mb-0 max-w-[1900px] mx-auto h-[90px] lg:h-[80px]">
      <div className="flex flex-col w-full">
        {/* First row: TopNavbar */}
        <div className="w-full">
          <TopNavbar t={t} />
        </div>

        {/* Second row: Logo and MainNavbar */}
        <div className="w-full flex items-center md:p-0 lg:px-6 justify-between">
          <div className="w-[150px] flex-shrink-0 hidden lg:flex items-center justify-center">
            <SiteBranding />
          </div>
          <div className="lg:w-4/5 w-full">
            <MainNavbar t={t} />
          </div>
        </div>
      </div>
    </header>
  );
}
