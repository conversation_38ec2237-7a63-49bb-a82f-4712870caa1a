import React, { useState, useEffect } from "react";
import {
  ArrowDownWideNarrow,
  Edit,
  Eye,
  EyeClosed,
  ImageIcon,
  PlusSquare,
  RefreshCcw,
  Trash2,
  X,
  FileText,
  DollarSign,
  GripVertical,
} from "lucide-react";
import { adminService } from "../../services/adminService";
import PreviewPackage from "../../../components/admin/package/PreviewPackage";
import { toast } from "react-toastify";
import { ProductStatus } from "../../config/ProductStatus";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";

const DISCOUNTS = {
  Hosting: [1, 12],
  SSL: [1, 12, 24, 36],
};

const SSLTypes = [
  "DV",
  "OV",
  "EV",
  "Wildcard",
  "Multi-Domain",
  "WP WILDCARD",
  "WILDCARD OV",
  "WILDCARD DV",
  "Multi-Domain OV",
  "MULTI-DOMAINS EV",
];

const FormField = ({ label, children }) => (
  <div className="w-full">
    <label className="text-sm font-medium text-gray-700 mb-1">{label}</label>
    {children}
  </div>
);

const StyledInput = ({ ...props }) => (
  <input
    {...props}
    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
  />
);

const PricingFields = ({ formData, setFormData, categories }) => {
  const [customPeriod, setCustomPeriod] = useState("");

  const handlePriceChange = (price) => {
    setFormData({
      ...formData,
      price: parseFloat(price) || 0,
    });
  };

  const handleDiscountChange = (percentage, period) => {
    // If empty string, set to 0 explicitly
    if (percentage === "") {
      const newDiscounts = [...(formData.discounts || [])];
      const existingIndex = newDiscounts.findIndex((d) => d.period === period);

      if (existingIndex >= 0) {
        newDiscounts[existingIndex].percentage = 0;
      } else {
        newDiscounts.push({ period, percentage: 0 });
      }

      setFormData({
        ...formData,
        discounts: newDiscounts,
      });
      return;
    }

    const newPercentage = Math.min(Math.max(parseFloat(percentage), 0), 100);
    const newDiscounts = [...(formData.discounts || [])];
    const existingIndex = newDiscounts.findIndex((d) => d.period === period);

    if (existingIndex >= 0) {
      newDiscounts[existingIndex].percentage = newPercentage;
    } else {
      newDiscounts.push({ period, percentage: newPercentage });
    }

    setFormData({
      ...formData,
      discounts: newDiscounts,
    });
  };

  const handleAddCustomPeriod = () => {
    if (!customPeriod || isNaN(customPeriod) || parseInt(customPeriod) <= 0) {
      toast.error("Please enter a valid period (positive number)");
      return;
    }

    const periodNum = parseInt(customPeriod);
    const newDiscounts = [...(formData.discounts || [])];

    // Check if period already exists
    if (newDiscounts.some((d) => d.period === periodNum)) {
      toast.warning(`Discount for ${periodNum} month(s) already exists`);
      return;
    }

    newDiscounts.push({ period: periodNum, percentage: 0 });

    setFormData({
      ...formData,
      discounts: newDiscounts,
    });

    setCustomPeriod("");
  };

  const handleRemovePeriod = (period) => {
    const newDiscounts = formData.discounts.filter((d) => d.period !== period);
    setFormData({
      ...formData,
      discounts: newDiscounts,
    });
  };

  const getDiscountByPeriod = (period) => {
    if (!formData?.discounts) return 0;
    const discountsArray =
      formData.discounts instanceof Map
        ? Array.from(formData.discounts.values())
        : formData.discounts;

    const discount = discountsArray?.find((d) => d.period === period);
    return discount ? discount.percentage : 0;
  };

  const formatPeriodLabel = (period) => {
    if (period < 12) {
      return `${period} Month${period > 1 ? "s" : ""}`;
    } else {
      const years = period / 12;
      return `${years % 1 === 0 ? years : years.toFixed(1)} Year${
        years > 1 ? "s" : ""
      }`;
    }
  };

  const selectedCategory = categories.find(
    (cat) => cat._id === formData.category
  );
  const categoryName = selectedCategory?.name || "";
  const availableDiscounts = formData.discounts || [];

  // Sort discounts by period
  const sortedDiscounts = [...availableDiscounts].sort(
    (a, b) => a.period - b.period
  );

  return (
    <div className="space-y-6">
      <FormField label="Price">
        <div className="relative">
          <div className="absolute inset-y-0 right-12 flex items-center pl-3 pointer-events-none">
            <span className="text-gray-500">MAD</span>
          </div>
          <StyledInput
            type="number"
            required
            min="0"
            step="0.01"
            value={formData.price || ""}
            onChange={(e) => handlePriceChange(e.target.value)}
            placeholder="0.00"
            className="pl-8 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </FormField>

      {categoryName && (
        <div className="bg-gray-50 p-5 rounded-xl border border-gray-200">
          <div className="flex justify-between items-center mb-4">
            <h4 className="text-sm font-semibold text-gray-700 flex items-center">
              <span className="bg-blue-100 text-blue-600 p-1 rounded-md mr-2">
                <PlusSquare size={16} />
              </span>
              Discount Periods
            </h4>
            <div className="flex items-center gap-2 bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
              <input
                type="number"
                value={customPeriod}
                onChange={(e) => setCustomPeriod(e.target.value)}
                placeholder="Add period (months)"
                className="w-48 px-3 py-2 text-sm border-none focus:ring-0 outline-none"
                min="1"
              />
              <button
                type="button"
                onClick={handleAddCustomPeriod}
                className="px-3 py-2 bg-blue-500 text-white hover:bg-blue-600 transition-colors"
                title="Add custom period"
              >
                <PlusSquare size={16} />
              </button>
            </div>
          </div>

          {sortedDiscounts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {sortedDiscounts.map((discount) => (
                <div
                  key={discount.period}
                  className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
                >
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      {formatPeriodLabel(discount.period)}
                    </span>
                    <button
                      type="button"
                      onClick={() => handleRemovePeriod(discount.period)}
                      className="text-gray-400 hover:text-red-500 transition-colors"
                      title="Remove period"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                      <span className="text-gray-500">%</span>
                    </div>
                    <StyledInput
                      type="number"
                      min="0"
                      max="100"
                      step="1"
                      value={getDiscountByPeriod(discount.period)}
                      onChange={(e) =>
                        handleDiscountChange(e.target.value, discount.period)
                      }
                      placeholder="0"
                      className="pr-8 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-lg p-6 text-center border border-dashed border-gray-300">
              <div className="text-gray-400 mb-2">
                <PlusSquare size={24} className="mx-auto" />
              </div>
              <p className="text-sm text-gray-500">
                No discount periods configured yet. Add your first period above.
              </p>
            </div>
          )}
        </div>
      )}

      {!categoryName && (
        <div className="bg-blue-50 p-5 rounded-xl border border-blue-100 text-center">
          <p className="text-sm text-blue-600">
            Select a category first to configure discount periods
          </p>
        </div>
      )}
    </div>
  );
};

const SortableSpec = ({ spec, onRemove, onUpdateSpec }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: spec._id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center bg-white p-2 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
    >
      <div
        {...attributes}
        {...listeners}
        className="mr-2 cursor-grab text-gray-400 hover:text-gray-600"
      >
        <GripVertical size={18} />
      </div>
      <button
        type="button"
        onClick={() => onUpdateSpec(spec._id)}
        className="mr-2"
      >
        <Edit className="text-gray-600 hover:text-blue-600" size={16} />
      </button>
      <span className="flex-grow">{spec?.value}</span>
      <button
        type="button"
        onClick={() => onRemove(spec._id)}
        className="ml-2 text-gray-400 hover:text-red-500 transition-colors"
      >
        <Trash2 size={16} />
      </button>
    </div>
  );
};

const SpecsInput = ({
  specInput,
  handleSpecInputChange,
  filteredSpecs,
  handleSpecSelect,
  handleAddSpec,
  selectedSpecs,
  handleRemoveSpec,
  onUpdateSpec,
  onReorderSpecs,
  isLoadingSpecs,
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = selectedSpecs.findIndex(
        (spec) => spec._id === active.id
      );
      const newIndex = selectedSpecs.findIndex((spec) => spec._id === over.id);
      onReorderSpecs({ oldIndex, newIndex });
    }
  };

  return (
    <div className="py-4">
      <FormField label="Package Specs">
        <div className="relative">
          <div className="flex gap-x-2">
            <input
              type="text"
              value={specInput}
              onChange={handleSpecInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Type to search or add new specifications"
              autoComplete="off"
            />
            <button
              type="button"
              onClick={handleAddSpec}
              className="bg-blue-500 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-600 whitespace-nowrap"
              disabled={!specInput.trim()}
            >
              Add Spec
            </button>
          </div>

          {isLoadingSpecs && (
            <div className="absolute left-0 right-0 top-full mt-1 p-2 bg-white border border-gray-300 rounded-lg shadow-md z-20">
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
                <span className="ml-2 text-sm text-gray-600">
                  Loading specifications...
                </span>
              </div>
            </div>
          )}

          {filteredSpecs.length > 0 && (
            <ul className="absolute left-0 right-0 top-full mt-1 bg-white border border-gray-300 rounded-lg shadow-md max-h-60 overflow-y-auto z-20">
              {filteredSpecs.map((spec) => (
                <li
                  key={spec._id}
                  onClick={() => handleSpecSelect(spec)}
                  className="px-4 py-2 cursor-pointer hover:bg-gray-100 border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex justify-between items-center">
                    <span>{spec.value}</span>
                    <span className="text-xs text-gray-500">
                      {spec.value_fr ? "Translated" : "No translation"}
                    </span>
                  </div>
                </li>
              ))}
            </ul>
          )}

          {specInput.trim() !== "" &&
            filteredSpecs.length === 0 &&
            !isLoadingSpecs && (
              <div className="absolute left-0 right-0 top-full mt-1 p-3 bg-white border border-gray-300 rounded-lg shadow-md z-20">
                <p className="text-sm text-gray-600">
                  No matching specifications found. Click &quot;Add Spec &quot;
                  to create a new one.
                </p>
              </div>
            )}
        </div>

        {selectedSpecs.length === 0 ? (
          <p className="text-sm text-gray-500 mt-4">No specs added yet.</p>
        ) : (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
            modifiers={[restrictToVerticalAxis]}
          >
            <SortableContext
              items={selectedSpecs.map((spec) => spec._id)}
              strategy={verticalListSortingStrategy}
            >
              <div className="mt-4 space-y-2">
                {selectedSpecs.map((spec) => (
                  <SortableSpec
                    key={spec._id}
                    spec={spec}
                    onRemove={handleRemoveSpec}
                    onUpdateSpec={onUpdateSpec}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        )}
      </FormField>
    </div>
  );
};

const SpecTranslationPopup = ({
  spec,
  initialFrench = "",
  onSave,
  onCancel,
  isUpdating = false,
}) => {
  const [specData, setSpecData] = useState({
    value: spec,
    value_fr: initialFrench,
  });
  const [isTranslating, setIsTranslating] = useState(false);

  const handleSpecTranslation = async () => {
    if (!specData.value) return;

    setIsTranslating(true);
    try {
      const data = { text: specData.value, targetLang: "fr" };
      const response = await adminService.deeplTranslate(data);
      const translatedText = response.data.translatedText;

      setSpecData({ ...specData, value_fr: translatedText });
      toast.success("Text translated successfully");
    } catch (error) {
      console.error("Error translating text:", error);
      toast.error("Failed to translate text");
    } finally {
      setIsTranslating(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg w-[500px]">
        <h3 className="text-lg font-medium mb-4">
          {isUpdating ? "Update Specification" : "Add New Specification"}
        </h3>
        <div className="space-y-4">
          <FormField label="English">
            <input
              type="text"
              value={specData.value}
              onChange={(e) =>
                setSpecData({ ...specData, value: e.target.value })
              }
              className="w-full px-4 py-2 border border-gray-300 rounded-lg"
              disabled={isTranslating}
            />
          </FormField>

          <div className="flex justify-center -my-2">
            <button
              type="button"
              onClick={handleSpecTranslation}
              className={`p-2 hover:bg-gray-100 rounded-full transition-colors ${
                isTranslating ? "cursor-not-allowed opacity-50" : ""
              }`}
              title="Translate to French"
              disabled={isTranslating}
            >
              <RefreshCcw
                size={20}
                className={`text-blue-500 ${
                  isTranslating ? "animate-spin" : ""
                }`}
              />
            </button>
          </div>

          <FormField label="French">
            <input
              type="text"
              value={specData.value_fr}
              onChange={(e) =>
                setSpecData({ ...specData, value_fr: e.target.value })
              }
              className="w-full px-4 py-2 border border-gray-300 rounded-lg"
              disabled={isTranslating}
            />
          </FormField>
          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              disabled={isTranslating}
            >
              Cancel
            </button>
            <button
              onClick={() => onSave(specData)}
              className={`px-4 py-2 bg-blue-500 text-white rounded-lg ${
                isTranslating || specData?.value_fr.trim() === ""
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:bg-blue-600"
              }`}
              disabled={isTranslating || specData?.value_fr.trim() === ""}
            >
              {isUpdating ? "Update" : "Save"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

function PackageForm({
  formData,
  setFormData,
  handleSubmit,
  setShowForm,
  editingId,
  selectedSpecs,
  setSelectedSpecs,
}) {
  const [showPreview, setShowPreview] = useState(false);
  const [specInput, setSpecInput] = useState("");
  const [specs, setSpecs] = useState([]);
  const [filteredSpecs, setFilteredSpecs] = useState([]);
  const [statuses] = useState(Object.values(ProductStatus));
  const [showSpecPopup, setShowSpecPopup] = useState(false);
  const [newSpec, setNewSpec] = useState({ value: "", value_fr: "" });
  const [showUpdateSpecPopup, setShowUpdateSpecPopup] = useState(false);
  const [specToUpdate, setSpecToUpdate] = useState(null);
  const [isNameTranslating, setIsNameTranslating] = useState(false);
  const [isDescTranslating, setIsDescTranslating] = useState(false);
  const [categories, setCategories] = useState([]);
  const [brands, setBrands] = useState([]);
  const [isLoadingSpecs, setIsLoadingSpecs] = useState(false);

  const handleReorderSpecs = ({ oldIndex, newIndex }) => {
    const items = Array.from(selectedSpecs);
    const [reorderedItem] = items.splice(oldIndex, 1);
    items.splice(newIndex, 0, reorderedItem);

    setSelectedSpecs(items);
  };

  useEffect(() => {
    const fetchSpecs = async () => {
      try {
        setIsLoadingSpecs(true);
        const response = await adminService.getAllSpecs();
        if (response && response.data) {
          setSpecs(response.data);
          console.log("Fetched specs:", response.data);
        } else {
          console.error("Invalid response format:", response);
          toast.error("Failed to fetch specifications");
        }
      } catch (error) {
        console.error("Error fetching specs:", error);
        toast.error("Failed to fetch specifications");
      } finally {
        setIsLoadingSpecs(false);
      }
    };

    const fetchCategories = async () => {
      try {
        const response = await adminService.getAllCategories();
        setCategories(response.data);
        if (editingId && formData.category) {
          const selectedCategory = response.data.find(
            (cat) => cat._id === formData.category
          );
          if (selectedCategory) {
            setBrands(selectedCategory.brands || []);
          }
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
        toast.error("Failed to fetch categories");
      }
    };

    fetchSpecs();
    fetchCategories();
  }, [editingId, formData.category]);

  const handleAddSpec = async () => {
    if (specInput.trim() === "") return;

    const existingSpec = specs.find(
      (spec) => spec.value.toLowerCase() === specInput.toLowerCase()
    );

    if (existingSpec) {
      const isAlreadySelected = selectedSpecs.some(
        (spec) => spec.value.toLowerCase() === specInput.toLowerCase()
      );

      if (!isAlreadySelected) {
        setSelectedSpecs([...selectedSpecs, existingSpec]);
        toast.success("Spec selected successfully");
      } else {
        toast.warning("Spec already selected");
      }
    } else {
      setShowSpecPopup(true);
      setNewSpec({ value: specInput, value_fr: "" });
    }

    setSpecInput("");
    setFilteredSpecs([]);
  };

  const handleSaveNewSpec = async (specData) => {
    try {
      const addRes = await adminService.addSpec(specData);
      setSpecs([...specs, addRes.data]);
      setSelectedSpecs([...selectedSpecs, addRes.data]);
      toast.success("Spec added to the database successfully");
      setShowSpecPopup(false);
    } catch (error) {
      toast.error("Failed to add spec");
    }
  };

  const handleSpecInputChange = (e) => {
    const input = e.target.value;
    setSpecInput(input);
    setFilteredSpecs(
      input.trim() === ""
        ? []
        : specs.filter((spec) =>
            spec.value.toLowerCase().includes(input.toLowerCase())
          )
    );
  };

  const handleSpecSelect = (newSpec) => {
    const isAlreadySelected = selectedSpecs.some(
      (spec) => spec.value.toLowerCase() === newSpec.value.toLowerCase()
    );

    if (!isAlreadySelected) {
      setSelectedSpecs([...selectedSpecs, newSpec]);
      setSpecInput("");
    } else {
      toast.warning("Spec already selected");
    }
    setFilteredSpecs([]);
  };

  const handleRemoveSpec = (specId) => {
    setSelectedSpecs(selectedSpecs.filter((spec) => spec._id !== specId));
  };

  const handleUpdateSpec = async (specId) => {
    const specToEdit = selectedSpecs.find((spec) => spec._id === specId);
    setSpecToUpdate(specToEdit);
    setShowUpdateSpecPopup(true);
  };

  const handleSaveUpdatedSpec = async (updatedSpecData) => {
    try {
      const response = await adminService.updateSpec({
        _id: specToUpdate._id,
        value: updatedSpecData.value,
        value_fr: updatedSpecData.value_fr,
      });

      const updatedSpecs = specs.map((spec) =>
        spec._id === specToUpdate._id ? response.data?.updatedSpec : spec
      );
      setSpecs(updatedSpecs);

      const updatedSelectedSpecs = selectedSpecs.map((spec) =>
        spec._id === specToUpdate._id ? response.data?.updatedSpec : spec
      );

      setSelectedSpecs(updatedSelectedSpecs);

      setShowUpdateSpecPopup(false);
      setSpecToUpdate(null);
      toast.success("Specification updated successfully");
    } catch (error) {
      console.error("Error updating spec:", error);
      toast.error("Failed to update specification");
    }
  };

  const resetForm = () => {
    setShowForm(false);
    setFormData({
      name: "",
      name_fr: "",
      description: "",
      description_fr: "",
      price: 0,
      discounts: [],
      specifications: [],
      billingPeriod: "",
      image: "",
      brand: "",
      status: "",
      category: "",
      categoryName: "",
      sslType: "",
    });
    setBrands([]);
  };

  const handleTranslation = async (text, targetLang, name) => {
    if (!text) return;

    if (name === "name") {
      setIsNameTranslating(true);
    } else if (name === "description") {
      setIsDescTranslating(true);
    }

    try {
      const data = { text, targetLang };
      const response = await adminService.deeplTranslate(data);
      const translatedText = response.data.translatedText;

      if (name === "name") {
        setFormData({ ...formData, name_fr: translatedText });
        toast.success("Name translated successfully");
      } else if (name === "description") {
        setFormData({ ...formData, description_fr: translatedText });
        toast.success("Description translated successfully");
      }
    } catch (error) {
      console.error("Error translating text:", error);
      toast.error("Failed to translate text");
    } finally {
      if (name === "name") {
        setIsNameTranslating(false);
      } else if (name === "description") {
        setIsDescTranslating(false);
      }
    }
  };

  const handleCategoryChange = (categoryId) => {
    const selectedCategory = categories.find((cat) => cat._id === categoryId);
    const isSSLCategory = selectedCategory?.name === "SSL";

    setFormData({
      ...formData,
      category: categoryId || "",
      categoryName: selectedCategory?.name || "",
      discounts: [], // Initialize with empty discounts array
      brand: "",
      ...(!isSSLCategory && { sslType: "" }),
    });
    setBrands(selectedCategory?.brands || []);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm relative">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-medium font-inter text-[#212121] flex items-center">
          {editingId ? (
            <>
              <Edit className="h-5 w-5 mr-2 text-blue-500" />
              Edit Package
            </>
          ) : (
            <>
              <PlusSquare className="h-5 w-5 mr-2 text-green-500" />
              Add New Package
            </>
          )}
        </h3>
        <button
          onClick={resetForm}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      <div className="flex items-center justify-between w-full p-4 rounded-lg">
        <form
          onSubmit={handleSubmit}
          className={`space-y-6 flex flex-col ${
            showPreview ? "w-1/2 pr-6" : "w-full"
          } transition-all duration-500`}
        >
          <details
            className="group border rounded-lg p-4 bg-white shadow-sm"
            open
          >
            <summary className="cursor-pointer list-none flex justify-between items-center">
              <span className="font-medium flex items-center">
                <span className="bg-blue-100 text-blue-600 p-1 rounded-md mr-2">
                  <FileText size={16} />
                </span>
                Basic Information
              </span>
              <span className="transition group-open:rotate-180">
                <ArrowDownWideNarrow className="text-blue-400" />
              </span>
            </summary>
            <div className="mt-4 space-y-4">
              <div className="flex gap-x-6 justify-between items-center">
                <FormField label="Package Name (English)">
                  <StyledInput
                    type="text"
                    required
                    value={formData.name}
                    onChange={(e) =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                    disabled={isNameTranslating}
                    className="focus:ring-blue-500 focus:border-blue-500"
                  />
                </FormField>
                <button
                  type="button"
                  onClick={() => handleTranslation(formData.name, "fr", "name")}
                  className={`p-2 hover:bg-gray-100 rounded-full transition-colors ${
                    isNameTranslating ? "cursor-not-allowed opacity-50" : ""
                  }`}
                  disabled={isNameTranslating}
                >
                  <RefreshCcw
                    size={20}
                    className={`text-blue-500 ${
                      isNameTranslating ? "animate-spin" : ""
                    }`}
                  />
                </button>
                <FormField label="Package Name (French)">
                  <StyledInput
                    type="text"
                    required
                    value={formData.name_fr}
                    onChange={(e) =>
                      setFormData({ ...formData, name_fr: e.target.value })
                    }
                    disabled={isNameTranslating}
                    className="focus:ring-blue-500 focus:border-blue-500"
                  />
                </FormField>
              </div>
              <div className="flex w-full gap-x-6 justify-between">
                <FormField label="Description (English)">
                  <textarea
                    required
                    rows={2}
                    value={formData.description}
                    onChange={(e) =>
                      setFormData({ ...formData, description: e.target.value })
                    }
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={isDescTranslating}
                  />
                </FormField>
                <button
                  type="button"
                  onClick={() =>
                    handleTranslation(formData.description, "fr", "description")
                  }
                  className={`p-2 hover:bg-gray-100 rounded-full transition-colors ${
                    isDescTranslating ? "cursor-not-allowed opacity-50" : ""
                  }`}
                  disabled={isDescTranslating}
                >
                  <RefreshCcw
                    size={20}
                    className={`text-blue-500 h-fit w-fit m-auto ${
                      isDescTranslating ? "animate-spin" : ""
                    }`}
                  />
                </button>
                <FormField label="Description (French)">
                  <textarea
                    required
                    rows={2}
                    value={formData.description_fr}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        description_fr: e.target.value,
                      })
                    }
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={isDescTranslating}
                  />
                </FormField>
              </div>
            </div>
          </details>

          <details className="group border rounded-lg p-4 bg-white shadow-sm">
            <summary className="cursor-pointer list-none flex justify-between items-center">
              <span className="font-medium flex items-center">
                <span className="bg-green-100 text-green-600 p-1 rounded-md mr-2">
                  <DollarSign size={16} />
                </span>
                Pricing & Brand Information
              </span>
              <span className="transition group-open:rotate-180">
                <ArrowDownWideNarrow className="text-blue-400" />
              </span>
            </summary>
            <div className="mt-4 space-y-4">
              <FormField label="Category">
                <select
                  required
                  value={formData.category || ""}
                  onChange={(e) => handleCategoryChange(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select a category</option>
                  {categories.map((category) => (
                    <option key={category._id} value={category._id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </FormField>
              <PricingFields
                formData={formData}
                setFormData={setFormData}
                categories={categories}
              />
              <div
                className={`grid ${
                  formData.categoryName === "SSL" || formData.sslType
                    ? "grid-cols-3"
                    : "grid-cols-2"
                } gap-4`}
              >
                <FormField label="Brand">
                  <select
                    required
                    value={formData.brand || ""}
                    onChange={(e) =>
                      setFormData({ ...formData, brand: e.target.value })
                    }
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!formData.category}
                  >
                    <option value="">Select a brand</option>
                    {brands.map((brand) => (
                      <option key={brand._id} value={brand._id}>
                        {brand.name}
                      </option>
                    ))}
                  </select>
                </FormField>
                <FormField label="Status">
                  <select
                    required
                    value={formData.status || ""}
                    onChange={(e) =>
                      setFormData({ ...formData, status: e.target.value })
                    }
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select a status</option>
                    {statuses.map((status) => (
                      <option key={status} value={status}>
                        {status}
                      </option>
                    ))}
                  </select>
                </FormField>
                {(formData.categoryName === "SSL" || formData.sslType) && (
                  <FormField label="SSL Type">
                    <select
                      required
                      value={formData.sslType || ""}
                      onChange={(e) =>
                        setFormData({ ...formData, sslType: e.target.value })
                      }
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select SSL Type</option>
                      {SSLTypes.map((type) => (
                        <option key={type} value={type}>
                          {type}
                        </option>
                      ))}
                    </select>
                  </FormField>
                )}
              </div>
            </div>
          </details>

          <details className="group border rounded-lg p-4" open>
            <summary className="cursor-pointer list-none flex justify-between items-center">
              <span className="font-medium flex items-center">
                <span className="bg-purple-100 text-purple-600 p-1 rounded-md mr-2">
                  <FileText size={16} />
                </span>
                Specifications / Features
                <span className="ml-2 text-xs text-gray-500">
                  (Drag to reorder)
                </span>
              </span>
              <span className="transition group-open:rotate-180">
                <ArrowDownWideNarrow className="text-blue-400" />
              </span>
            </summary>
            <SpecsInput
              specInput={specInput}
              handleSpecInputChange={handleSpecInputChange}
              filteredSpecs={filteredSpecs}
              handleSpecSelect={handleSpecSelect}
              handleAddSpec={handleAddSpec}
              selectedSpecs={selectedSpecs}
              handleRemoveSpec={handleRemoveSpec}
              onUpdateSpec={handleUpdateSpec}
              onReorderSpecs={handleReorderSpecs}
              isLoadingSpecs={isLoadingSpecs}
            />
          </details>

          <div className="flex justify-between pt-4">
            <button
              type="button"
              onClick={() => setShowPreview(!showPreview)}
              className="bg-white text-[#212121] text-base px-4 py-2 rounded-lg font-medium hover:text-blue-700"
            >
              {showPreview ? (
                <span className="flex items-center gap-x-2 text-secondary">
                  <Eye />
                  Hide Preview
                </span>
              ) : (
                <span className="flex items-center gap-x-2">
                  <EyeClosed />
                  Preview
                </span>
              )}
            </button>
            <button
              type="submit"
              className="text-base text-[#212121] px-6 py-2 rounded-lg font-medium hover:text-blue-700"
            >
              {editingId ? (
                <span className="flex items-center gap-x-2">
                  <Edit />
                  Update Package
                </span>
              ) : (
                <span className="flex items-center gap-x-2">
                  <PlusSquare />
                  Add Package
                </span>
              )}
            </button>
          </div>
        </form>

        {showPreview && (
          <div className="w-1/2 mx-auto">
            <div className="sticky top-4 bg-gray-50 p-4 rounded-xl border border-gray-200 shadow-sm">
              <h4 className="text-sm font-semibold text-gray-700 mb-4 flex items-center">
                <Eye size={16} className="mr-2 text-blue-500" />
                Package Preview
              </h4>
              <div className="transform transition-all duration-300 hover:scale-[1.02]">
                <PreviewPackage
                  title={formData.name}
                  price={formData.price}
                  discounts={formData.discounts || []}
                  per={
                    formData.billingPeriod === "yearly" ? "Yearly" : "Monthly"
                  }
                  formData={formData}
                  image={formData.image}
                  features={selectedSpecs || []}
                  color="bg-secondry"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {showSpecPopup && (
        <SpecTranslationPopup
          spec={newSpec.value}
          onSave={handleSaveNewSpec}
          onCancel={() => setShowSpecPopup(false)}
        />
      )}

      {showUpdateSpecPopup && (
        <SpecTranslationPopup
          spec={specToUpdate.value}
          initialFrench={specToUpdate.value_fr}
          onSave={handleSaveUpdatedSpec}
          onCancel={() => {
            setShowUpdateSpecPopup(false);
            setSpecToUpdate(null);
          }}
          isUpdating={true}
        />
      )}
    </div>
  );
}

export default PackageForm;
