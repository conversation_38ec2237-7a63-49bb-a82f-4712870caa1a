const axios = require("axios");
const contactService = require("../../services/contactService");

/**
 * Add a new contact
 */
exports.addContact = async (req, res) => {
  try {
    const { contactDetails, customerId } = req.body;
    console.log("addContact: ", contactDetails, customerId);

    // Validate required fields
    if (!contactDetails || !contactDetails.name || !contactDetails.email) {
      return res.status(400).json({
        error: "Contact details with name and email are required",
      });
    }

    if (!customerId) {
      return res.status(400).json({
        error: "Customer ID is required",
      });
    }

    const result = await contactService.addContact(contactDetails, customerId);

    res.status(201).json({
      success: true,
      message: "Contact added successfully",
      data: result,
    });
  } catch (error) {
    console.error("Error in addContact controller:", error);
    res.status(500).json({
      error: "Failed to add contact",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Modify an existing contact
 */
exports.modifyContact = async (req, res) => {
  try {
    const { contactId, updatedDetails } = req.body;

    if (!contactId) {
      return res.status(400).json({
        error: "Contact ID is required",
      });
    }

    if (!updatedDetails || Object.keys(updatedDetails).length === 0) {
      return res.status(400).json({
        error: "Updated details are required",
      });
    }

    const result = await contactService.modifyContact(
      contactId,
      updatedDetails
    );

    res.json({
      success: true,
      message: "Contact modified successfully",
      data: result,
    });
  } catch (error) {
    console.error("Error in modifyContact controller:", error);
    res.status(500).json({
      error: "Failed to modify contact",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Get contact details
 */
exports.getContactDetails = async (req, res) => {
  try {
    const { contactId } = req.params;

    if (!contactId) {
      return res.status(400).json({
        error: "Contact ID is required",
      });
    }

    const result = await contactService.getContactDetails(contactId);

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error in getContactDetails controller:", error);
    res.status(500).json({
      error: "Failed to get contact details",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Search contacts
 */
exports.searchContacts = async (req, res) => {
  try {
    const searchCriteria = req.query;

    if (!searchCriteria || Object.keys(searchCriteria).length === 0) {
      return res.status(400).json({
        error: "Search criteria are required",
      });
    }

    const result = await contactService.searchContacts(searchCriteria);

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error in searchContacts controller:", error);
    res.status(500).json({
      error: "Failed to search contacts",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Get default contact for a customer
 */
exports.getDefaultContact = async (req, res) => {
  try {
    const { customerId } = req.params;

    if (!customerId) {
      return res.status(400).json({
        error: "Customer ID is required",
      });
    }

    const result = await contactService.getDefaultContact(customerId);

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error in getDefaultContact controller:", error);
    res.status(500).json({
      error: "Failed to get default contact",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Associate extra details for specific TLDs
 */
exports.associateExtraDetails = async (req, res) => {
  try {
    const { contactId, extraDetails } = req.body;

    if (!contactId) {
      return res.status(400).json({
        error: "Contact ID is required",
      });
    }

    if (!extraDetails || Object.keys(extraDetails).length === 0) {
      return res.status(400).json({
        error: "Extra details are required",
      });
    }

    const result = await contactService.associateExtraDetails(
      contactId,
      extraDetails
    );

    res.json({
      success: true,
      message: "Extra details associated successfully",
      data: result,
    });
  } catch (error) {
    console.error("Error in associateExtraDetails controller:", error);
    res.status(500).json({
      error: "Failed to associate extra details",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Delete a contact
 */
exports.deleteContact = async (req, res) => {
  try {
    const { contactId } = req.params;

    if (!contactId) {
      return res.status(400).json({
        error: "Contact ID is required",
      });
    }

    const result = await contactService.deleteContact(contactId);

    res.json({
      success: true,
      message: "Contact deleted successfully",
      data: result,
    });
  } catch (error) {
    console.error("Error in deleteContact controller:", error);
    res.status(500).json({
      error: "Failed to delete contact",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Create customer (helper function for domain registration)
 */
exports.createCustomer = async (req, res) => {
  try {
    const customerDetails = req.body;

    // Validate required fields
    if (!customerDetails.email || !customerDetails.name) {
      return res.status(400).json({
        error: "Customer email and name are required",
      });
    }

    const result = await contactService.createCustomer(customerDetails);

    res.status(201).json({
      success: true,
      message: "Customer created successfully",
      data: { customerId: result },
    });
  } catch (error) {
    console.error("Error in createCustomer controller:", error);
    res.status(500).json({
      error: "Failed to create customer",
      details: error.response?.data || error.message,
    });
  }
};
