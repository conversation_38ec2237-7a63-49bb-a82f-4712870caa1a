const User = require("../../models/User");
const Contact = require("../../models/Contact");
const contactService = require("../../services/contactService");

/**
 * Helper function to convert country name to ISO code
 */
const getCountryCode = (countryInput) => {
  if (!countryInput) return "";

  // If already a 2-letter code, return as is
  if (countryInput.length === 2 && /^[A-Z]{2}$/i.test(countryInput)) {
    return countryInput.toUpperCase();
  }

  // Common country name to ISO code mappings
  const countryMappings = {
    morocco: "MA",
    maroc: "MA",
    france: "FR",
    "united states": "US",
    usa: "US",
    canada: "CA",
    "united kingdom": "GB",
    uk: "GB",
    spain: "ES",
    germany: "DE",
    italy: "IT",
    portugal: "PT",
    algeria: "DZ",
    tunisia: "TN",
    egypt: "EG",
    "saudi arabia": "SA",
    uae: "AE",
    "united arab emirates": "AE",
    netherlands: "NL",
    belgium: "BE",
    switzerland: "CH",
    austria: "AT",
    sweden: "SE",
    norway: "NO",
    denmark: "DK",
    finland: "FI",
    poland: "PL",
    "czech republic": "CZ",
    hungary: "HU",
    romania: "RO",
    bulgaria: "BG",
    greece: "GR",
    turkey: "TR",
    russia: "RU",
    ukraine: "UA",
    india: "IN",
    china: "CN",
    japan: "JP",
    "south korea": "KR",
    australia: "AU",
    "new zealand": "NZ",
    brazil: "BR",
    argentina: "AR",
    mexico: "MX",
    chile: "CL",
    colombia: "CO",
    peru: "PE",
    venezuela: "VE",
    "south africa": "ZA",
    nigeria: "NG",
    kenya: "KE",
    ghana: "GH",
    // israel: "IL",
    lebanon: "LB",
    jordan: "JO",
    kuwait: "KW",
    qatar: "QA",
    bahrain: "BH",
    oman: "OM",
  };

  const normalizedInput = countryInput.toLowerCase().trim();
  const mappedCountry = countryMappings[normalizedInput];

  if (mappedCountry) {
    return mappedCountry;
  }

  // If no mapping found and input is not a valid 2-letter code, return empty string
  // This will trigger validation error instead of creating invalid codes like "RR"
  console.warn(
    `Invalid country input: "${countryInput}". Please use a valid country name or ISO code.`
  );
  return "";
};

/**
 * Helper function to validate and format zipcode
 */
const formatZipcode = (zipcode, countryCode) => {
  if (!zipcode) return "";

  // Remove spaces and special characters, keep only alphanumeric
  let cleanZip = zipcode.toString().replace(/[^a-zA-Z0-9]/g, "");

  // Country-specific zipcode formatting
  switch (countryCode) {
    case "US":
      // US: 5 digits or 5+4 format
      if (cleanZip.length >= 5) {
        return cleanZip.substring(0, 5);
      }
      break;
    case "CA":
      // Canada: A1A 1A1 format
      if (cleanZip.length >= 6) {
        return cleanZip.substring(0, 6).toUpperCase();
      }
      break;
    case "GB":
      // UK: Various formats, keep original but clean
      return zipcode
        .toString()
        .replace(/[^a-zA-Z0-9\s]/g, "")
        .trim()
        .toUpperCase();
    case "FR":
      // France: 5 digits
      if (cleanZip.length >= 5) {
        return cleanZip.substring(0, 5);
      }
      break;
    case "DE":
      // Germany: 5 digits
      if (cleanZip.length >= 5) {
        return cleanZip.substring(0, 5);
      }
      break;
    case "MA":
      // Morocco: 5 digits
      if (cleanZip.length >= 5) {
        return cleanZip.substring(0, 5);
      }
      break;
    default:
      // For other countries, return cleaned version with max 10 chars
      return cleanZip.substring(0, 10);
  }

  return cleanZip;
};

/**
 * Get user's domain contacts
 */
exports.getUserDomainContacts = async (req, res) => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId).populate(
      "domainContacts.registrant domainContacts.admin domainContacts.tech domainContacts.billing"
    );

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Get existing contacts from database
    const contacts = {
      registrant: user.domainContacts.registrant,
      admin: user.domainContacts.admin,
      tech: user.domainContacts.tech,
      billing: user.domainContacts.billing,
    };

    // Prepare user info for prepopulation
    const userCountryCode = getCountryCode(user.billingInfo?.country || "");
    const userZipcode = formatZipcode(
      user.billingInfo?.zipCode || user.billingInfo?.zipcode || "",
      userCountryCode
    );

    const userInfo = {
      name: `${user.firstName} ${user.lastName}`.trim(),
      email: user.email,
      company: user.billingInfo?.companyName || "",
      address: user.billingInfo?.address || "",
      city: user.billingInfo?.city || "",
      country: userCountryCode,
      zipcode: userZipcode,
      phoneCountryCode: user.phoneCountryCode || "",
      phone: user.phone || "",
    };

    // If no contacts exist, provide user info as default for prepopulation
    const contactsWithDefaults = {};
    const contactTypes = ["registrant", "admin", "tech", "billing"];

    contactTypes.forEach((type) => {
      if (contacts[type]) {
        contactsWithDefaults[type] = contacts[type];
      } else {
        // Prepopulate with user info if no contact exists
        contactsWithDefaults[type] = {
          ...userInfo,
          contactType: type,
          isDefault: true, // Flag to indicate this is prepopulated data
        };
      }
    });

    res.json({
      success: true,
      contacts: contactsWithDefaults,
      userInfo, // Send user info separately for reference
    });
  } catch (error) {
    console.error("Error getting user domain contacts:", error);
    res.status(500).json({ error: "Failed to get domain contacts" });
  }
};

/**
 * Create or update user's domain contact
 */
exports.createOrUpdateDomainContact = async (req, res) => {
  try {
    const userId = req.user._id;
    const { contactType, contactDetails } = req.body;

    // Always use the company customer ID from environment
    const customerId = process.env.COMPANY_CUSTOMER_ID;

    if (!customerId) {
      return res.status(500).json({
        error:
          "Company customer ID not configured. Please contact administrator.",
      });
    }

    // Validate contact type
    const validTypes = ["registrant", "admin", "tech", "billing"];
    if (!validTypes.includes(contactType)) {
      return res.status(400).json({ error: "Invalid contact type" });
    }

    // Validate required fields
    if (!contactDetails || !contactDetails.name || !contactDetails.email) {
      return res.status(400).json({
        error: "Contact details with name and email are required",
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Validate and format contact details
    const validatedContactDetails = {
      ...contactDetails,
      country: getCountryCode(contactDetails.country || ""),
      zipcode: formatZipcode(
        contactDetails.zipcode || "",
        getCountryCode(contactDetails.country || "")
      ),
      // Ensure phone country code is numeric
      phoneCountryCode: (
        contactDetails.phoneCountryCode ||
        contactDetails.phoneCc ||
        ""
      ).replace(/[^0-9]/g, ""),
      // Ensure phone is numeric
      phone: (contactDetails.phone || "").replace(/[^0-9]/g, ""),
    };

    // Additional validation
    if (!validatedContactDetails.country) {
      return res.status(400).json({
        error: "Valid country code is required (e.g., US, CA, FR, MA)",
      });
    }

    if (
      validatedContactDetails.zipcode &&
      validatedContactDetails.zipcode.length < 3
    ) {
      return res.status(400).json({
        error: "Valid postal code is required for the selected country",
      });
    }

    if (
      validatedContactDetails.phoneCountryCode &&
      (validatedContactDetails.phoneCountryCode.length < 1 ||
        validatedContactDetails.phoneCountryCode.length > 3)
    ) {
      return res.status(400).json({
        error: "Phone country code must be 1-3 digits",
      });
    }

    if (
      validatedContactDetails.phone &&
      (validatedContactDetails.phone.length < 4 ||
        validatedContactDetails.phone.length > 12)
    ) {
      return res.status(400).json({
        error: "Phone number must be 4-12 digits",
      });
    }

    // Check if contact already exists in database
    let existingContact = await Contact.findByUserAndType(userId, contactType);

    let contactId;
    let dbContact;

    if (existingContact) {
      // Update existing contact
      console.log(
        "Updating existing contact:",
        existingContact.externalContactId
      );

      // Check if this is a fallback contact ID (doesn't exist in external API)
      const isFallbackContact =
        existingContact.externalContactId.startsWith("fallback-");

      if (!isFallbackContact) {
        try {
          // Update existing contact in external API
          console.log("🔄 Updating contact in external API...");
          const updateResult = await contactService.modifyContact(
            existingContact.externalContactId,
            validatedContactDetails
          );
          console.log(
            "✅ Successfully updated contact in external API:",
            updateResult
          );
        } catch (apiError) {
          console.error(
            "❌ Failed to update contact in external API:",
            apiError.message
          );
          // If the contact doesn't exist in external API, create a new one
          if (
            apiError.message.includes("Contact not found") ||
            apiError.response?.status === 404
          ) {
            console.log(
              "🔄 Contact not found in external API, creating new one..."
            );
            try {
              const createResult = await contactService.addContact(
                validatedContactDetails,
                customerId
              );

              // Extract contact ID from response
              const newContactId =
                createResult.contactId ||
                createResult["contact-id"] ||
                createResult.id ||
                createResult;

              if (
                newContactId &&
                (typeof newContactId === "string" ||
                  typeof newContactId === "number") &&
                !(
                  typeof newContactId === "string" &&
                  newContactId.startsWith("fallback-")
                )
              ) {
                existingContact.externalContactId = newContactId;
                console.log(
                  "✅ Created new contact in external API with ID:",
                  newContactId
                );
              } else {
                console.error(
                  "❌ Failed to get valid contact ID from create response:",
                  createResult
                );
                // Return error for invalid contact ID
                return res.status(500).json({
                  success: false,
                  error: "Failed to create contact in reseller API",
                  details: "Invalid contact ID received from API",
                  message: "Contact update failed. Please try again.",
                });
              }
            } catch (createError) {
              console.error(
                "❌ Failed to create new contact in external API:",
                createError.message
              );
              // Return error instead of continuing with fallback
              return res.status(500).json({
                success: false,
                error: "Failed to create contact in reseller API",
                details: createError.message,
                message:
                  "Contact update failed. Please check your contact details and try again.",
              });
            }
          } else {
            // Return error for other API failures
            return res.status(500).json({
              success: false,
              error: "Failed to update contact in reseller API",
              details: apiError.message,
              message: "Contact update failed. Please try again.",
            });
          }
        }
      } else {
        // This is a fallback contact, try to create it in the external API
        console.log(
          "🔄 Attempting to create fallback contact in external API..."
        );
        try {
          const createResult = await contactService.addContact(
            validatedContactDetails,
            customerId
          );

          // Extract contact ID from response
          const newContactId =
            createResult.contactId ||
            createResult["contact-id"] ||
            createResult.id ||
            createResult;

          if (
            newContactId &&
            (typeof newContactId === "string" ||
              typeof newContactId === "number") &&
            !(
              typeof newContactId === "string" &&
              newContactId.startsWith("fallback-")
            )
          ) {
            existingContact.externalContactId = newContactId;
            console.log(
              "✅ Successfully created contact in external API with ID:",
              newContactId
            );
          } else {
            console.error(
              "❌ Failed to get valid contact ID from response:",
              createResult
            );
            // Return error for invalid contact ID
            return res.status(500).json({
              success: false,
              error: "Failed to create contact in reseller API",
              details: "Invalid contact ID received from API",
              message: "Contact update failed. Please try again.",
            });
          }
        } catch (createError) {
          console.error(
            "❌ Failed to create contact in external API:",
            createError.message
          );
          // Return error instead of continuing with fallback
          return res.status(500).json({
            success: false,
            error: "Failed to create contact in reseller API",
            details: createError.message,
            message:
              "Contact update failed. Please check your contact details and try again.",
          });
        }
      }

      contactId = existingContact.externalContactId;

      // Update contact in database
      console.log("💾 Updating contact in local database...");
      existingContact.updateContactData(validatedContactDetails);
      existingContact.customerId = customerId;
      await existingContact.save();
      console.log(
        "✅ Contact updated in local database with ID:",
        existingContact._id
      );
      dbContact = existingContact;
    } else {
      // Create new contact in external API first
      console.log(
        "Creating new contact with details:",
        validatedContactDetails
      );
      console.log("Using customer ID:", customerId);

      try {
        const result = await contactService.addContact(
          validatedContactDetails,
          customerId
        );

        console.log("API response from addContact:", result);

        // Extract contact ID from various possible response formats
        contactId =
          result.contactId || result["contact-id"] || result.id || result;

        console.log("Extracted contact ID:", contactId);

        // Validate that we got a valid contact ID (can be string or number)
        if (
          !contactId ||
          contactId === undefined ||
          contactId === null ||
          (typeof contactId !== "string" && typeof contactId !== "number") ||
          (typeof contactId === "string" && contactId.startsWith("fallback-"))
        ) {
          throw new Error(
            "Failed to get valid contact ID from API response. Response: " +
              JSON.stringify(result)
          );
        }

        console.log(
          "Successfully created contact in external API with ID:",
          contactId
        );
      } catch (apiError) {
        console.error(
          "❌ Failed to create contact in external API:",
          apiError.message
        );
        console.error("Full error:", apiError);

        // Return error instead of creating fallback contact
        return res.status(500).json({
          success: false,
          error: "Failed to create contact in reseller API",
          details: apiError.message,
          message:
            "Contact creation failed. Please check your contact details and try again.",
        });
      }

      // Create contact in database with valid external contact ID
      console.log("💾 Creating contact in local database...");
      dbContact = new Contact({
        userId,
        externalContactId: contactId.toString(),
        contactType,
        name: validatedContactDetails.name,
        email: validatedContactDetails.email,
        company: validatedContactDetails.company || "",
        address: validatedContactDetails.address || "",
        city: validatedContactDetails.city || "",
        country: validatedContactDetails.country,
        zipcode: validatedContactDetails.zipcode,
        phoneCountryCode: validatedContactDetails.phoneCountryCode,
        phone: validatedContactDetails.phone,
        customerId: customerId,
        lastSyncedAt: new Date(),
      });
      await dbContact.save();
      console.log("✅ Contact saved to local database with ID:", dbContact._id);
    }

    // Update user's contact reference
    user.domainContacts[contactType] = dbContact._id;
    await user.save();

    res.json({
      success: true,
      message: `${contactType} contact ${
        existingContact ? "updated" : "created"
      } successfully`,
      contactId,
      contact: dbContact,
      customerId: customerId,
      isExternalContact: true, // Always true now since we don't create fallback contacts
    });
  } catch (error) {
    console.error("Error creating/updating domain contact:", error);
    res.status(500).json({
      error: "Failed to create/update domain contact",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Delete user's domain contact
 */
exports.deleteDomainContact = async (req, res) => {
  try {
    const userId = req.user._id;
    const { contactType } = req.params;

    // Validate contact type
    const validTypes = ["registrant", "admin", "tech", "billing"];
    if (!validTypes.includes(contactType)) {
      return res.status(400).json({ error: "Invalid contact type" });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Find contact in database
    const contact = await Contact.findByUserAndType(userId, contactType);
    if (!contact) {
      return res.status(404).json({ error: "Contact not found" });
    }

    // Check if this is a fallback contact ID (doesn't exist in external API)
    const isFallbackContact = contact.externalContactId.startsWith("fallback-");

    if (!isFallbackContact) {
      try {
        // Delete contact from external service
        await contactService.deleteContact(contact.externalContactId);
        console.log("Successfully deleted contact from external API");
      } catch (apiError) {
        console.error(
          "Failed to delete contact from external API:",
          apiError.message
        );
        // Continue with database deletion even if API fails
      }
    } else {
      console.log("Skipping external API deletion for fallback contact");
    }

    // Mark contact as deleted in database
    contact.status = "deleted";
    await contact.save();

    // Remove contact reference from user
    user.domainContacts[contactType] = null;
    await user.save();

    res.json({
      success: true,
      message: `${contactType} contact deleted successfully`,
    });
  } catch (error) {
    console.error("Error deleting domain contact:", error);
    res.status(500).json({
      error: "Failed to delete domain contact",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Copy contact details from one type to another
 */
exports.copyDomainContact = async (req, res) => {
  try {
    const userId = req.user._id;
    const { fromType, toType } = req.body;

    // Always use the company customer ID from environment
    const customerId = process.env.COMPANY_CUSTOMER_ID;

    if (!customerId) {
      return res.status(500).json({
        error:
          "Company customer ID not configured. Please contact administrator.",
      });
    }

    // Validate contact types
    const validTypes = ["registrant", "admin", "tech", "billing"];
    if (!validTypes.includes(fromType) || !validTypes.includes(toType)) {
      return res.status(400).json({ error: "Invalid contact type" });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Find source contact in database
    const sourceContact = await Contact.findByUserAndType(userId, fromType);
    if (!sourceContact) {
      return res
        .status(404)
        .json({ error: `Source ${fromType} contact not found` });
    }

    // Check if target contact already exists
    let targetContact = await Contact.findByUserAndType(userId, toType);

    // Get source contact API data and validate it
    const rawContactData = sourceContact.getApiData();
    const validatedContactData = {
      ...rawContactData,
      country: getCountryCode(rawContactData.country || ""),
      zipcode: formatZipcode(
        rawContactData.zipcode || "",
        getCountryCode(rawContactData.country || "")
      ),
      phoneCountryCode: (rawContactData.phoneCountryCode || "").replace(
        /[^0-9]/g,
        ""
      ),
      phone: (rawContactData.phone || "").replace(/[^0-9]/g, ""),
    };

    let newContactId;
    let dbContact;

    if (targetContact) {
      // Update existing target contact
      console.log(
        "Updating existing target contact:",
        targetContact.externalContactId
      );

      // Check if this is a fallback contact ID (doesn't exist in external API)
      const isFallbackContact =
        targetContact.externalContactId.startsWith("fallback-");

      if (!isFallbackContact) {
        try {
          // Update existing target contact
          const updateResult = await contactService.modifyContact(
            targetContact.externalContactId,
            validatedContactData
          );
          console.log(
            "Successfully updated target contact in external API:",
            updateResult
          );
        } catch (apiError) {
          console.error(
            "Failed to update target contact in external API:",
            apiError.message
          );
          // If the contact doesn't exist in external API, create a new one
          if (
            apiError.message.includes("Contact not found") ||
            apiError.response?.status === 404
          ) {
            console.log(
              "Target contact not found in external API, creating new one..."
            );
            try {
              const createResult = await contactService.addContact(
                validatedContactData,
                customerId
              );

              // Extract contact ID from response
              const newContactId =
                createResult.contactId ||
                createResult["contact-id"] ||
                createResult.id ||
                createResult;

              if (
                newContactId &&
                (typeof newContactId === "string" ||
                  typeof newContactId === "number") &&
                !(
                  typeof newContactId === "string" &&
                  newContactId.startsWith("fallback-")
                )
              ) {
                targetContact.externalContactId = newContactId;
                console.log(
                  "Created new target contact in external API with ID:",
                  newContactId
                );
              }
            } catch (createError) {
              console.error(
                "Failed to create new target contact in external API:",
                createError.message
              );
            }
          }
        }
      } else {
        // This is a fallback contact, try to create it in the external API
        console.log(
          "Attempting to create fallback target contact in external API..."
        );
        try {
          const createResult = await contactService.addContact(
            validatedContactData,
            customerId
          );

          // Extract contact ID from response
          const newContactId =
            createResult.contactId ||
            createResult["contact-id"] ||
            createResult.id ||
            createResult;

          if (
            newContactId &&
            (typeof newContactId === "string" ||
              typeof newContactId === "number") &&
            !(
              typeof newContactId === "string" &&
              newContactId.startsWith("fallback-")
            )
          ) {
            targetContact.externalContactId = newContactId;
            console.log(
              "Successfully created target contact in external API with ID:",
              newContactId
            );
          }
        } catch (createError) {
          console.error(
            "Failed to create target contact in external API:",
            createError.message
          );
        }
      }

      newContactId = targetContact.externalContactId;

      // Update in database
      targetContact.updateContactData(validatedContactData);
      targetContact.customerId = customerId;
      await targetContact.save();
      dbContact = targetContact;
    } else {
      // Create new contact in external API
      console.log(
        "Creating new target contact with details:",
        validatedContactData
      );
      console.log("Using customer ID:", customerId);

      try {
        const result = await contactService.addContact(
          validatedContactData,
          customerId
        );

        // Extract contact ID from response
        newContactId =
          result.contactId || result["contact-id"] || result.id || result;

        // Validate that we got a valid contact ID (can be string or number)
        if (
          !newContactId ||
          newContactId === undefined ||
          newContactId === null ||
          (typeof newContactId !== "string" &&
            typeof newContactId !== "number") ||
          (typeof newContactId === "string" &&
            newContactId.startsWith("fallback-"))
        ) {
          throw new Error("Failed to get valid contact ID from API response");
        }

        console.log(
          "Successfully created target contact in external API with ID:",
          newContactId
        );
      } catch (apiError) {
        console.error(
          "❌ Failed to create contact in external API during copy:",
          apiError.message
        );

        // Return error instead of creating fallback contact
        return res.status(500).json({
          success: false,
          error: "Failed to create contact in reseller API during copy",
          details: apiError.message,
          message:
            "Contact copy failed. Please check your contact details and try again.",
        });
      }

      // Create new contact in database
      console.log("💾 Creating copied contact in local database...");
      dbContact = new Contact({
        userId,
        externalContactId: newContactId.toString(),
        contactType: toType,
        name: validatedContactData.name,
        email: validatedContactData.email,
        company: validatedContactData.company,
        address: validatedContactData.address,
        city: validatedContactData.city,
        country: validatedContactData.country,
        zipcode: validatedContactData.zipcode,
        phoneCountryCode: validatedContactData.phoneCountryCode,
        phone: validatedContactData.phone,
        customerId: customerId,
        lastSyncedAt: new Date(),
      });
      await dbContact.save();
      console.log(
        "✅ Copied contact saved to local database with ID:",
        dbContact._id
      );
    }

    // Update user's contact reference
    user.domainContacts[toType] = dbContact._id;
    await user.save();

    res.json({
      success: true,
      message: `${fromType} contact copied to ${toType} successfully`,
      contactId: newContactId,
      contact: dbContact,
      customerId: customerId,
      isExternalContact: true, // Always true now since we don't create fallback contacts
    });
  } catch (error) {
    console.error("Error copying domain contact:", error);
    res.status(500).json({
      error: "Failed to copy domain contact",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Update existing domain contact (separate from create/update)
 */
exports.updateDomainContact = async (req, res) => {
  try {
    const userId = req.user._id;
    const { contactType } = req.params;
    const { contactDetails } = req.body;

    // Always use the company customer ID from environment
    const customerId = process.env.COMPANY_CUSTOMER_ID;

    if (!customerId) {
      return res.status(500).json({
        error:
          "Company customer ID not configured. Please contact administrator.",
      });
    }

    // Validate contact type
    const validTypes = ["registrant", "admin", "tech", "billing"];
    if (!validTypes.includes(contactType)) {
      return res.status(400).json({ error: "Invalid contact type" });
    }

    // Validate required fields
    if (!contactDetails || !contactDetails.name || !contactDetails.email) {
      return res.status(400).json({
        error: "Contact details with name and email are required",
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Find existing contact
    const existingContact = await Contact.findByUserAndType(
      userId,
      contactType
    );
    if (!existingContact) {
      return res.status(404).json({
        error: `${contactType} contact not found. Please create it first.`,
      });
    }

    // Validate and format contact data
    const validatedContactDetails = {
      ...contactDetails,
      country: getCountryCode(contactDetails.country || ""),
      zipcode: formatZipcode(
        contactDetails.zipcode || "",
        getCountryCode(contactDetails.country || "")
      ),
      phoneCountryCode: (contactDetails.phoneCountryCode || "").replace(
        /[^0-9]/g,
        ""
      ),
      phone: (contactDetails.phone || "").replace(/[^0-9]/g, ""),
    };

    console.log(
      "🔄 Updating existing contact:",
      existingContact.externalContactId
    );

    // Update contact in external API
    try {
      const updateResult = await contactService.modifyContact(
        existingContact.externalContactId,
        validatedContactDetails
      );
      console.log(
        "✅ Successfully updated contact in external API:",
        updateResult
      );
    } catch (apiError) {
      console.error(
        "❌ Failed to update contact in external API:",
        apiError.message
      );

      // Return error for API failures
      return res.status(500).json({
        success: false,
        error: "Failed to update contact in reseller API",
        details: apiError.message,
        message:
          "Contact update failed. Please check your contact details and try again.",
      });
    }

    // Update contact in database
    console.log("💾 Updating contact in local database...");
    existingContact.updateContactData(validatedContactDetails);
    existingContact.customerId = customerId;
    await existingContact.save();
    console.log(
      "✅ Contact updated in local database with ID:",
      existingContact._id
    );

    res.json({
      success: true,
      message: `${contactType} contact updated successfully`,
      contactId: existingContact.externalContactId,
      contact: existingContact,
      customerId: customerId,
    });
  } catch (error) {
    console.error("Error updating domain contact:", error);
    res.status(500).json({
      error: "Failed to update domain contact",
      details: error.response?.data || error.message,
    });
  }
};
