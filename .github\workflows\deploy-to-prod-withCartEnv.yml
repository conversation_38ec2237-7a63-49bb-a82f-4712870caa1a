name: Deploy ztechEngineering to PROD WITH CART Environment

on:
  push:
    branches: [prodwithcart]

jobs:
  deploy:
    runs-on: ["self-hosted", "ztech-nextjs"]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          # cache: "npm"
          # cache-dependency-path: "**/package-lock.json"

      - name: Install frontend modules
        run: |
          cd frontend
          npm ci
          npm list # Debug: Show installed dependencies

      - name: Build the frontend
        run: |
          cd frontend
          npm run build
          sync # Ensure file writes are complete
          ls -la .next # Debug: Verify build output

      - name: Install backend modules
        run: |
          cd backend
          npm ci
          npm list # Debug: Show installed dependencies

      - name: Write .env for backend
        run: |
          cd backend
          echo "${{ secrets.PROD_ENV_FILE_CONTENT_18_06_2025}}" > .env
          chmod 600 .env # Secure permissions
          ls -la .env # Debug: Verify .env file

      - name: Start frontend with pm2 (port 3001)
        run: |
          cd frontend
          pm2 delete ztech-frontend || true
          pm2 start npm --name ztech-frontend -- start -- --port=3001
          pm2 list # Debug: Show pm2 processes

      - name: Start backend with pm2 (port 5002)
        run: |
          cd backend
          pm2 delete ztech-backend || true
          pm2 start npm --name ztech-backend -- start -- --port=5002
          pm2 save
          pm2 list # Debug: Show pm2 processes
