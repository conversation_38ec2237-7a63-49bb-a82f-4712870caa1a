"use client";
import React, { useEffect, useState, useCallback } from "react";
import { Typography } from "@material-tailwind/react";
import { toast } from "react-toastify";
import { useTranslations } from "next-intl";
import { useAuth } from "@/app/context/AuthContext";
import cartService from "@/app/services/cartService";
import orderService from "@/app/services/orderService";
import paymentService from "@/app/services/paymentService";
import BillingInfoForm from "@/components/cart2/billingInfoForm";
import CartItemsList from "@/components/cart/cartItemsList";
import Summary from "@/components/cart2/summary";
import { useSearchParams } from "next/navigation";
import PaymentStatusModal from "@/components/order/paymentStatusModal";

function CartPage() {
  const t = useTranslations("client");
  const { cartCount, setCartCount } = useAuth();
  const [cartData, setCartData] = useState({});
  const [domainPrivacySettings, setDomainPrivacySettings] = useState({}); // Track privacy protection for each domain

  const [orderId, setOrderId] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState(null);

  const searchParams = useSearchParams();
  const status = searchParams.get("status");
  const item = searchParams.get("item");

  const [orderLoading, setOrderLoading] = useState(false);
  const [billingInfo, setBillingInfo] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    country: "",
    password: "",
    confirmPassword: "",
  });

  useEffect(() => {
    if (status && item) {
      setPaymentStatus(status);
      setOrderId(item);
      setOpenModal(true);
    }
  }, [status, item]);

  const closeModal = () => {
    setOpenModal(false);
  };

  const fetchCartData = async () => {
    try {
      const response = await cartService.getCart();
      if (response.data.success) {
        setCartData(response.data.cart);
        setCartCount(response.data.cart.cartCount);
      }
      console.log("Cart data e:", response.data.cart);
    } catch (error) {
      console.error("Error fetching cart data:", error);
    }
  };
  useEffect(() => {
    fetchCartData();
    console.log("fetchCartData: ", cartCount);
  }, []);

  const handleQuantityChange = async (itemId, change, quantity, period) => {
    console.log("Quantity reashed to maximum");
    try {
      const service =
        change > 0 ? cartService.addItemToCart : cartService.removeItemFromCart;
      console.log("in handleQuantityChange", itemId, quantity, change, period);
      const response = await service({
        packageId: itemId,
        quantity,
        period,
      });
      // setCartData(response.data?.cart);
      setCartCount(response.data?.cart.cartCount);
      // Return success message to child
      return { success: true };
    } catch (error) {
      // Return error message to child if there's an issue
      return { success: false, message: error.response.data.message };
    }
  };

  const handlePeriodChange = async (itemId, period, isDomain = false) => {
    try {
      if (isDomain) {
        // Handle domain period change
        const response = await cartService.updateDomainPeriod({
          itemId,
          period,
        });
        setCartData(response.data?.cart);
      } else {
        // Handle package period change (existing code)
        const response = await cartService.updateItemPeriod({
          packageId: itemId,
          period,
        });
        setCartData(response.data?.cart);
      }
      setCartCount(0); // Trigger cart count update
    } catch (error) {
      console.error("Error updating period:", error);
      // Re-throw the error so child components can handle it
      throw error;
    }
  };

  const handleRemove = async (itemId) => {
    try {
      // Re-fetch cart data after item removal
      await fetchCartData();
      setCartCount(0); // Trigger cart count update
    } catch (error) {
      console.error("Error refreshing cart after removal:", error);
    }
  };

  const handlePrivacyChange = useCallback((itemId, privacyProtection) => {
    setDomainPrivacySettings((prev) => ({
      ...prev,
      [itemId]: privacyProtection,
    }));
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setBillingInfo((prev) => ({ ...prev, [name]: value }));
  };

  const handlePlaceOrder = async () => {
    if (!billingInfo || Object.keys(billingInfo).length === 0) {
      toast.error(t("billing_missing"));
      return;
    }
    setOrderLoading(true);

    try {
      // Update domain privacy settings in cart before placing order
      for (const [itemId, privacyProtection] of Object.entries(
        domainPrivacySettings
      )) {
        try {
          await cartService.updateDomainOptions({
            itemId,
            privacyProtection,
            autoRenew: false, // Always false as per requirement
          });
        } catch (error) {
          console.error(`Error updating privacy for item ${itemId}:`, error);
          // Continue with other items even if one fails
        }
      }

      console.log("Placing order with:", billingInfo);
      const res = await orderService.createOrder(billingInfo);
      console.log("Order created successfully:", res.data);

      setCartData({});
      setCartCount(0);

      const orderBillingInfo = res.data.order.billingInfo;
      const data = {
        BillToName: orderBillingInfo.BillToName,
        email: orderBillingInfo.email,
        tel: orderBillingInfo.phone,
        address: orderBillingInfo.address,
        country: orderBillingInfo.country,
        amount: res.data.order.totalPrice,
        orderId: res.data.order._id,
        customerId:
          res.data.order.user?.identifiant || res.data.order.user?._id,
      };
      console.log("🚀 ~ handlePlaceOrder ~ data:", data);

      try {
        const resPayment = await paymentService.initiatePayment(data);
        // console.log("Payment initiated:", resPayment.data);
        // Execute the form in the current window
        executePaymentForm(resPayment.data);
      } catch (paymentError) {
        console.error("Error initiating payment:", paymentError);
        toast.error(t("payment_failed"));
      }
    } catch (error) {
      if (error.response?.data?.cartIsEmpty) {
        console.error("Error creating order:", error.response.data.message);
        toast.error(error.response.data.message);
      } else {
        console.error("Error creating order:", error.response.data);
        toast.error(t("order_creation_failed"));
      }
    } finally {
      setOrderLoading(false);
    }
  };

  const executePaymentForm = (formHTML) => {
    try {
      console.log("Executing Payment Form:", formHTML);

      const formContainer = document.createElement("div");
      formContainer.innerHTML = formHTML;

      const form = formContainer.querySelector("form");
      if (!form) {
        console.error("Form not found in the provided HTML!");
        return;
      }

      document.body.appendChild(form);
      form.submit();

      setTimeout(() => {
        form.remove();
      }, 1000);
    } catch (error) {
      console.error("Error executing payment form:", error);
    }
  };

  return (
    <div className="p-2 min-h-screen max-w-screen-2xl mx-auto">
      <div className="w-full px-2 md:px-4 md:py-8 pt-4 flex flex-col">
        <Typography variant="h1" className="text-xl font-semibold mb-4">
          {t("cart_checkout")}
        </Typography>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3 w-full justify-between mx-auto">
          {/* Left side: Billing form and Cart Items */}
          <div className="md:col-span-2 space-y-6">
            <BillingInfoForm
              billingInfo={billingInfo}
              setBillingInfo={setBillingInfo}
              onInputChange={handleInputChange}
              t={t}
            />
            <CartItemsList
              cartItems={cartData?.items || []}
              onQuantityChange={handleQuantityChange}
              onPeriodChange={handlePeriodChange}
              onRemove={handleRemove}
              t={t}
              onPrivacyChange={handlePrivacyChange}
            />
          </div>

          {/* Right side: Summary */}
          <div className="relative max-w-md mx-auto">
            <div className="md:sticky md:top-8">
              <Summary
                totalPrice={cartData?.totalPrice}
                totalDiscount={cartData?.totalDiscount}
                onPlaceOrder={handlePlaceOrder}
                orderLoading={orderLoading}
                t={t}
              />
            </div>
          </div>
        </div>
      </div>

      {openModal && (
        <PaymentStatusModal
          status={paymentStatus}
          orderId={orderId}
          onClose={closeModal}
          t={t}
        />
      )}
    </div>
  );
}

export default CartPage;
