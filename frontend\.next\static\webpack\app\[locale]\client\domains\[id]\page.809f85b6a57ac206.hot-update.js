"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx":
/*!*******************************************************!*\
  !*** ./src/app/[locale]/client/domains/[id]/page.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/domains/NameserverManager */ \"(app-pages-browser)/./src/components/domains/NameserverManager.jsx\");\n/* harmony import */ var _components_domains_PrivacyProtectionManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/domains/PrivacyProtectionManager */ \"(app-pages-browser)/./src/components/domains/PrivacyProtectionManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction DomainDetailPage(param) {\n    let { params } = param;\n    _s();\n    const { id } = params;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"client.domainWrapper\");\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [apiError, setApiError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getDomainDetails = async ()=>{\n            try {\n                var _domainsRes_data;\n                setLoading(true);\n                // First, get the user's domains to find the domain name by ID\n                const domainsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getUserDomains();\n                const userDomains = ((_domainsRes_data = domainsRes.data) === null || _domainsRes_data === void 0 ? void 0 : _domainsRes_data.domains) || [];\n                // Find the domain with the matching ID\n                const userDomain = userDomains.find((d)=>d.id === id);\n                if (!userDomain) {\n                    console.error(\"Domain not found with ID:\", id);\n                    setLoading(false);\n                    return;\n                }\n                console.log(\"Found user domain:\", userDomain);\n                // Get comprehensive domain details from the registration system API\n                try {\n                    var _detailsRes_data;\n                    // Use the domains/details-by-name.json endpoint with comprehensive options\n                    const detailsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getDomainDetailsByName(userDomain.name, \"All\" // Get all available details including OrderDetails, ContactIds, NsDetails, DomainStatus, etc.\n                    );\n                    console.log(\"Domain details from registration API:\", detailsRes.data);\n                    const apiDomain = ((_detailsRes_data = detailsRes.data) === null || _detailsRes_data === void 0 ? void 0 : _detailsRes_data.domain) || {};\n                    // Parse nameservers from API response\n                    const nameservers = [];\n                    if (apiDomain.ns1) nameservers.push(apiDomain.ns1);\n                    if (apiDomain.ns2) nameservers.push(apiDomain.ns2);\n                    if (apiDomain.ns3) nameservers.push(apiDomain.ns3);\n                    if (apiDomain.ns4) nameservers.push(apiDomain.ns4);\n                    // Parse contact details from API response\n                    const contacts = {\n                        registrant: apiDomain.registrantcontact || {\n                            name: \"Not available\",\n                            email: \"Not available\",\n                            phone: \"Not available\",\n                            address: \"Not available\"\n                        },\n                        admin: apiDomain.admincontact || {\n                            name: \"Not available\",\n                            email: \"Not available\",\n                            phone: \"Not available\",\n                            address: \"Not available\"\n                        },\n                        tech: apiDomain.techcontact || {\n                            name: \"Not available\",\n                            email: \"Not available\",\n                            phone: \"Not available\",\n                            address: \"Not available\"\n                        },\n                        billing: apiDomain.billingcontact || {\n                            name: \"Not available\",\n                            email: \"Not available\",\n                            phone: \"Not available\",\n                            address: \"Not available\"\n                        }\n                    };\n                    // Format dates from API response\n                    const formatDate = (timestamp)=>{\n                        if (!timestamp) return \"Not available\";\n                        try {\n                            return new Date(timestamp * 1000).toLocaleDateString();\n                        } catch (e) {\n                            return timestamp;\n                        }\n                    };\n                    // Combine user domain data with comprehensive API details\n                    const combinedDomain = {\n                        id: userDomain.id,\n                        name: userDomain.name,\n                        // Use API status if available, fallback to user status\n                        status: apiDomain.currentstatus || userDomain.status || \"unknown\",\n                        registrationDate: formatDate(apiDomain.creationtime) || userDomain.registrationDate || \"Unknown\",\n                        expiryDate: formatDate(apiDomain.endtime) || userDomain.expiryDate || \"Unknown\",\n                        autoRenew: apiDomain.recurring === \"true\" || userDomain.autoRenew || false,\n                        registrar: userDomain.registrar || \"ZTech Domains\",\n                        nameservers: nameservers.length > 0 ? nameservers : userDomain.nameservers || [\n                            \"No nameservers configured\"\n                        ],\n                        privacyProtection: apiDomain.isprivacyprotected === \"true\" || userDomain.privacyProtection || false,\n                        period: userDomain.period || 1,\n                        price: userDomain.price || \"N/A\",\n                        orderId: apiDomain.orderid || userDomain.orderId || \"N/A\",\n                        orderStatus: apiDomain.orderstatus || userDomain.orderStatus || \"unknown\",\n                        // Additional API details\n                        productCategory: apiDomain.productcategory || \"Domain\",\n                        productKey: apiDomain.productkey || \"N/A\",\n                        customerId: apiDomain.customerid || \"N/A\",\n                        domainSecret: apiDomain.domsecret || \"N/A\",\n                        raaVerificationStatus: apiDomain.raaVerificationStatus || \"Unknown\",\n                        gdprProtection: apiDomain.gdpr || {\n                            enabled: \"false\",\n                            eligible: \"false\"\n                        },\n                        privacyProtectionExpiry: apiDomain.privacyprotectendtime ? formatDate(apiDomain.privacyprotectendtime) : \"N/A\",\n                        isOrderSuspendedUponExpiry: apiDomain.isOrderSuspendedUponExpiry === \"true\",\n                        orderSuspendedByParent: apiDomain.orderSuspendedByParent === \"true\",\n                        allowDeletion: apiDomain.allowdeletion === \"true\",\n                        contacts,\n                        // DNS records placeholder (would need separate API call)\n                        dnsRecords: [\n                            {\n                                id: \"rec1\",\n                                type: \"A\",\n                                name: \"@\",\n                                content: \"Use DNS management to view/edit records\",\n                                ttl: 3600\n                            }\n                        ]\n                    };\n                    setDomain(combinedDomain);\n                } catch (apiError) {\n                    console.warn(\"Could not fetch domain details from API:\", apiError);\n                    // Fallback to user domain data only\n                    const fallbackDomain = {\n                        id: userDomain.id,\n                        name: userDomain.name,\n                        status: userDomain.status,\n                        registrationDate: userDomain.registrationDate,\n                        expiryDate: userDomain.expiryDate,\n                        autoRenew: userDomain.autoRenew,\n                        registrar: userDomain.registrar || \"ZTech Domains\",\n                        nameservers: userDomain.nameservers || [\n                            \"moha1280036.earth.orderbox-dns.com\",\n                            \"moha1280036.mars.orderbox-dns.com\",\n                            \"moha1280036.mercury.orderbox-dns.com\",\n                            \"moha1280036.venus.orderbox-dns.com\"\n                        ],\n                        privacyProtection: userDomain.privacyProtection,\n                        period: userDomain.period,\n                        price: userDomain.price,\n                        orderId: userDomain.orderId,\n                        orderStatus: userDomain.orderStatus,\n                        contacts: {\n                            registrant: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            admin: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            tech: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            billing: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            }\n                        },\n                        dnsRecords: [\n                            {\n                                id: \"rec1\",\n                                type: \"A\",\n                                name: \"@\",\n                                content: \"DNS information not available\",\n                                ttl: 3600\n                            }\n                        ]\n                    };\n                    setDomain(fallbackDomain);\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error getting domain details\", error);\n                setLoading(false);\n            }\n        };\n        getDomainDetails();\n    }, [\n        id\n    ]);\n    const handleAutoRenewToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.toggleAutoRenewal(id, value);\n            setDomain({\n                ...domain,\n                autoRenew: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling auto renewal\", error);\n        }\n    };\n    const handlePrivacyToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.togglePrivacyProtection(id, value);\n            setDomain({\n                ...domain,\n                privacyProtection: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling privacy protection\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-6 w-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-gray-600\",\n                        children: [\n                            t(\"loading\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 252,\n            columnNumber: 7\n        }, this);\n    }\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                    variant: \"h4\",\n                    className: \"text-gray-800 font-bold mb-2\",\n                    children: t(\"domain_not_found\", {\n                        defaultValue: \"Domain Not Found\"\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    className: \"mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"text\",\n                    className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                            variant: \"h1\",\n                                            className: \"text-2xl font-bold text-gray-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize mr-2 \".concat(domain.status === \"active\" ? \"bg-green-100 text-green-800\" : domain.status === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : domain.status === \"expired\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: dt(domain.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        dt(\"registrar\"),\n                                                        \": \",\n                                                        domain.registrar\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outlined\",\n                                    className: \"border-blue-600 text-blue-600 hover:bg-blue-50 flex items-center gap-2\",\n                                    onClick: ()=>window.open(\"http://\".concat(domain.name), \"_blank\"),\n                                    children: [\n                                        t(\"visit_website\", {\n                                            defaultValue: \"Visit Website\"\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                    onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/renew\")),\n                                    children: [\n                                        dt(\"renew_domain\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                    value: activeTab,\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsHeader, {\n                            className: \"bg-gray-100 rounded-lg p-1\",\n                            indicatorProps: {\n                                className: \"bg-white shadow-md rounded-md\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"overview\",\n                                    onClick: ()=>setActiveTab(\"overview\"),\n                                    className: activeTab === \"overview\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"overview\", {\n                                        defaultValue: \"Overview\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"dns\",\n                                    onClick: ()=>setActiveTab(\"dns\"),\n                                    className: activeTab === \"dns\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"dns_settings\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"contacts\",\n                                    onClick: ()=>setActiveTab(\"contacts\"),\n                                    className: activeTab === \"contacts\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"domain_contacts\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"privacy\",\n                                    onClick: ()=>setActiveTab(\"privacy\"),\n                                    className: activeTab === \"privacy\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"privacy\", {\n                                        defaultValue: \"Privacy\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsBody, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"overview\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"domain_details\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"domain_name\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 390,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"status\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 398,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize \".concat(domain.status === \"active\" ? \"bg-green-100 text-green-800\" : domain.status === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : domain.status === \"expired\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                            children: dt(domain.status)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 401,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 397,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"registration_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 415,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: new Date(domain.registrationDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"expiry_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 425,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: new Date(domain.expiryDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"auto_renew\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 433,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                            checked: domain.autoRenew,\n                                                                            onChange: (e)=>handleAutoRenewToggle(e.target.checked),\n                                                                            color: \"blue\",\n                                                                            disabled: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"whois_privacy\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                            checked: domain.privacyProtection,\n                                                                            onChange: (e)=>handlePrivacyToggle(e.target.checked),\n                                                                            color: \"blue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 449,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                domain.orderId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Order ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 459,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-xs\",\n                                                                            children: domain.orderId\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 462,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.customerId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Customer ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 469,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-xs\",\n                                                                            children: domain.customerId\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.raaVerificationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"RAA Verification\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 479,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(domain.raaVerificationStatus === \"Verified\" ? \"bg-green-100 text-green-800\" : domain.raaVerificationStatus === \"Pending\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                            children: domain.raaVerificationStatus\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 482,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.gdprProtection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"GDPR Protection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(domain.gdprProtection.enabled === \"true\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                            children: domain.gdprProtection.enabled === \"true\" ? \"Enabled\" : \"Disabled\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"nameservers\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                domain.nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"NS \",\n                                                                                    index + 1\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 524,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"font-medium\",\n                                                                                children: ns\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 527,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 520,\n                                                                        columnNumber: 25\n                                                                    }, this)),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outlined\",\n                                                                        className: \"w-full border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                                                        onClick: ()=>setActiveTab(\"dns\"),\n                                                                        children: dt(\"update_nameservers\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 531,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"dns\",\n                                    className: \"p-0 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                domain: domain,\n                                                onUpdate: (updatedDomain)=>setDomain(updatedDomain)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: dt(\"manage_dns_records\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                                onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/add\")),\n                                                                children: t(\"add_record\", {\n                                                                    defaultValue: \"Add Record\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"bg-gray-50 border-b border-gray-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"type\", {\n                                                                                    defaultValue: \"Type\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 574,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"name\", {\n                                                                                    defaultValue: \"Name\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 577,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"content\", {\n                                                                                    defaultValue: \"Content\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 580,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"ttl\", {\n                                                                                    defaultValue: \"TTL\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 583,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-500\",\n                                                                                children: t(\"actions\", {\n                                                                                    defaultValue: \"Actions\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 586,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 573,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"divide-y divide-gray-200\",\n                                                                    children: domain.dnsRecords.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            className: \"hover:bg-gray-50\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm font-medium text-gray-900\",\n                                                                                    children: record.type\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 594,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 597,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.content\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 600,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.ttl\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 603,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-right\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        size: \"sm\",\n                                                                                        variant: \"text\",\n                                                                                        className: \"text-blue-600\",\n                                                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/\").concat(record.id)),\n                                                                                        children: t(\"edit\", {\n                                                                                            defaultValue: \"Edit\"\n                                                                                        })\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 607,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 606,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, record.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 593,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"contacts\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: dt(\"domain_contacts\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"registrant\", {\n                                                                        defaultValue: \"Registrant Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 638,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.registrant.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.registrant.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 647,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.registrant.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 650,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.registrant.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 653,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 643,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"admin\", {\n                                                                        defaultValue: \"Administrative Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.admin.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 663,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.admin.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 666,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.admin.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 669,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.admin.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 672,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"technical\", {\n                                                                        defaultValue: \"Technical Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.tech.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.tech.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.tech.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.tech.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"billing\", {\n                                                                        defaultValue: \"Billing Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.billing.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 701,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.billing.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 704,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.billing.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 707,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.billing.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 710,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 700,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/contacts\")),\n                                                        children: dt(\"update_contacts\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"privacy\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: t(\"privacy\", {\n                                                        defaultValue: \"Privacy Protection\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: t(\"privacy_content_coming_soon\", {\n                                                                defaultValue: \"Privacy protection settings will be available soon.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: t(\"privacy_description\", {\n                                                                defaultValue: \"Manage your domain privacy protection and WHOIS information visibility.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 745,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 284,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n        lineNumber: 283,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainDetailPage, \"+EnspOi5i8G7ORbhH3TPg4wkUtU=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DomainDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DomainDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx\n"));

/***/ })

});