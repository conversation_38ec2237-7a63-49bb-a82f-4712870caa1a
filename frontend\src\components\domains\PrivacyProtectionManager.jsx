"use client";
import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Card<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Dialog<PERSON>eader,
  DialogBody,
  DialogFooter,
} from "@material-tailwind/react";
import {
  Shield,
  Eye,
  EyeOff,
  AlertCircle,
  CheckCircle,
  Info,
} from "lucide-react";
import domainMngService from "@/app/services/domainMngService";
import { toast } from "react-toastify";

export default function PrivacyProtectionManager({ domain, onUpdate }) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState(null); // 'enable' or 'disable'

  const handleTogglePrivacy = (enabled) => {
    setPendingAction(enabled ? 'enable' : 'disable');
    setShowConfirmDialog(true);
  };

  const confirmPrivacyChange = async () => {
    if (!pendingAction) return;

    try {
      setIsUpdating(true);
      setShowConfirmDialog(false);

      const requestData = {
        domainName: domain.name,
        orderId: domain.orderId,
      };

      console.log(`${pendingAction === 'enable' ? 'Enabling' : 'Disabling'} privacy protection:`, requestData);

      let response;
      if (pendingAction === 'enable') {
        response = await domainMngService.enablePrivacyProtection(requestData);
      } else {
        response = await domainMngService.disablePrivacyProtection(requestData);
      }
      
      if (response.data.success) {
        const newPrivacyStatus = pendingAction === 'enable';
        toast.success(
          `Privacy protection ${newPrivacyStatus ? 'enabled' : 'disabled'} successfully`
        );
        
        // Call onUpdate callback if provided
        if (onUpdate) {
          onUpdate({
            ...domain,
            privacyProtection: newPrivacyStatus,
          });
        }
      } else {
        throw new Error(response.data.error || `Failed to ${pendingAction} privacy protection`);
      }
    } catch (error) {
      console.error(`Error ${pendingAction}ing privacy protection:`, error);
      toast.error(
        error.response?.data?.error || 
        error.message || 
        `Failed to ${pendingAction} privacy protection`
      );
    } finally {
      setIsUpdating(false);
      setPendingAction(null);
    }
  };

  const cancelPrivacyChange = () => {
    setShowConfirmDialog(false);
    setPendingAction(null);
  };

  return (
    <>
      <Card>
        <CardBody>
          <div className="flex items-center gap-2 mb-4">
            <Shield className="h-5 w-5 text-blue-600" />
            <Typography variant="h5" className="text-gray-800">
              Privacy Protection
            </Typography>
          </div>

          <Typography className="text-gray-600 mb-6">
            Privacy protection hides your personal contact information from public WHOIS databases, 
            protecting you from spam and unwanted solicitations.
          </Typography>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg mb-4">
            <div className="flex items-center gap-3">
              {domain?.privacyProtection ? (
                <EyeOff className="h-5 w-5 text-green-600" />
              ) : (
                <Eye className="h-5 w-5 text-red-600" />
              )}
              <div>
                <Typography className="font-semibold text-gray-800">
                  Privacy Protection
                </Typography>
                <Typography className="text-sm text-gray-600">
                  {domain?.privacyProtection 
                    ? "Your contact information is hidden from public view"
                    : "Your contact information is publicly visible"
                  }
                </Typography>
              </div>
            </div>
            
            <Switch
              checked={domain?.privacyProtection || false}
              onChange={handleTogglePrivacy}
              disabled={isUpdating}
              color="green"
            />
          </div>

          {domain?.privacyProtection ? (
            <Alert color="green" className="mb-4">
              <CheckCircle className="h-4 w-4" />
              <div>
                <Typography className="font-semibold">Privacy Protection Active</Typography>
                <Typography className="text-sm">
                  Your personal information is protected and hidden from public WHOIS searches.
                </Typography>
              </div>
            </Alert>
          ) : (
            <Alert color="amber" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <div>
                <Typography className="font-semibold">Privacy Protection Disabled</Typography>
                <Typography className="text-sm">
                  Your contact information is publicly visible in WHOIS databases. 
                  Consider enabling privacy protection to protect your personal data.
                </Typography>
              </div>
            </Alert>
          )}

          <Alert color="blue" className="mb-4">
            <Info className="h-4 w-4" />
            <div>
              <Typography className="font-semibold">Important Information:</Typography>
              <ul className="text-sm mt-1 space-y-1">
                <li>• Privacy protection may take a few hours to take effect</li>
                <li>• Some TLDs may not support privacy protection</li>
                <li>• You can enable or disable this feature at any time</li>
                <li>• Privacy protection does not affect domain functionality</li>
              </ul>
            </div>
          </Alert>

          {isUpdating && (
            <div className="flex items-center gap-2 text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <Typography className="text-sm">
                Updating privacy protection settings...
              </Typography>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} handler={cancelPrivacyChange}>
        <DialogHeader>
          <div className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            {pendingAction === 'enable' ? 'Enable' : 'Disable'} Privacy Protection
          </div>
        </DialogHeader>
        <DialogBody>
          <div className="space-y-4">
            <Typography>
              Are you sure you want to {pendingAction === 'enable' ? 'enable' : 'disable'} privacy 
              protection for <strong>{domain?.name}</strong>?
            </Typography>
            
            {pendingAction === 'enable' ? (
              <Alert color="green">
                <CheckCircle className="h-4 w-4" />
                <div>
                  <Typography className="font-semibold">Enabling Privacy Protection</Typography>
                  <Typography className="text-sm">
                    Your personal contact information will be hidden from public WHOIS databases.
                  </Typography>
                </div>
              </Alert>
            ) : (
              <Alert color="amber">
                <AlertCircle className="h-4 w-4" />
                <div>
                  <Typography className="font-semibold">Disabling Privacy Protection</Typography>
                  <Typography className="text-sm">
                    Your personal contact information will become publicly visible in WHOIS databases.
                  </Typography>
                </div>
              </Alert>
            )}
          </div>
        </DialogBody>
        <DialogFooter>
          <Button
            variant="text"
            color="gray"
            onClick={cancelPrivacyChange}
            className="mr-2"
          >
            Cancel
          </Button>
          <Button
            color={pendingAction === 'enable' ? 'green' : 'amber'}
            onClick={confirmPrivacyChange}
            disabled={isUpdating}
          >
            {pendingAction === 'enable' ? 'Enable' : 'Disable'} Privacy Protection
          </Button>
        </DialogFooter>
      </Dialog>
    </>
  );
}
