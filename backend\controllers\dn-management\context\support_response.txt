SUPPORT TICKET - <PERSON><PERSON>AIN REGISTRATION API ISSUE
=====================================================

DATE: 2025-07-11

ORIGINAL QUESTION TO SUPPORT:
-----------------------------
<PERSON><PERSON>,

I'm currently testing your API on the demo server, and I noticed that whenever I try to register a domain name via the API, the domain status gets stuck in "processing" and never completes. This blocks me from testing other endpoints such as renewal, transfer, updating name servers, etc.

I have a few questions regarding this behavior:

1. Is it normal on the demo environment that domain registrations don't complete?

2. Could the issue be caused by the fact that my demo account has 0 funds?

3. Is there a way to add fake funds or simulate a successful domain registration to proceed with testing the rest of the API?

Here are my account details on the demo server:
- ID Revendeur: 1280036
- PIN Support: 5235

Thanks in advance for your assistance.

Best regards,
<PERSON><PERSON><PERSON><PERSON>

SUPPORT TEAM RESPONSE:
----------------------
From: <PERSON><PERSON><PERSON> (Level 3 Technical Support Engineer)
Heberjahiz Support Team

Hi Abd<PERSON><PERSON>im,

If your reseller account balance is 0, it can prevent domain registration operations from completing properly in the demo environment.

To add demo funds to your reseller account, please go to: My Billing > Add Funds

Additionally, if needed, you have top up your demo customer account by going to: Customers > List > [Select your customer] > Add Funds

Could you please send us the exact API request you're using to register the domain? This will help us check if the request is correctly structured and advise on any necessary adjustments.

Once both your reseller and customer demo accounts have sufficient funds, and the API request is validated, you should be able to simulate domain registrations and proceed with testing other actions.

Looking forward to your request details.

Regards,
Abdellah AZIZ
Level 3 Technical Support Engineer

Heberjahiz Support Team
Phone: 080200 2800 (+212 5 22 49 19 44 for international calls)

ACTION ITEMS:
-------------
1. Add demo funds to reseller account via: My Billing > Add Funds
2. Add demo funds to customer account via: Customers > List > [Select customer] > Add Funds
3. Send exact API request structure to support for validation
4. Test domain registration again after funding accounts

ACCOUNT DETAILS:
----------------
- ID Revendeur: 1280036
- PIN Support: 5235
- Demo Environment: Yes
- Current Balance: 0 (causing registration failures)

REPLY TO SUPPORT:
-----------------
Hi Abdellah,

Thank you for your quick response and clarification regarding the demo account funding issue.

I understand that the 0 balance is preventing domain registrations from completing properly. I will proceed to add demo funds to both my reseller account and customer account as instructed:

1. Reseller account: My Billing > Add Funds
2. Customer account: Customers > List > [Select customer] > Add Funds

As requested, here is the exact API request structure I'm using for domain registration:

**API Endpoint:**
POST https://test.httpapi.com/api/domains/register.json

**Request Parameters:**
- auth-userid: 1280036
- api-key: [my_api_key]
- domain-name: example.com
- years: 1
- customer-id: [company_customer_id]
- reg-contact-id: [company_reg_contact_id]
- admin-contact-id: [company_admin_contact_id]
- tech-contact-id: [company_tech_contact_id]
- billing-contact-id: [company_billing_contact_id]
- ns: ns1.domain-name-api.dynv6.net
- ns: ns2.domain-name-api.dynv6.net
- invoice-option: NoInvoice
- auto-renew: false
- purchase-privacy: false
- protect-privacy: false

**Sample Full Request URL:**
https://test.httpapi.com/api/domains/register.json?auth-userid=1280036&api-key=[my_api_key]&domain-name=example.com&years=1&customer-id=[company_customer_id]&reg-contact-id=[company_reg_contact_id]&admin-contact-id=[company_admin_contact_id]&tech-contact-id=[company_tech_contact_id]&billing-contact-id=[company_billing_contact_id]&ns=ns1.domain-name-api.dynv6.net&ns=ns2.domain-name-api.dynv6.net&invoice-option=NoInvoice&auto-renew=false

**Current Behavior:**
- The API call returns successfully with a response
- Domain status shows as "processing"
- Status never changes from "processing" to "active"
- This prevents testing of other endpoints like renewal, transfer, nameserver updates, etc.

I'm using the company account approach where all domains are registered under a single company customer account with predefined contact IDs, rather than creating individual customer accounts for each domain registration.

Once I add the demo funds to both accounts, I'll test the registration again and let you know if the issue persists.

Please let me know if you need any additional information about the API request structure or if you notice any issues with the parameters I'm using.

Thank you for your assistance.

Best regards,
Abderrahim

Account Details:
- ID Revendeur: 1280036
- PIN Support: 5235
