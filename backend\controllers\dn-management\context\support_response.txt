SUPPORT TICKET - <PERSON><PERSON>AIN REGISTRATION API ISSUE
=====================================================

DATE: 2025-07-11

ORIGINAL QUESTION TO SUPPORT:
-----------------------------
<PERSON><PERSON>,

I'm currently testing your API on the demo server, and I noticed that whenever I try to register a domain name via the API, the domain status gets stuck in "processing" and never completes. This blocks me from testing other endpoints such as renewal, transfer, updating name servers, etc.

I have a few questions regarding this behavior:

1. Is it normal on the demo environment that domain registrations don't complete?

2. Could the issue be caused by the fact that my demo account has 0 funds?

3. Is there a way to add fake funds or simulate a successful domain registration to proceed with testing the rest of the API?

Here are my account details on the demo server:
- ID Revendeur: 1280036
- PIN Support: 5235

Thanks in advance for your assistance.

Best regards,
<PERSON><PERSON><PERSON><PERSON>

SUPPORT TEAM RESPONSE:
----------------------
From: <PERSON><PERSON><PERSON> (Level 3 Technical Support Engineer)
Heberjahiz Support Team

Hi Abd<PERSON><PERSON>im,

If your reseller account balance is 0, it can prevent domain registration operations from completing properly in the demo environment.

To add demo funds to your reseller account, please go to: My Billing > Add Funds

Additionally, if needed, you have top up your demo customer account by going to: Customers > List > [Select your customer] > Add Funds

Could you please send us the exact API request you're using to register the domain? This will help us check if the request is correctly structured and advise on any necessary adjustments.

Once both your reseller and customer demo accounts have sufficient funds, and the API request is validated, you should be able to simulate domain registrations and proceed with testing other actions.

Looking forward to your request details.

Regards,
Abdellah AZIZ
Level 3 Technical Support Engineer

Heberjahiz Support Team
Phone: 080200 2800 (+212 5 22 49 19 44 for international calls)

ACTION ITEMS:
-------------
1. Add demo funds to reseller account via: My Billing > Add Funds
2. Add demo funds to customer account via: Customers > List > [Select customer] > Add Funds
3. Send exact API request structure to support for validation
4. Test domain registration again after funding accounts

ACCOUNT DETAILS:
----------------
- ID Revendeur: 1280036
- PIN Support: 5235
- Demo Environment: Yes
- Current Balance: 0 (causing registration failures)
