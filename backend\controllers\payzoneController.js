const payzoneService = require("../services/payzoneService");
const orderService = require("../services/orderService");
const { isProd } = require("../constants/constant");

require("dotenv").config();
const frontendUrl = isProd
  ? process.env.FRONTEND_URL
  : process.env.FRONTEND_LOCAL_URL;

// ✅ 1️⃣ Initiate Payment (Replaces CMI Payment Initiation)
exports.initiatePayment = async (req, res) => {
  console.log("req.body : ", req.body);

  try {
    const formInputs = await payzoneService.createPaywall(req.body);

    const htmlForm = `
      <form id="openPaywall" action="${formInputs.payzoneUrl}" method="POST">
        <input type="hidden" name="payload" value='${formInputs.payload}' />
        <input type="hidden" name="signature" value="${formInputs.signature}" />
      </form>
      <script type="text/javascript">
        document.getElementById("openPaywall").submit();
      </script>
    `;

    res.setHeader("Content-Type", "text/html");
    res.send(htmlForm);
  } catch (error) {
    console.error("Error creating paywall:", error.message);
    res.status(500).json({ error: "Failed to create payment session" });
  }
};

// ✅ 2️⃣ Handle Payzone Callback (Verifies & Updates Order Status)
exports.handleCallback = async (req, res) => {
  console.log("🚀 Payzone Callback Received");

  try {
    const isVerified = await payzoneService.processCallback(req);
    if (!isVerified) {
      console.error("🚨 Payment verification failed!");
      return res.status(403).json({ message: "Forbidden: Invalid signature" });
    }

    const { orderId, status, id, transactions } = req.body;
    console.log("📌 Callback Data:", req.body);

    if (!orderId || !status || !id || !Array.isArray(transactions)) {
      console.error("🚨 Missing required parameters or transactions array.");
      return res
        .status(400)
        .json({ message: "Bad Request: Missing parameters" });
    }

    // ✅ Find an approved transaction with resultCode === 0
    const approvedTransaction = transactions.find(
      (tx) => tx.state === "APPROVED" && tx.resultCode === 0
    );

    // ✅ Log Payment Status
    const rawBody = req.rawBody || JSON.stringify(req.body);
    await payzoneService.logPaymentStatus(orderId, id, status, rawBody);
    console.log(`📌 Logged Payment: ${status} for Order ${orderId}`);

    // ✅ Process Payment Based on Transactions
    if (approvedTransaction) {
      await orderService.markOrderAsPaid(orderId, id);
      console.log(
        `✅ Order ${orderId} marked as PAID (Transaction ID: ${approvedTransaction.gatewayProvidedId})`
      );
      return res.status(200).json({ message: "Success" });
    }

    if (
      [
        "AUTHORIZE_PENDING",
        "CHARGE_PENDING",
        "AUTHORIZED",
        "REFUND_PENDING",
      ].includes(status)
    ) {
      console.log(`🔄 Payment ${status} in progress for Order ${orderId}`);
      return res.status(202).json({ message: "Payment Pending" });
    }

    if (
      ["DECLINED", "CANCELLED", "REFUNDED", "CHARGEBACK", "ERROR"].includes(
        status
      )
    ) {
      await orderService.markOrderAsFailed(orderId, id);
      console.log(`❌ Order ${orderId} marked as ${status}`);
      return res.status(200).json({ message: "Payment Failed" });
    }

    console.warn(`⚠️ Unhandled payment status: ${status} for Order ${orderId}`);
    return res.status(400).json({ message: "Unhandled Status" });
  } catch (error) {
    console.error("🚨 Callback Processing Error:", error);
    return res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
};

// ✅ 3️⃣ Handle Success Redirect (User Returns After Payment Success)
exports.handleSuccess = async (req, res) => {
  // console.log("req.query : ", req);
  console.log("✅ Payment Success Redirect");
  await orderService.markOrderAsPaid(
    "6836dc6821a2fd8120a50ffc",
    "test_transaction_id"
  );

  res.redirect(
    `${frontendUrl}/client/hosting-plans?item=21ESC43VOI33&status=success`
  );
};

// ✅ 4️⃣ Handle Failure Redirect (User Returns After Payment Failure)
exports.handleFailure = async (req, res) => {
  console.log("❌ Payment Failure Redirect");
  res.redirect(`${frontendUrl}/client/cart?item=21ESC43VOI23&status=failed`);
};

// ✅ 5️⃣ Handle Payment Cancellation Redirect
exports.handleCancel = async (req, res) => {
  console.log("🔄 Payment Cancelled Redirect");
  res.redirect(`${frontendUrl}/client/cart?item=21ESC43VOI48&status=canceled`);
};

// ✅ 4️⃣ Get Transaction Details
exports.getTransaction = async (req, res) => {
  try {
    const transaction = await payzoneService.getTransaction(req.params.id);
    res.json(transaction);
  } catch (error) {
    console.error("Error fetching transaction:", error.message);
    res.status(500).json({ error: "Failed to fetch transaction details" });
  }
};

// ✅ 5️⃣ Capture Payment
exports.capturePayment = async (req, res) => {
  try {
    const response = await payzoneService.capturePayment(
      req.params.id,
      req.body.amount
    );
    res.json(response);
  } catch (error) {
    console.error("Error capturing payment:", error.message);
    res.status(500).json({ error: "Failed to capture payment" });
  }
};

// ✅ 6️⃣ Cancel Payment
exports.cancelPayment = async (req, res) => {
  try {
    const response = await payzoneService.cancelPayment(req.params.id);
    res.json(response);
  } catch (error) {
    console.error("Error cancelling payment:", error.message);
    res.status(500).json({ error: "Failed to cancel payment" });
  }
};

// ✅ 7️⃣ Refund Payment
exports.refundPayment = async (req, res) => {
  try {
    const response = await payzoneService.refundPayment(
      req.params.id,
      req.body.amount
    );
    res.json(response);
  } catch (error) {
    console.error("Error refunding payment:", error.message);
    res.status(500).json({ error: "Failed to refund payment" });
  }
};
