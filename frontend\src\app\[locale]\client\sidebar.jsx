"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  UserCircle,
  ShoppingCart,
  Server,
  CreditCard,
  Headset,
  CodeXml,
  ShieldCheck,
  Globe,
} from "lucide-react";
import { useTranslations } from "next-intl";

const navigation = [
  {
    name: "profile",
    href: "/client/profile",
    icon: UserCircle,
    checkHref: "/client/profile",
  },
  {
    name: "cart",
    href: "/client/cart",
    icon: ShoppingCart,
    checkHref: "/client/cart",
  },
  {
    name: "hosting_plans",
    href: "/client/hosting-plans",
    icon: Server,
    checkHref: "/client/hosting-plans",
  },
  {
    name: "ssl_certificates",
    href: "/client/ssl-certificates",
    icon: ShieldCheck,
    checkHref: "/client/ssl-certificates",
  },
  {
    name: "domains",
    href: "/client/domains",
    icon: Globe,
    checkHref: "/client/domains",
  },
  {
    name: "web_dev.title",
    href: "/client/web-development",
    icon: CodeXml,
    checkHref: "/client/web-development",
  },
  {
    name: "payment_history",
    href: "/client/payment-history",
    icon: CreditCard,
    checkHref: "/client/payment",
  },
  {
    name: "support",
    href: "/client/support/tickets",
    icon: Headset,
    checkHref: "/client/support",
  },
];

export default function Sidebar({ isSidebarOpen, setIsSidebarOpen, isMobile }) {
  const pathname = usePathname();
  const normalizedPath = pathname.replace(/^\/[a-z]{2}/, "") || "/"; // Removes the locale prefix
  const t = useTranslations("client");

  return (
    <div
      id="sidebar"
      className={`fixed lg:static inset-y-0 left-0 z-40 w-64 bg-white border-l transform ${
        isSidebarOpen ? "translate-x-0" : "-translate-x-full"
      } lg:translate-x-0 transition-transform duration-300 ease-in-out border-r border-gray-200 pt-16 lg:pt-0`}
    >
      <nav className="mt-5 px-2 space-y-1">
        {navigation.map((item) => {
          // const isActive = pathname.startsWith(item.href);
          const isActive = normalizedPath.includes(item.checkHref);

          return (
            <Link
              key={item.name}
              href={item.href}
              className={`group flex items-center hover:bg-blue-400 hover:text-white px-3 py-3 text-sm font-medium rounded-lg transition-colors ${
                isActive
                  ? "bg-blue-50 text-blue-600"
                  : "text-gray-700  group-hover:text-gray-500"
              }`}
              onClick={() => isMobile && setIsSidebarOpen(false)}
            >
              <item.icon className="mr-3 h-5 w-5 " />
              {/* {item.name} */}
              {t(`${item.name}`)}
            </Link>
          );
        })}
      </nav>
    </div>
  );
}
