/* Custom styles for the settings page */

/* Smooth transitions for all interactive elements */
.settings-input {
  transition: all 0.2s ease-in-out;
}

.settings-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Gradient backgrounds with subtle animations */
.settings-section {
  background: linear-gradient(135deg, var(--gradient-from), var(--gradient-to));
  transition: transform 0.2s ease-in-out;
}

.settings-section:hover {
  transform: translateY(-2px);
}

/* Custom toggle switch styling */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
  background-color: #3b82f6;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Animated save button */
.save-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.save-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.save-button:active {
  transform: translateY(0);
}

/* Loading spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner {
  animation: spin 1s linear infinite;
}

/* Fade in animation for sections */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

/* Tab hover effects */
.tab-button {
  position: relative;
  transition: all 0.2s ease;
}

.tab-button:hover {
  transform: translateY(-1px);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 1px;
}

/* Character counter styling */
.char-counter {
  font-size: 0.75rem;
  font-weight: 500;
  transition: color 0.2s ease;
}

.char-counter.warning {
  color: #f59e0b;
}

.char-counter.danger {
  color: #ef4444;
}

/* Success/Error alert animations */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.alert-slide-in {
  animation: slideInRight 0.3s ease-out;
}

/* Favicon preview styling */
.favicon-preview {
  border: 2px solid #e5e7eb;
  border-radius: 4px;
  padding: 2px;
  background: white;
  transition: all 0.2s ease;
}

.favicon-preview:hover {
  border-color: #3b82f6;
  transform: scale(1.1);
}

/* Form section styling */
.form-section {
  border-radius: 12px;
  border: 1px solid;
  padding: 24px;
  margin-bottom: 32px;
  position: relative;
  overflow: hidden;
}

.form-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--section-color), var(--section-color-light));
}

/* Blue section (Site Information) */
.form-section.blue {
  --section-color: #3b82f6;
  --section-color-light: #60a5fa;
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  border-color: #bfdbfe;
}

/* Green section (Contact Information) */
.form-section.green {
  --section-color: #10b981;
  --section-color-light: #34d399;
  background: linear-gradient(135deg, #ecfdf5, #d1fae5);
  border-color: #a7f3d0;
}

/* Purple section (Social Media) */
.form-section.purple {
  --section-color: #8b5cf6;
  --section-color-light: #a78bfa;
  background: linear-gradient(135deg, #f5f3ff, #ede9fe);
  border-color: #c4b5fd;
}

/* Orange section (Analytics) */
.form-section.orange {
  --section-color: #f97316;
  --section-color-light: #fb923c;
  background: linear-gradient(135deg, #fff7ed, #fed7aa);
  border-color: #fdba74;
}

/* Gray section (Advanced) */
.form-section.gray {
  --section-color: #6b7280;
  --section-color-light: #9ca3af;
  background: linear-gradient(135deg, #f9fafb, #f3f4f6);
  border-color: #d1d5db;
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .form-section {
    padding: 16px;
    margin-bottom: 24px;
  }
  
  .settings-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Custom Toast Styling */
/* .toast-success {
  background: linear-gradient(135deg, #10b981, #059669) !important;
  color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3) !important;
}

.toast-error {
  background: linear-gradient(135deg, #ef4444, #dc2626) !important;
  color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3) !important;
}

.toast-warning {
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
  color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(245, 158, 11, 0.3) !important;
}

.toast-info {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3) !important;
} */

/* Toast container positioning */
.Toastify__toast-container {
  z-index: 9999;
}

/* Toast animation improvements */
.Toastify__toast {
  border-radius: 12px;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

/* .Toastify__toast--success {
  background: linear-gradient(135deg, #10b981, #059669);
}

.Toastify__toast--error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.Toastify__toast--warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.Toastify__toast--info {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.Toastify__progress-bar {
  background: rgba(255, 255, 255, 0.7);
} */

/* Enhanced alert animations */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.alert-slide-in {
  animation: slideInRight 0.3s ease-out;
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .form-section {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(75, 85, 99, 0.6);
  }

  .settings-input {
    background: rgba(31, 41, 55, 0.5);
    border-color: rgba(75, 85, 99, 0.6);
    color: #f9fafb;
  }
}
