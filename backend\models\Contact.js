const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const contactSchema = new Schema({
  // Reference to the user who owns this contact
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // External API contact ID from domain registration service
  externalContactId: {
    type: String,
    required: true,
    index: true
  },
  
  // Contact type (registrant, admin, tech, billing)
  contactType: {
    type: String,
    required: true,
    enum: ['registrant', 'admin', 'tech', 'billing'],
    index: true
  },
  
  // Contact details
  name: {
    type: String,
    required: true,
    trim: true
  },
  
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  
  company: {
    type: String,
    default: '',
    trim: true
  },
  
  address: {
    type: String,
    default: '',
    trim: true
  },
  
  city: {
    type: String,
    default: '',
    trim: true
  },
  
  country: {
    type: String,
    default: '',
    trim: true
  },
  
  zipcode: {
    type: String,
    default: '',
    trim: true
  },
  
  phoneCountryCode: {
    type: String,
    default: '',
    trim: true
  },
  
  phone: {
    type: String,
    default: '',
    trim: true
  },
  
  // Customer ID in the external system (if applicable)
  customerId: {
    type: String,
    default: ''
  },
  
  // Status of the contact
  status: {
    type: String,
    enum: ['active', 'inactive', 'deleted'],
    default: 'active'
  },
  
  // Metadata for tracking
  lastSyncedAt: {
    type: Date,
    default: Date.now
  },
  
  // Additional fields that might come from external API
  additionalData: {
    type: Object,
    default: {}
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for efficient queries
contactSchema.index({ userId: 1, contactType: 1 });
contactSchema.index({ userId: 1, externalContactId: 1 });
contactSchema.index({ externalContactId: 1, contactType: 1 });

// Virtual for full phone number
contactSchema.virtual('fullPhone').get(function() {
  if (this.phoneCountryCode && this.phone) {
    return `${this.phoneCountryCode}${this.phone}`;
  }
  return this.phone || '';
});

// Method to get contact data for external API
contactSchema.methods.getApiData = function() {
  return {
    name: this.name,
    email: this.email,
    company: this.company,
    address: this.address,
    city: this.city,
    country: this.country,
    zipcode: this.zipcode,
    phoneCountryCode: this.phoneCountryCode,
    phone: this.phone
  };
};

// Method to update contact data
contactSchema.methods.updateContactData = function(contactData) {
  this.name = contactData.name || this.name;
  this.email = contactData.email || this.email;
  this.company = contactData.company || this.company;
  this.address = contactData.address || this.address;
  this.city = contactData.city || this.city;
  this.country = contactData.country || this.country;
  this.zipcode = contactData.zipcode || this.zipcode;
  this.phoneCountryCode = contactData.phoneCountryCode || this.phoneCountryCode;
  this.phone = contactData.phone || this.phone;
  this.lastSyncedAt = new Date();
};

// Static method to find contact by user and type
contactSchema.statics.findByUserAndType = function(userId, contactType) {
  return this.findOne({ userId, contactType, status: 'active' });
};

// Static method to find all contacts for a user
contactSchema.statics.findByUser = function(userId) {
  return this.find({ userId, status: 'active' });
};

// Static method to find contact by external ID
contactSchema.statics.findByExternalId = function(externalContactId) {
  return this.findOne({ externalContactId, status: 'active' });
};

const Contact = mongoose.model('Contact', contactSchema);
module.exports = Contact;
