const mongoose = require("mongoose");
const Package = require("../../models/Package");
const Specification = require("../../models/Specification");
const { v4: uuidv4 } = require("uuid");
const ProductStatus = require("../../constants/enums/poduct-status");
const { translateTextService } = require("../../services/adminService");
const Brand = require("../../models/Brand");
const Category = require("../../models/Category");
const adminLogger = require("../../utils/adminLogger");

// Get All Packages with pagination
exports.getAllPackages = async (req, res) => {
  try {
    // Extract pagination parameters from query string
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 5;
    const skip = (page - 1) * limit;
    const searchTerm = req.query.search || "";
    const brandFilter = req.query.brand || "";
    const statusFilter = req.query.status || "";

    // Build the query object
    let query = {};

    // Add search condition if search term provided
    if (searchTerm) {
      query.$or = [
        { name: { $regex: searchTerm, $options: "i" } },
        { description: { $regex: searchTerm, $options: "i" } },
        { reference: { $regex: searchTerm, $options: "i" } },
      ];
    }

    // Add brand filter if provided
    if (brandFilter) {
      // Find the brand ID if a name is provided
      if (!mongoose.Types.ObjectId.isValid(brandFilter)) {
        const brand = await Brand.findOne({
          name: { $regex: brandFilter, $options: "i" },
        });
        if (brand) {
          query.brand = brand._id;
        }
      } else {
        query.brand = brandFilter;
      }
    }

    // Add status filter if provided
    if (statusFilter && Object.values(ProductStatus).includes(statusFilter)) {
      query.status = statusFilter;
    }

    // Get total count for pagination
    const totalPackages = await Package.countDocuments(query);

    // Fetch packages with pagination, filtering and sorting
    const packages = await Package.find(query)
      .populate("brand")
      .populate("specifications")
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalPackages / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return res.status(200).json({
      success: true,
      data: packages,
      pagination: {
        currentPage: page,
        totalPages: totalPages,
        totalItems: totalPackages,
        itemsPerPage: limit,
        hasNextPage: hasNextPage,
        hasPrevPage: hasPrevPage,
        nextPage: hasNextPage ? page + 1 : null,
        prevPage: hasPrevPage ? page - 1 : null,
      },
    });
  } catch (error) {
    console.error("Error fetching packages:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Add a New Package
exports.addPackage = async (req, res) => {
  try {
    const packageData = req.body;
    console.log("Package Data:", packageData);

    if (!Object.values(ProductStatus).includes(packageData.status)) {
      console.error("Invalid package status:", packageData.status);
      return res.status(400).json({ error: "Invalid package status" });
    }

    // Generate an 8-character unique reference
    const reference = uuidv4().slice(0, 8);
    const newPackageData = { ...packageData, reference: reference };
    const newPackage = new Package(newPackageData);
    const savedPackage = await newPackage.save();

    // Update the brand with the new package reference
    if (savedPackage.brand) {
      await Brand.findByIdAndUpdate(
        savedPackage.brand,
        { $push: { packages: savedPackage._id } },
        { new: true }
      );
    }

    // Populate the saved package with brand and specifications
    const populatedPackage = await Package.findById(savedPackage._id)
      .populate("brand")
      .populate("specifications");

    // Log admin activity using the enhanced logger
    await adminLogger.logCreate(
      req.user?._id,
      "Package",
      {
        name: savedPackage.name,
        reference: savedPackage.reference,
      },
      savedPackage.toObject()
    );

    return res.status(201).json(populatedPackage);
  } catch (error) {
    console.error("Error adding package:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

// Update a Package
exports.updatePackage = async (req, res) => {
  try {
    const { packageId } = req.params;
    const updateData = req.body;
    console.log("Package Data:", updateData);

    if (!mongoose.Types.ObjectId.isValid(packageId)) {
      return res.status(400).json({ error: "Invalid package ID" });
    }

    // Get the package before update to check original brand
    const originalPackage = await Package.findById(packageId);
    if (!originalPackage) {
      return res.status(404).json({ error: "Package not found" });
    }

    const updatedPackage = await Package.findByIdAndUpdate(
      packageId,
      { $set: updateData },
      { new: true, runValidators: true }
    )
      .populate("brand")
      .populate("specifications");

    // Log admin activity using the enhanced logger
    await adminLogger.logUpdate(
      req.user?._id,
      "Package",
      {
        name: originalPackage.name,
        reference: originalPackage.reference,
      },
      updatedPackage ? updatedPackage.toObject() : {}
    );

    // Handle brand changes
    if (originalPackage.brand?.toString() !== updateData.brand?.toString()) {
      // Remove package from old brand
      if (originalPackage.brand) {
        await Brand.findByIdAndUpdate(originalPackage.brand, {
          $pull: { packages: packageId },
        });
      }

      // Add package to new brand
      if (updateData.brand) {
        await Brand.findByIdAndUpdate(updateData.brand, {
          $push: { packages: packageId },
        });
      }
    }

    return res.status(200).json(updatedPackage);
  } catch (error) {
    console.error("Error updating package:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

// Delete a Package
exports.deletePackage = async (req, res) => {
  try {
    const { packageId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(packageId)) {
      return res.status(400).json({ error: "Invalid package ID" });
    }

    // Get the package to find its brand before deletion
    const packageToDelete = await Package.findById(packageId);
    if (!packageToDelete) {
      return res.status(404).json({ error: "Package not found" });
    }

    // Remove package reference from brand
    if (packageToDelete.brand) {
      await Brand.findByIdAndUpdate(packageToDelete.brand, {
        $pull: { packages: packageId },
      });
    }

    // Delete the package
    await Package.findByIdAndDelete(packageId);

    // Log admin activity using the enhanced logger
    await adminLogger.logDelete(
      req.user?._id,
      "Package",
      {
        name: packageToDelete.name,
        reference: packageToDelete.reference,
      },
      packageToDelete.toObject()
    );

    return res.status(200).json({ message: "Package deleted successfully" });
  } catch (error) {
    console.error("Error deleting package:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

// Add a New Spec
exports.addSpec = async (req, res) => {
  try {
    const specData = req.body;
    console.log("Spec Data:", specData);
    const newSpec = new Specification(specData);
    const savedSpec = await newSpec.save();
    const { createdAt, updatedAt, __v, ...specWithoutTimestamps } =
      savedSpec.toObject();
    return res.status(201).json(specWithoutTimestamps);
  } catch (error) {
    console.error("Error adding spec:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

// Get All Specs
exports.getAllSpecs = async (req, res) => {
  try {
    const specs = await Specification.find();
    return res.status(200).json(specs);
  } catch (error) {
    console.error("Error fetching specs:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

exports.deeplTranslate = async (req, res) => {
  try {
    const { text, targetLang } = req.body;
    const translatedText = await translateTextService(text, targetLang);
    console.log("Translating text:", translatedText);
    // Simulate translation
    return res.status(200).json({ translatedText });
  } catch (error) {
    console.error("Error translating text:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

exports.updateSpec = async (req, res) => {
  try {
    const { _id, value, value_fr } = req.body;
    console.log("Spec Data:", req.body);
    // Validate required fields
    if (!_id || !value || !value_fr) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields: id, value, or value_fr",
      });
    }

    // Find and update the spec
    const updatedSpec = await Specification.findByIdAndUpdate(
      _id,
      {
        value,
        value_fr,
        updated_at: Date.now(),
      },
      { new: true } // Return the updated document
    );

    if (!updatedSpec) {
      return res.status(404).json({
        success: false,
        message: "Specification not found",
      });
    }

    // Return success response
    res.status(200).json({
      success: true,
      message: "Specification updated successfully",
      updatedSpec,
    });
  } catch (error) {
    console.error("Error updating specification:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error while updating specification",
      error: error.message,
    });
  }
};

exports.getAllCategories = async (req, res) => {
  try {
    const categories = await Category.find().populate("brands");
    return res.status(200).json(categories);
  } catch (error) {
    console.error("Error fetching categories:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};
