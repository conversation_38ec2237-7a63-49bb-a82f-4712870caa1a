const mongoose = require('mongoose');
const { isProd } = require('../constants/constant');

require('dotenv').config();
const dbName = isProd ? "ztech" : "zn_ztech"
const dbURI = `mongodb://0.0.0.0:27017/${dbName}`;
const connectDatabase = async () => {
    try {
        const res = await mongoose.connect(dbURI, { useNewUrlParser: true, useUnifiedTopology: true });
        console.log('Connection to the database successful... ');

        // startCronJob(); this cron job will be removed soon
        // console.log('Starting cron job successfully');

    } catch (error) {
        console.log('Error connecting to the database:', error);
    }
};
connectDatabase();
module.exports = mongoose; // ✅ Export mongoose instance

// const mongoose = require("mongoose");
// require("dotenv").config();

// const connectDB = async () => {
//     const maxRetries = 3;
//     let currentRetry = 0;

//     while (currentRetry < maxRetries) {
//         try {
//             await mongoose.connect(process.env.MONGODB_URI);
//             console.log("✅ MongoDB Atlas connected successfully!");
//             return;
//         } catch (error) {
//             currentRetry++;
//             console.error(`❌ MongoDB Connection Failed (Attempt ${currentRetry}/${maxRetries})`);

//             if (error.name === "MongooseServerSelectionError") {
//                 console.error("⚠️ MongoDB Atlas Unreachable. Check:");
//                 console.error("1. IP whitelist in MongoDB Atlas");
//                 console.error("2. Your connection string in `.env`");
//                 console.error("3. Database user credentials");
//             } else {
//                 console.error("⚠️ Connection Error:", error.message);
//             }

//             if (currentRetry === maxRetries) {
//                 console.error("❌ Maximum connection attempts reached. Exiting...");
//                 process.exit(1);
//             }

//             // Wait 2 seconds before retrying
//             await new Promise(resolve => setTimeout(resolve, 2000));
//         }
//     }
// };
// connectDB();
// module.exports = mongoose;
