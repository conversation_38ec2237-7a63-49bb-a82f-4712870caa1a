"use client";
import React, { useEffect, useState } from "react";
import { Input, Button, Typography, Checkbox } from "@material-tailwind/react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { AccountState } from "../../app/config/AccountState";
import { useAuth } from "../../app/context/AuthContext";
import { useRouter } from "next/navigation";
import authService from "../../app/services/authService";
import profileService from "../../app/services/profileService";
import { toast } from "react-toastify";
import { LogIn, LogInIcon, Save, SaveIcon, XCircle } from "lucide-react";
import SecButton from "../shared/SecButton";
import ReCAPTCHA from "react-google-recaptcha";
import { useTranslations } from "use-intl";

// Define the helper function outside the component
const truncateEmail = (email) => {
  if (!email) return "";
  const [localPart, domain] = email.split("@");
  // Show only the first 5 characters if the local part is longer than 5 characters
  return localPart.length > 5
    ? localPart.slice(0, 5) + "..." + "@" + domain
    : email;
};

function BillingInfoForm({ billingInfo, setBillingInfo, onInputChange, t }) {
  const authT = useTranslations("auth");
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isEditBillingInfo, setIsEditBillingInfo] = useState(false);
  const [errors, setErrors] = useState({});
  const [recaptchaValue, setRecaptchaValue] = useState("");
  const [recaptchaError, setRecaptchaError] = useState("");

  const { user, checkAuth } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (user?.state === AccountState.GUEST) {
      setIsLoggedIn(false);
      return;
    }

    const info = user?.billingInfo || {};
    setBillingInfo(info);
    console.log("User billing info: ", info);
    setIsLoggedIn(true);
    cancelEditBillingInfo();
  }, [user]);

  const saveAndSignUp = async (e) => {
    e.preventDefault();
    setRecaptchaError("");
    if (!recaptchaValue) {
      console.log("Recaptcha value is required for this request to work");
      setRecaptchaError(authT("recaptcha_required"));
      return;
    }
    setLoading(true);
    try {
      setErrors({});
      const billingInfoPayload = {
        ...billingInfo,
        "g-recaptcha-response": recaptchaValue,
      };
      const signUpRes = await authService.cartRegister(billingInfoPayload);
      console.log("saveAndSignUp: ", signUpRes);
      toast.success(t("account_created_success"));
      window.location.reload();
      setIsLoggedIn(true);
      setIsEditBillingInfo(false);
    } catch (error) {
      setLoading(false);
      const errors = error?.response?.data?.errors || [];
      const formattedErrors = errors.reduce((acc, err) => {
        if (err.key === "g-recaptcha-response") {
          console.log("recaptcha error: ", err.msg.replace(/"/g, ""));
          setRecaptchaError(err.msg.replace(/"/g, ""));
        } else {
          acc[err.key] = err.msg.replace(/"/g, "");
        }
        return acc;
      }, {});
      console.log("🚀 ~ formattedErrors ~ formattedErrors:", errors);
      setErrors(formattedErrors);
    } finally {
      setLoading(false);
    }
  };

  const saveBillingInfo = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      // Validate required fields before submission
      const requiredFields = ['BillToName', 'email', 'phone', 'country', 'address'];
      const newErrors = {};
      let isValid = true;

      // Check required fields
      requiredFields.forEach(field => {
        if (!billingInfo[field]) {
          newErrors[field] = `${t(field)} ${t('is_required')}`;
          isValid = false;
        }
      });

      // Check company-specific fields if isCompany is true
      if (billingInfo.isCompany) {
        const companyRequiredFields = ['companyICE', 'companyEmail', 'companyPhone', 'companyAddress'];
        companyRequiredFields.forEach(field => {
          if (!billingInfo[field]) {
            // Use the correct translation key for company fields
            const fieldLabel = field === 'companyICE' ? 'ice' : field.replace('company', '').toLowerCase();
            newErrors[field] = `${t(fieldLabel)} ${t('is_required')}`;
            isValid = false;
          }
        });

        // Validate ICE format
        if (billingInfo.companyICE && !/^\d{15}$/.test(billingInfo.companyICE)) {
          newErrors.companyICE = t('invalid_ice');
          isValid = false;
        }

        // Validate email format
        if (billingInfo.companyEmail && !/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/.test(billingInfo.companyEmail)) {
          newErrors.companyEmail = t('invalid_email');
          isValid = false;
        }

        // Validate phone format
        if (billingInfo.companyPhone && !/^\+?[0-9]{7,15}$/.test(billingInfo.companyPhone)) {
          newErrors.companyPhone = t('invalid_phone');
          isValid = false;
        }
      }

      if (!isValid) {
        setErrors(newErrors);
        setLoading(false);
        return;
      }

      // Add userId to the request
      const billingInfoWithUserId = {
        ...billingInfo,
        userId: user?._id
      };

      const updatedUser = await profileService.updateBillingInfo(billingInfoWithUserId);
      toast.success(t("billing_info_updated"));
      await checkAuth();
      setIsEditBillingInfo(false);
    } catch (error) {
      console.error("Error updating billing info:", error);
      const errors = error?.response?.data?.errors || [];
      const formattedErrors = errors.reduce((acc, err) => {
        acc[err.key] = err.msg.replace(/"/g, "");
        return acc;
      }, {});
      setErrors(formattedErrors);
    } finally {
      setLoading(false);
    }
  };

  const cancelEditBillingInfo = () => {
    try {
      const info = user?.billingInfo || {};
      const newErrors = {};
      if (!info.phone) newErrors.phone = " ";
      if (!info.address) newErrors.address = " ";
      if (!info.country) newErrors.country = " ";

      if (Object.keys(newErrors).length > 0) {
        setIsEditBillingInfo(true);
        setErrors(newErrors);
      } else {
        setIsEditBillingInfo(false);
        setErrors({});
      }
    } catch (error) {
      console.log(" cancelling edit billing info : ", error);
    }
  };

  const signUpInputFields = [
    { name: "firstName", label: "First name", type: "text" },
    { name: "lastName", label: "Last name", type: "text" },
    { name: "email", label: "Email", type: "email" },
    { name: "phone", label: "Phone number", type: "tel" },
    { name: "country", label: "Your Country", type: "text" },
    { name: "address", label: "Address", type: "text" },
    { name: "password", label: "Password", type: "password" },
    { name: "confirmPassword", label: "Confirm Password", type: "password" },
  ];
  const billingInputFields = [
    { name: "BillToName", label: "Full name", type: "text" },
    { name: "email", label: "Email", type: "email" },
    { name: "phone", label: "Phone number", type: "tel" },
    { name: "country", label: "Your Country", type: "text" },
    { name: "address", label: "Address", type: "text" },
  ];

  // Company-specific fields
  const companyFields = [
    { name: "companyICE", label: "company_ice", type: "text" },
    { name: "companyEmail", label: "company_email", type: "email" },
    { name: "companyPhone", label: "company_phone", type: "tel" },
    { name: "companyAddress", label: "company_address", type: "text" },
  ];

  // if (loading) {
  //   return (
  //     <div className="p-4 border rounded-lg shadow-sm space-y-4">
  //       <Skeleton height={30} />
  //       <Skeleton count={4} height={40} className="mb-4" />
  //       <div className="flex flex-col sm:flex-row gap-x-5">
  //         <Skeleton height={40} width={120} />
  //         <Skeleton height={40} width={180} />
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className="p-4 border rounded-lg shadow-sm bg-white">
      <Typography
        variant="h1"
        className="text-lg font-medium text-gray-900 mb-4"
      >
        {t("billing_information")}
      </Typography>
      {!isLoggedIn ? (
        <form className="space-y-4" onSubmit={saveAndSignUp}>
          {signUpInputFields.map((_, index) => {
            if (index % 2 === 0) {
              const firstField = signUpInputFields[index];
              const secondField = signUpInputFields[index + 1];

              return (
                <div
                  key={firstField.name}
                  className="flex flex-col md:flex-row gap-4"
                >
                  <div className="flex-1">
                    {loading ? (
                      <Skeleton height={40} className="mb-2" />
                    ) : (
                      <>
                        <Input
                          type={firstField.type}
                          name={firstField.name}
                          value={billingInfo[firstField.name] || ""}
                          onChange={onInputChange}
                          className="w-full p-2 border rounded-lg"
                          label={t(firstField.name)}
                          error={!!errors[firstField.name]}
                        />
                        {errors[firstField.name] && (
                          <span className="text-red-500 text-xs">
                            {errors[firstField.name]}
                          </span>
                        )}
                      </>
                    )}
                  </div>

                  {secondField && (
                    <div className="flex-1">
                      {loading ? (
                        <Skeleton height={40} className="mb-2" />
                      ) : (
                        <>
                          <Input
                            type={secondField.type}
                            name={secondField.name}
                            value={billingInfo[secondField.name] || ""}
                            onChange={onInputChange}
                            className="w-full p-2 border rounded-lg"
                            label={t(secondField.name)}
                            error={!!errors[secondField.name]}
                          />
                          {errors[secondField.name] && (
                            <span className="text-red-500 text-xs">
                              {errors[secondField.name]}
                            </span>
                          )}
                        </>
                      )}
                    </div>
                  )}
                </div>
              );
            }
          })}
          {/* ReCAPTCHA */}
          <div className="flex flex-col md:flex-row md:items-start md:gap-x-10 p-0 m-0">
            <ReCAPTCHA
              sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY}
              onChange={setRecaptchaValue}
            />
            {recaptchaError && (
              <Typography
                variant="small"
                color="red"
                className="mt-2 text-xs my-auto"
              >
                {recaptchaError}
              </Typography>
            )}
          </div>
          <div className="flex gap-x-4">
            <SecButton
              type={"submit"}
              text={loading ? t("saving") : t("save_and_sign_up")}
              icon={LogInIcon}
              disabled={loading}
              className={`flex gap-2 px-2 text-secondary border hover:bg-secondary hover:text-white hover:border-transparent border-secondary text-sm py-2 rounded-lg hover:bg-opacity-80  shadow-none font-medium ${
                loading ? "opacity-50 cursor-not-allowed" : ""
              }`}
            />
            <Button
              onClick={() => router.push("/auth/login")}
              disabled={loading}
              className="bg-transparent hover:shadow-none font-medium text-sm text-gray-700 hover:text-primary normal-case p-0 shadow-none border-none"
            >
              {t("already_have_account")}
            </Button>
          </div>
        </form>
      ) : (
        <>
          {isEditBillingInfo ? (
            <form className="space-y-4" onSubmit={saveBillingInfo}>
              {/* Company Account Selection */}
              <div className="mb-4">
                <div className="flex items-center gap-2">
                  <Checkbox
                    name="isCompany"
                    label={t('company_account')}
                    checked={billingInfo.isCompany || false}
                    onChange={(e) => {
                      setBillingInfo({
                        ...billingInfo,
                        isCompany: e.target.checked
                      });
                    }}
                  />
                </div>
              </div>

              {/* Regular Billing Fields */}
              {billingInputFields.map((field, index) => {
                // Check for even indices to render pairs of inputs
                if (index % 2 === 0) {
                  const firstField = field;
                  const secondField = billingInputFields[index + 1];

                  return (
                    <div
                      key={firstField.name}
                      className="flex flex-col md:flex-row gap-4"
                    >
                      <div className="flex-1">
                        <Input
                          type={firstField.type}
                          name={firstField.name}
                          value={billingInfo[firstField.name] || ""}
                          onChange={onInputChange}
                          className="w-full p-2 border rounded-lg"
                          label={t(firstField.name)}
                          error={!!errors[firstField.name]}
                        />
                        {errors[firstField.name] && (
                          <span className="text-red-500 text-xs">
                            {errors[firstField.name]}
                          </span>
                        )}
                      </div>

                      {secondField && (
                        <div className="flex-1">
                          <Input
                            type={secondField.type}
                            name={secondField.name}
                            value={billingInfo[secondField.name] || ""}
                            onChange={onInputChange}
                            className="w-full p-2 border rounded-lg"
                            label={t(secondField.name)}
                            error={!!errors[secondField.name]}
                          />
                          {errors[secondField.name] && (
                            <span className="text-red-500 text-xs">
                              {errors[secondField.name]}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  );
                }
                return null; // Don't render anything for odd indices
              })}

              {/* Company Fields (conditionally rendered) */}
              {billingInfo.isCompany && (
                <>
                  <Typography variant="h6" className="text-base font-semibold text-gray-800 mt-4 mb-2">
                    {t('company_info')}
                  </Typography>
                  {companyFields.map((field, index) => {
                    // Check for even indices to render pairs of inputs
                    if (index % 2 === 0) {
                      const firstField = field;
                      const secondField = companyFields[index + 1];

                      return (
                        <div
                          key={firstField.name}
                          className="flex flex-col md:flex-row gap-4"
                        >
                          <div className="flex-1">
                            <Input
                              type={firstField.type}
                              name={firstField.name}
                              value={billingInfo[firstField.name] || ""}
                              onChange={onInputChange}
                              className="w-full p-2 border rounded-lg"
                              label={t(firstField.label)}
                              error={!!errors[firstField.name]}
                            />
                            {errors[firstField.name] && (
                              <span className="text-red-500 text-xs">
                                {errors[firstField.name]}
                              </span>
                            )}
                          </div>

                          {secondField && (
                            <div className="flex-1">
                              <Input
                                type={secondField.type}
                                name={secondField.name}
                                value={billingInfo[secondField.name] || ""}
                                onChange={onInputChange}
                                className="w-full p-2 border rounded-lg"
                                label={t(secondField.label)}
                                error={!!errors[secondField.name]}
                              />
                              {errors[secondField.name] && (
                                <span className="text-red-500 text-xs">
                                  {errors[secondField.name]}
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    }
                    return null; // Don't render anything for odd indices
                  })}
                </>
              )}
              <div className="flex w-fit ml-auto gap-x-10 justify-center items-center">
                <SecButton
                  onClick={() => cancelEditBillingInfo()}
                  text={t("discard")}
                  icon={XCircle}
                  className={`flex justify-end gap-2 px-2 text-red-500 border border-transparent hover:border-red-500 text-sm hover:bg-opacity-80 py-1 w-fit ml-auto rounded-md shadow-none font-medium`}
                />
                <SecButton
                  type={"submit"}
                  // onClick={() => cancelEditBillingInfo()}
                  text={t("save_billing_info")}
                  icon={SaveIcon}
                  className={`flex gap-2 px-2 text-secondary border border-transparent hover:border-secondary text-sm hover:bg-opacity-80 py-1 w-fit ml-auto rounded-md shadow-none font-medium`}
                />
              </div>
            </form>
          ) : (
            <div className="bg-white font-poppins px-4 py-2 text-sm mx-auto border border-gray-50 rounded-lg max-w-3xl">
              <div className="flex flex-col space-y-2">
                <div className="flex flex-wrap gap-4 items-center justify-between">
                  <div className="text-gray-700 font-medium w-full sm:w-auto">
                    {billingInfo.BillToName}
                  </div>
                  <div className="text-gray-700 w-full sm:w-auto">
                    {truncateEmail(billingInfo.email)}
                  </div>
                  <div className="text-gray-700 w-full sm:w-auto xl:text-right">
                    {billingInfo.phone}
                  </div>
                  <div className="text-gray-700 w-full sm:w-auto">
                    {billingInfo.country}
                  </div>
                </div>

                {/* Second Row: Address and Edit Button */}
                <div className="flex items-center justify-between">
                  <div className="text-gray-700 capitalize flex-1">
                    {billingInfo.address}
                  </div>
                </div>
                <SecButton
                  onClick={() => setIsEditBillingInfo(true)}
                  text={t("edit")}
                  className={`flex justify-end gap-2 px-2 text-secondary border border-transparent hover:border-secondary text-sm hover:bg-opacity-80 py-1 w-fit ml-auto rounded-md shadow-none font-medium`}
                />
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}

export default BillingInfoForm;
