"use client";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import {
  <PERSON>po<PERSON>,
  Card,
  CardBody,
  Button,
  Tabs,
  TabsHeader,
  TabsBody,
  Tab,
  TabPanel,
  Switch,
} from "@material-tailwind/react";
import {
  Globe,
  ArrowLeft,
  Server,
  Shield,
  Lock,
  Mail,
  ExternalLink,
  RefreshCw,
} from "lucide-react";
import domainMngService from "@/app/services/domainMngService";
import NameserverManager from "@/components/domains/NameserverManager";
import PrivacyProtectionManager from "@/components/domains/PrivacyProtectionManager";

export default function DomainDetailPage({ params }) {
  const { id } = params;
  const t = useTranslations("client");
  const dt = useTranslations("client.domainWrapper");
  const [domain, setDomain] = useState(null);
  const [loading, setLoading] = useState(true);
  const [apiError, setApiError] = useState(null);
  const [activeTab, setActiveTab] = useState("overview");
  const router = useRouter();

  useEffect(() => {
    const getDomainDetails = async () => {
      try {
        setLoading(true);

        // First, get the user's domains to find the domain name by ID
        const domainsRes = await domainMngService.getUserDomains();
        const userDomains = domainsRes.data?.domains || [];

        // Find the domain with the matching ID
        const userDomain = userDomains.find((d) => d.id === id);

        if (!userDomain) {
          console.error("Domain not found with ID:", id);
          setLoading(false);
          return;
        }

        console.log("Found user domain:", userDomain);

        // Get comprehensive domain details from the registration system API
        try {
          // Use the domains/details-by-name.json endpoint with comprehensive options
          const detailsRes = await domainMngService.getDomainDetailsByName(
            userDomain.name,
            "All" // Get all available details including OrderDetails, ContactIds, NsDetails, DomainStatus, etc.
          );
          console.log("Domain details from registration API:", detailsRes.data);

          const apiDomain = detailsRes.data?.domain || {};

          // Parse nameservers from API response
          const nameservers = [];
          if (apiDomain.ns1) nameservers.push(apiDomain.ns1);
          if (apiDomain.ns2) nameservers.push(apiDomain.ns2);
          if (apiDomain.ns3) nameservers.push(apiDomain.ns3);
          if (apiDomain.ns4) nameservers.push(apiDomain.ns4);

          // Parse contact details from API response
          const contacts = {
            registrant: apiDomain.registrantcontact || {
              name: dt("not_available"),
              email: dt("not_available"),
              phone: dt("not_available"),
              address: dt("not_available"),
            },
            admin: apiDomain.admincontact || {
              name: dt("not_available"),
              email: dt("not_available"),
              phone: dt("not_available"),
              address: dt("not_available"),
            },
            tech: apiDomain.techcontact || {
              name: dt("not_available"),
              email: dt("not_available"),
              phone: dt("not_available"),
              address: dt("not_available"),
            },
            billing: apiDomain.billingcontact || {
              name: dt("not_available"),
              email: dt("not_available"),
              phone: dt("not_available"),
              address: dt("not_available"),
            },
          };

          // Format dates from API response
          const formatDate = (timestamp) => {
            if (!timestamp) return dt("not_available");
            try {
              return new Date(timestamp * 1000).toLocaleDateString();
            } catch (e) {
              return timestamp;
            }
          };

          // Combine user domain data with comprehensive API details
          const combinedDomain = {
            id: userDomain.id,
            name: userDomain.name,
            // Use API status if available, fallback to user status
            status: apiDomain.currentstatus || userDomain.status || "unknown",
            registrationDate: formatDate(apiDomain.creationtime) || userDomain.registrationDate || "Unknown",
            expiryDate: formatDate(apiDomain.endtime) || userDomain.expiryDate || "Unknown",
            autoRenew: apiDomain.recurring === "true" || userDomain.autoRenew || false,
            registrar: userDomain.registrar || "ZTech Domains",
            nameservers: nameservers.length > 0 ? nameservers : (userDomain.nameservers || [dt("no_nameservers_configured")]),
            privacyProtection: apiDomain.isprivacyprotected === "true" || userDomain.privacyProtection || false,
            period: userDomain.period || 1,
            price: userDomain.price || "N/A",
            orderId: apiDomain.orderid || userDomain.orderId || "N/A",
            orderStatus: apiDomain.orderstatus || userDomain.orderStatus || "unknown",
            // Additional API details
            productCategory: apiDomain.productcategory || "Domain",
            productKey: apiDomain.productkey || "N/A",
            customerId: apiDomain.customerid || "N/A",
            domainSecret: apiDomain.domsecret || "N/A",
            raaVerificationStatus: apiDomain.raaVerificationStatus || "Unknown",
            gdprProtection: apiDomain.gdpr || { enabled: "false", eligible: "false" },
            privacyProtectionExpiry: apiDomain.privacyprotectendtime ? formatDate(apiDomain.privacyprotectendtime) : "N/A",
            isOrderSuspendedUponExpiry: apiDomain.isOrderSuspendedUponExpiry === "true",
            orderSuspendedByParent: apiDomain.orderSuspendedByParent === "true",
            allowDeletion: apiDomain.allowdeletion === "true",
            contacts,
            // DNS records placeholder (would need separate API call)
            dnsRecords: [
              {
                id: "rec1",
                type: "A",
                name: "@",
                content: dt("use_dns_management_to_view_edit_records"),
                ttl: 3600,
              },
            ],
          };

          setDomain(combinedDomain);
        } catch (apiError) {
          console.warn("Could not fetch domain details from API:", apiError);
          setApiError(dt("could_not_fetch_complete_domain_details"));

          // Fallback to user domain data only
          const fallbackDomain = {
            id: userDomain.id,
            name: userDomain.name,
            status: userDomain.status,
            registrationDate: userDomain.registrationDate,
            expiryDate: userDomain.expiryDate,
            autoRenew: userDomain.autoRenew,
            registrar: userDomain.registrar || "ZTech Domains",
            nameservers: userDomain.nameservers || [
              "moha1280036.earth.orderbox-dns.com",
              "moha1280036.mars.orderbox-dns.com",
              "moha1280036.mercury.orderbox-dns.com",
              "moha1280036.venus.orderbox-dns.com",
            ],
            privacyProtection: userDomain.privacyProtection,
            period: userDomain.period,
            price: userDomain.price,
            orderId: userDomain.orderId,
            orderStatus: userDomain.orderStatus,
            contacts: {
              registrant: {
                name: dt("contact_information_not_available"),
                email: dt("contact_information_not_available"),
                phone: dt("contact_information_not_available"),
                address: dt("contact_information_not_available"),
              },
              admin: {
                name: dt("contact_information_not_available"),
                email: dt("contact_information_not_available"),
                phone: dt("contact_information_not_available"),
                address: dt("contact_information_not_available"),
              },
              tech: {
                name: dt("contact_information_not_available"),
                email: dt("contact_information_not_available"),
                phone: dt("contact_information_not_available"),
                address: dt("contact_information_not_available"),
              },
              billing: {
                name: dt("contact_information_not_available"),
                email: dt("contact_information_not_available"),
                phone: dt("contact_information_not_available"),
                address: dt("contact_information_not_available"),
              },
            },
            dnsRecords: [
              {
                id: "rec1",
                type: "A",
                name: "@",
                content: dt("dns_information_not_available"),
                ttl: 3600,
              },
            ],
          };

          setDomain(fallbackDomain);
        }

        setLoading(false);
      } catch (error) {
        console.error("Error getting domain details", error);
        setLoading(false);
      }
    };
    getDomainDetails();
  }, [id]);

  const handleAutoRenewToggle = async (value) => {
    try {
      // This would be replaced with actual API call when implemented
      // await domainMngService.toggleAutoRenewal(id, value);
      setDomain({ ...domain, autoRenew: value });
    } catch (error) {
      console.error("Error toggling auto renewal", error);
    }
  };

  const handlePrivacyToggle = async (value) => {
    try {
      // This would be replaced with actual API call when implemented
      // await domainMngService.togglePrivacyProtection(id, value);
      setDomain({ ...domain, privacyProtection: value });
    } catch (error) {
      console.error("Error toggling privacy protection", error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Globe className="h-6 w-6 text-blue-600" />
          </div>
          <Typography variant="h6" className="text-gray-600">
            {t("loading")}...
          </Typography>
        </div>
      </div>
    );
  }

  if (!domain) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-8">
        <Typography variant="h4" className="text-gray-800 font-bold mb-2">
          {t("domain_not_found", { defaultValue: "Domain Not Found" })}
        </Typography>
        <Button
          className="mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
          onClick={() => router.push("/client/domains")}
        >
          <ArrowLeft className="h-4 w-4" />
          {dt("back_to_domains")}
        </Button>
      </div>
    );
  }

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        <Button
          variant="text"
          className="mb-6 text-blue-600 flex items-center gap-2"
          onClick={() => router.push("/client/domains")}
        >
          <ArrowLeft className="h-4 w-4" />
          {dt("back_to_domains")}
        </Button>

        {apiError && (
          <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <Typography className="text-sm text-yellow-800">
                  <strong>{dt("warning")}:</strong> {apiError}. {dt("some_information_may_be_limited")}.
                </Typography>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div className="flex items-center">
            <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
              <Globe className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <Typography
                variant="h1"
                className="text-2xl font-bold text-gray-800"
              >
                {domain.name}
              </Typography>
              <div className="flex items-center mt-1">
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize mr-2 ${domain.status === "active"
                    ? "bg-green-100 text-green-800"
                    : domain.status === "pending"
                      ? "bg-yellow-100 text-yellow-800"
                      : domain.status === "expired"
                        ? "bg-red-100 text-red-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                >
                  {dt(domain.status)}
                </span>
                <Typography className="text-sm text-gray-500">
                  {dt("registrar")}: {domain.registrar}
                </Typography>
              </div>
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outlined"
              className="border-blue-600 text-blue-600 hover:bg-blue-50 flex items-center gap-2"
              onClick={() => window.open(`http://${domain.name}`, "_blank")}
            >
              {dt("visit_website")}
              <ExternalLink className="h-4 w-4" />
            </Button>
            <Button
              className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
              onClick={() => router.push(`/client/domains/${id}/renew`)}
            >
              {dt("renew_domain")}
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} className="mb-8">
          <TabsHeader
            className="bg-gray-100 rounded-lg p-1"
            indicatorProps={{
              className: "bg-white shadow-md rounded-md",
            }}
          >
            <Tab
              value="overview"
              onClick={() => setActiveTab("overview")}
              className={activeTab === "overview" ? "text-blue-600" : ""}
            >
              {dt("overview")}
            </Tab>
            <Tab
              value="dns"
              onClick={() => setActiveTab("dns")}
              className={activeTab === "dns" ? "text-blue-600" : ""}
            >
              {dt("dns_settings")}
            </Tab>
            <Tab
              value="contacts"
              onClick={() => setActiveTab("contacts")}
              className={activeTab === "contacts" ? "text-blue-600" : ""}
            >
              {dt("domain_contacts")}
            </Tab>
            <Tab
              value="privacy"
              onClick={() => setActiveTab("privacy")}
              className={activeTab === "privacy" ? "text-blue-600" : ""}
            >
              {dt("privacy")}
            </Tab>
          </TabsHeader>
          <TabsBody>
            <TabPanel value="overview" className="p-0 mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                  <CardBody className="p-6">
                    <Typography className="text-lg font-medium text-gray-900 mb-4">
                      {dt("domain_details")}
                    </Typography>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("domain_name")}
                        </Typography>
                        <Typography className="font-medium">
                          {domain.name}
                        </Typography>
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("status")}
                        </Typography>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${domain.status === "active"
                            ? "bg-green-100 text-green-800"
                            : domain.status === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : domain.status === "expired"
                                ? "bg-red-100 text-red-800"
                                : "bg-gray-100 text-gray-800"
                            }`}
                        >
                          {dt(domain.status)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("registration_date")}
                        </Typography>
                        <Typography className="font-medium">
                          {new Date(
                            domain.registrationDate
                          ).toLocaleDateString()}
                        </Typography>
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("expiry_date")}
                        </Typography>
                        <Typography className="font-medium">
                          {new Date(domain.expiryDate).toLocaleDateString()}
                        </Typography>
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("auto_renew")}
                        </Typography>
                        <Switch
                          checked={domain.autoRenew}
                          onChange={(e) =>
                            handleAutoRenewToggle(e.target.checked)
                          }
                          color="blue"
                          disabled={true}
                        />
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("whois_privacy")}
                        </Typography>
                        <Switch
                          checked={domain.privacyProtection}
                          onChange={(e) =>
                            handlePrivacyToggle(e.target.checked)
                          }
                          color="blue"
                        />
                      </div>
                      {domain.orderId && (
                        <div className="flex justify-between items-center">
                          <Typography className="text-sm text-gray-500">
                            {dt("order_id")}
                          </Typography>
                          <Typography className="font-medium text-xs">
                            {domain.orderId}
                          </Typography>
                        </div>
                      )}
                      {domain.customerId && (
                        <div className="flex justify-between items-center">
                          <Typography className="text-sm text-gray-500">
                            {dt("customer_id")}
                          </Typography>
                          <Typography className="font-medium text-xs">
                            {domain.customerId}
                          </Typography>
                        </div>
                      )}
                      {domain.raaVerificationStatus && (
                        <div className="flex justify-between items-center">
                          <Typography className="text-sm text-gray-500">
                            {dt("raa_verification")}
                          </Typography>
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${domain.raaVerificationStatus === "Verified"
                              ? "bg-green-100 text-green-800"
                              : domain.raaVerificationStatus === "Pending"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-red-100 text-red-800"
                              }`}
                          >
                            {domain.raaVerificationStatus}
                          </span>
                        </div>
                      )}
                      {domain.gdprProtection && (
                        <div className="flex justify-between items-center">
                          <Typography className="text-sm text-gray-500">
                            {dt("gdpr_protection")}
                          </Typography>
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${domain.gdprProtection.enabled === "true"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-gray-100 text-gray-800"
                              }`}
                          >
                            {domain.gdprProtection.enabled === "true" ? dt("enabled") : dt("disabled")}
                          </span>
                        </div>
                      )}
                    </div>
                  </CardBody>
                </Card>

                <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                  <CardBody className="p-6">
                    <Typography className="text-lg font-medium text-gray-900 mb-4">
                      {dt("nameservers")}
                    </Typography>
                    <div className="space-y-4">
                      {domain.nameservers.map((ns, index) => (
                        <div
                          key={index}
                          className="flex justify-between items-center"
                        >
                          <Typography className="text-sm text-gray-500">
                            NS {index + 1}
                          </Typography>
                          <Typography className="font-medium">{ns}</Typography>
                        </div>
                      ))}
                      <div className="mt-6">
                        <Button
                          variant="outlined"
                          className="w-full border-blue-600 text-blue-600 hover:bg-blue-50"
                          onClick={() => setActiveTab("dns")}
                        >
                          {dt("update_nameservers")}
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            </TabPanel>

            <TabPanel value="dns" className="p-0 mt-6">
              {/* Nameserver Management */}
              <div className="mb-6">
                <NameserverManager
                  domain={domain}
                  onUpdate={(updatedDomain) => setDomain(updatedDomain)}
                />
              </div>

              {/* DNS Records Tab Content */}
              <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                <CardBody className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <Typography className="text-lg font-medium text-gray-900">
                      {dt("manage_dns_records")}
                    </Typography>
                    <Button
                      className="bg-blue-600 hover:bg-blue-700"
                      onClick={() =>
                        router.push(`/client/domains/${id}/dns/add`)
                      }
                    >
                      {t("add_record", { defaultValue: "Add Record" })}
                    </Button>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="bg-gray-50 border-b border-gray-200">
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                            {t("type", { defaultValue: "Type" })}
                          </th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                            {t("name", { defaultValue: "Name" })}
                          </th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                            {t("content", { defaultValue: "Content" })}
                          </th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                            {t("ttl", { defaultValue: "TTL" })}
                          </th>
                          <th className="px-4 py-3 text-right text-sm font-medium text-gray-500">
                            {t("actions", { defaultValue: "Actions" })}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {domain.dnsRecords.map((record) => (
                          <tr key={record.id} className="hover:bg-gray-50">
                            <td className="px-4 py-3 text-sm font-medium text-gray-900">
                              {record.type}
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-500">
                              {record.name}
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-500">
                              {record.content}
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-500">
                              {record.ttl}
                            </td>
                            <td className="px-4 py-3 text-right">
                              <Button
                                size="sm"
                                variant="text"
                                className="text-blue-600"
                                onClick={() =>
                                  router.push(
                                    `/client/domains/${id}/dns/${record.id}`
                                  )
                                }
                              >
                                {t("edit", { defaultValue: "Edit" })}
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardBody>
              </Card>
            </TabPanel>

            <TabPanel value="contacts" className="p-0 mt-6">
              {/* Domain Contacts Tab Content */}
              <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                <CardBody className="p-6">
                  <Typography className="text-lg font-medium text-gray-900 mb-6">
                    {dt("domain_contacts")}
                  </Typography>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Typography className="font-medium text-gray-900 mb-2">
                        {dt("registrant")}
                      </Typography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <Typography className="font-medium">
                          {domain.contacts.registrant.name}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.registrant.email}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.registrant.phone}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.registrant.address}
                        </Typography>
                      </div>
                    </div>
                    <div>
                      <Typography className="font-medium text-gray-900 mb-2">
                        {t("admin", { defaultValue: "Administrative Contact" })}
                      </Typography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <Typography className="font-medium">
                          {domain.contacts.admin.name}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.admin.email}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.admin.phone}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.admin.address}
                        </Typography>
                      </div>
                    </div>
                    <div>
                      <Typography className="font-medium text-gray-900 mb-2">
                        {t("technical", { defaultValue: "Technical Contact" })}
                      </Typography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <Typography className="font-medium">
                          {domain.contacts.tech.name}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.tech.email}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.tech.phone}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.tech.address}
                        </Typography>
                      </div>
                    </div>
                    <div>
                      <Typography className="font-medium text-gray-900 mb-2">
                        {t("billing", { defaultValue: "Billing Contact" })}
                      </Typography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <Typography className="font-medium">
                          {domain.contacts.billing.name}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.billing.email}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.billing.phone}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.billing.address}
                        </Typography>
                      </div>
                    </div>
                  </div>
                  <div className="mt-6">
                    <Button
                      className="w-full bg-blue-600 hover:bg-blue-700"
                      onClick={() =>
                        router.push(`/client/domains/${id}/contacts`)
                      }
                    >
                      {dt("update_contacts")}
                    </Button>
                  </div>
                </CardBody>
              </Card>
            </TabPanel>

            <TabPanel value="privacy" className="p-0 mt-6">
              {/* Privacy Protection Tab Content */}
              <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                <CardBody className="p-6">
                  <Typography className="text-lg font-medium text-gray-900 mb-6">
                    {t("privacy", { defaultValue: "Privacy Protection" })}
                  </Typography>
                  <div className="text-center py-8">
                    <Shield className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <Typography className="text-gray-500 mb-4">
                      {t("privacy_content_coming_soon", {
                        defaultValue:
                          "Privacy protection settings will be available soon.",
                      })}
                    </Typography>
                    <Typography className="text-sm text-gray-400">
                      {t("privacy_description", {
                        defaultValue:
                          "Manage your domain privacy protection and WHOIS information visibility.",
                      })}
                    </Typography>
                  </div>
                </CardBody>
              </Card>
            </TabPanel>
          </TabsBody>
        </Tabs>
      </div>
    </div>
  );
}
