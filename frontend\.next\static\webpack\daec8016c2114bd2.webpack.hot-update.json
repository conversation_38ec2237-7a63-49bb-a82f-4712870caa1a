{"c": ["app/[locale]/layout", "app/[locale]/client/domains/[id]/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5C%5Blocale%5D%5Cclient%5Cdomains%5C%5Bid%5D%5Cpage.jsx&server=false!", "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx", "(app-pages-browser)/./src/components/domains/NameserverManager.jsx", "(app-pages-browser)/./src/components/domains/PrivacyProtectionManager.jsx"]}