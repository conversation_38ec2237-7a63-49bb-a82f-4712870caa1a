/**
 * Test script to verify contact creation and domain registration flow
 * This script tests the complete flow from contact creation to domain registration
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/User');
const Contact = require('../models/Contact');
const contactService = require('../services/contactService');

// Test configuration
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_CONTACT_DATA = {
  name: '<PERSON>',
  email: '<EMAIL>',
  company: 'Test Company',
  address: '123 Test Street',
  city: 'Test City',
  country: 'US',
  zipcode: '12345',
  phoneCountryCode: '1',
  phone: '5551234567'
};

async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function findOrCreateTestUser() {
  try {
    let user = await User.findOne({ email: TEST_USER_EMAIL });
    
    if (!user) {
      console.log('📝 Creating test user...');
      user = new User({
        email: TEST_USER_EMAIL,
        firstName: 'Test',
        lastName: 'User',
        password: 'test123456',
        isEmailVerified: true,
        domainContacts: {
          registrant: null,
          admin: null,
          tech: null,
          billing: null
        }
      });
      await user.save();
      console.log('✅ Test user created:', user._id);
    } else {
      console.log('✅ Test user found:', user._id);
    }
    
    return user;
  } catch (error) {
    console.error('❌ Error with test user:', error);
    throw error;
  }
}

async function testContactCreation(userId, contactType) {
  try {
    console.log(`\n🧪 Testing ${contactType} contact creation...`);
    
    const customerId = process.env.COMPANY_CUSTOMER_ID;
    if (!customerId) {
      throw new Error('COMPANY_CUSTOMER_ID not configured');
    }
    
    // Test contact creation in reseller API
    console.log('📞 Creating contact in reseller API...');
    const apiResult = await contactService.addContact(TEST_CONTACT_DATA, customerId);
    
    // Extract contact ID
    const contactId = apiResult.contactId || apiResult['contact-id'] || apiResult.id || apiResult;
    
    if (!contactId || typeof contactId !== 'string' || contactId.startsWith('fallback-')) {
      throw new Error('Failed to get valid contact ID from API');
    }
    
    console.log('✅ Contact created in reseller API with ID:', contactId);
    
    // Create contact in local database
    console.log('💾 Creating contact in local database...');
    const dbContact = new Contact({
      userId,
      externalContactId: contactId,
      contactType,
      name: TEST_CONTACT_DATA.name,
      email: TEST_CONTACT_DATA.email,
      company: TEST_CONTACT_DATA.company,
      address: TEST_CONTACT_DATA.address,
      city: TEST_CONTACT_DATA.city,
      country: TEST_CONTACT_DATA.country,
      zipcode: TEST_CONTACT_DATA.zipcode,
      phoneCountryCode: TEST_CONTACT_DATA.phoneCountryCode,
      phone: TEST_CONTACT_DATA.phone,
      customerId: customerId,
      lastSyncedAt: new Date()
    });
    
    await dbContact.save();
    console.log('✅ Contact saved to local database with ID:', dbContact._id);
    
    // Update user's contact reference
    const user = await User.findById(userId);
    user.domainContacts[contactType] = dbContact._id;
    await user.save();
    console.log('✅ User contact reference updated');
    
    return {
      dbContact,
      externalContactId: contactId
    };
    
  } catch (error) {
    console.error(`❌ Failed to create ${contactType} contact:`, error.message);
    throw error;
  }
}

async function testContactUpdate(contactId, contactType) {
  try {
    console.log(`\n🧪 Testing ${contactType} contact update...`);
    
    const updatedData = {
      ...TEST_CONTACT_DATA,
      name: 'John Doe Updated',
      company: 'Updated Test Company'
    };
    
    // Test contact update in reseller API
    console.log('📞 Updating contact in reseller API...');
    const updateResult = await contactService.modifyContact(contactId, updatedData);
    console.log('✅ Contact updated in reseller API:', updateResult);
    
    return true;
  } catch (error) {
    console.error(`❌ Failed to update ${contactType} contact:`, error.message);
    throw error;
  }
}

async function testDomainRegistrationFlow(userId) {
  try {
    console.log('\n🧪 Testing domain registration flow...');
    
    // Get user with populated contacts
    const user = await User.findById(userId).populate(
      'domainContacts.registrant domainContacts.admin domainContacts.tech domainContacts.billing'
    );
    
    if (!user) {
      throw new Error('User not found');
    }
    
    // Check that all contacts are available
    const contacts = {
      registrant: user.domainContacts.registrant?.externalContactId,
      admin: user.domainContacts.admin?.externalContactId,
      tech: user.domainContacts.tech?.externalContactId,
      billing: user.domainContacts.billing?.externalContactId
    };
    
    console.log('📋 User contacts for domain registration:', contacts);
    
    // Check for missing contacts
    const missingContacts = [];
    Object.entries(contacts).forEach(([type, contactId]) => {
      if (!contactId) {
        missingContacts.push(type);
      }
    });
    
    if (missingContacts.length > 0) {
      throw new Error(`Missing required contacts: ${missingContacts.join(', ')}`);
    }
    
    console.log('✅ All required contacts are available for domain registration');
    console.log('✅ Domain registration flow test passed');
    
    return contacts;
    
  } catch (error) {
    console.error('❌ Domain registration flow test failed:', error.message);
    throw error;
  }
}

async function runTests() {
  try {
    console.log('🚀 Starting contact and domain registration flow tests...\n');
    
    // Connect to database
    await connectToDatabase();
    
    // Find or create test user
    const user = await findOrCreateTestUser();
    
    // Test creating all contact types
    const contactTypes = ['registrant', 'admin', 'tech', 'billing'];
    const createdContacts = {};
    
    for (const contactType of contactTypes) {
      const result = await testContactCreation(user._id, contactType);
      createdContacts[contactType] = result;
    }
    
    // Test contact updates
    for (const contactType of contactTypes) {
      await testContactUpdate(createdContacts[contactType].externalContactId, contactType);
    }
    
    // Test domain registration flow
    await testDomainRegistrationFlow(user._id);
    
    console.log('\n🎉 All tests passed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('✅ Contact creation in reseller API: PASSED');
    console.log('✅ Contact storage in local database: PASSED');
    console.log('✅ Contact updates in reseller API: PASSED');
    console.log('✅ Domain registration flow: PASSED');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testContactCreation,
  testContactUpdate,
  testDomainRegistrationFlow
};
