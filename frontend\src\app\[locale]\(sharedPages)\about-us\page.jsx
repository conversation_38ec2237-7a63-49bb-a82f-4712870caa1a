"use client";
import React, { useState } from "react";
import ContactForm2 from "../../../../components/shared/contactForm2";
// import Banner from "../../../../components/about-us/banner";
// import WhyChooseUs from "../../../../components/about-us/whyChooseUs";
// import OurMission from "../../../../components/about-us/ourMission";
import { useTranslations } from "next-intl";
import Banner from "@/components/about-us/banner";
import OurMission from "@/components/about-us/ourMission";
import WhyChooseUs from "@/components/about-us/whyChooseUs";

function AboutUs() {
  const t = useTranslations("about-us");

  const [data, setData] = useState({
    page: "À propos de nous",
    offerName: "No offer selected",
    id: 0,
    url: "/about-us",
  });

  return (
    <div className="w-full h-full">
      <Banner t={t} />
      <OurMission t={t} />
      <WhyChooseUs t={t} />
      <ContactForm2 data={data} setData={setData} />
    </div>
  );
}

export default AboutUs;
