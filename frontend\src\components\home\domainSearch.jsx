"use client";

import { useState } from "react";
import { Search, Check, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import domainMngService from "@/app/services/domainMngService";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";

const DomainSearch = ({ t }) => {
  const [domain, setDomain] = useState("");
  const [searchResults, setSearchResults] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [addingToCart, setAddingToCart] = useState(false);
  const [selectedPeriods, setSelectedPeriods] = useState({}); // State to hold selected periods for each domain
  const router = useRouter();

  const handleSearch = async (e) => {
    e.preventDefault();
    const trimmedDomain = domain.trim();
    console.log("Searching for domain:", domain);
    if (!trimmedDomain) {
      setError(t ? t("domain.error_no_domain") : "Please enter a domain name");
      setSearchResults(null); // Clear previous results
      return;
    }

    setIsLoading(true);
    setError(null);
    setSearchResults(null); // Clear previous results before new search
    setSelectedPeriods({}); // Clear selected periods on new search

    try {
      // Call the new backend endpoint that handles all logic
      const response = await domainMngService.searchDomains({
        params: trimmedDomain,
      });

      const backendAvailable = response.data.available || [];
      const backendUnavailable = response.data.unavailable || [];
      const backendSuggestions = response.data.suggestions || [];
      const backendPremium = response.data.premium || [];

      let primaryPremiumDomain = null;
      // Separate the primary searched domain if it's in the premium list
      backendPremium.forEach((item) => {
        if (item.name.toLowerCase() === trimmedDomain.toLowerCase()) {
          primaryPremiumDomain = {
            name: item.name,
            pricing: {
              register: item.price,
              period: 1, // Assuming 1 year period for premium
              currency: item.currency || "MAD", // Use item.currency from backend, fallback to USD
            },
            isPremium: true,
            isAvailable: true, // Explicitly mark as available if it passed the filter
          };
        }
      });

      // Construct the final results object
      const processedResults = {
        available: [
          // Add standard available domains
          ...backendAvailable.map((item) => ({
            ...item,
            pricing: item.pricing || {
              register: {}, // Ensure register is an object even if null
              renewal: {}, // Ensure renewal is an object even if null
              period: 1,
              currency: "MAD",
            }, // Fallback if pricing is null
          })),
          // Add the primary premium domain if found and available
          ...(primaryPremiumDomain ? [primaryPremiumDomain] : []),
        ],
        unavailable: backendUnavailable, // Unavailable domains remain separate
        suggestions: [
          // Add standard suggestions
          ...backendSuggestions.map((item) => ({
            ...item,
            pricing: item.pricing || {
              register: {}, // Ensure register is an object even if null
              renewal: {}, // Ensure renewal is an object even if null
              period: 1,
              currency: "MAD",
            }, // Fallback if pricing is null
          })),
        ],
      };

      console.log("Processed results from backend:", processedResults);
      setSearchResults(processedResults);

      // Initialize selectedPeriods state with the first available period for each domain
      const initialPeriods = {};
      processedResults.available.forEach((item) => {
        // Find the first available period key, default to '1' if none found or register is empty
        const firstPeriod = Object.keys(item.pricing?.register || {})[0] || "1";
        initialPeriods[item.name] = parseInt(firstPeriod);
      });
      processedResults.suggestions.forEach((item) => {
        // Find the first available period key, default to '1' if none found or register is empty
        const firstPeriod = Object.keys(item.pricing?.register || {})[0] || "1";
        initialPeriods[item.name] = parseInt(firstPeriod);
      });
      setSelectedPeriods(initialPeriods);
    } catch (err) {
      console.error("Domain search error:", err);
      const errorMessage =
        err.response?.data?.error ||
        err.message ||
        (t ? t("domain.error_search") : "Failed to search for domain");
      setError(errorMessage);
      setSearchResults(null); // Clear results on error
      setSelectedPeriods({}); // Clear selected periods on error
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddToCart = async (domainName, tld, price, period = 1) => {
    try {
      setAddingToCart(true);

      console.log("Adding domain to cart:", {
        domain: domainName,
        tld: tld,
        price: price, // Already numeric from getPricing
        period: period, // Use the selected period
      });

      const response = await domainMngService.addDomainToCart({
        domain: domainName,
        tld: tld,
        price: price, // Already numeric
        period: period, // Use the selected period
        privacyProtection: false, // Default to false, user can change in cart
        autoRenew: false, // Always false, not user-configurable
      });

      toast.success(t ? t("domain.added_to_cart") : "Domain added to cart");
      router.push("/client/cart");
    } catch (error) {
      console.error("Error adding domain to cart:", error);
      toast.error(
        error.response?.data?.message ||
          (t
            ? t("domain.error_adding_to_cart")
            : "Failed to add domain to cart")
      );
    } finally {
      setAddingToCart(false);
    }
  };

  return (
    <div className="w-full max-w-5xl mx-auto -mt-14 relative z-20">
      <div className="bg-white rounded-lg shadow-md border-gray-50 p-6 mx-4">
        <form
          onSubmit={handleSearch}
          className="flex flex-col md:flex-row gap-3"
        >
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-blue-gray-400" />
            </div>
            <input
              type="text"
              value={domain}
              onChange={(e) => setDomain(e.target.value.toLowerCase())}
              placeholder={
                t
                  ? t("domain.search_placeholder")
                  : "Enter your domain name (e.g., example.com or just example)"
              }
              className="pl-10 w-full py-3 rounded-md"
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="bg-tertiary hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition-colors"
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                {t ? t("domain.searching") : "Searching..."}
              </span>
            ) : t ? (
              t("domain.search_button")
            ) : (
              "Search"
            )}
          </button>
        </form>

        {error && <div className="mt-4 text-red-600">{error}</div>}

        <AnimatePresence>
          {searchResults && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mt-6"
            >
              <div className="border-t border-gray-200 pt-4">
                <h4 className="font-medium text-lg mb-3">
                  {t ? t("domain.search_results") : "Search Results"}
                </h4>

                {/* Available domains */}
                {searchResults.available.map((domainItem, index) => (
                  <div
                    key={`available-${index}`}
                    className={`flex items-center justify-between p-3 border rounded-md mb-2 ${
                      domainItem.isPremium
                        ? "border-yellow-400 bg-yellow-50" // Highlight premium domains
                        : "border-green-200 bg-green-50"
                    }`}
                  >
                    <div className="flex items-center">
                      {domainItem.isPremium ? (
                        <span className="text-yellow-700 mr-2 font-semibold text-sm">
                          PREMIUM
                        </span> // Add Premium label
                      ) : (
                        <Check className="h-5 w-5 text-green-500 mr-2" />
                      )}
                      <span className="font-medium">{domainItem.name}</span>
                    </div>
                    {domainItem.pricing &&
                    domainItem.pricing.register !== null ? (
                      <>
                        <div className="flex flex-col items-end mr-3">
                          <div
                            className={`font-medium ${
                              domainItem.isPremium
                                ? "text-yellow-800"
                                : "text-green-700"
                            }`}
                          >
                            {/* Display price for the currently selected period * selected period */}
                            {!domainItem.isPremium
                              ? (
                                  (domainItem.pricing.register[
                                    selectedPeriods[domainItem.name] || 1
                                  ] || 0) *
                                  (selectedPeriods[domainItem.name] || 1)
                                )?.toFixed(2) || ""
                              : domainItem.pricing.register?.toFixed(2) || ""}
                            {domainItem.pricing.currency || "MAD"}
                          </div>
                          {!domainItem.isPremium && (
                            <div className="text-xs text-gray-500">
                              {t ? t("domain.renewal") : "Renewal"}:{" "}
                              {/* Display renewal price for the currently selected period */}
                              {domainItem.pricing.renewal[
                                selectedPeriods[domainItem.name] || 1
                              ]?.toFixed(2) || ""}{" "}
                              {domainItem.pricing.currency || "MAD"}
                            </div>
                          )}
                        </div>
                        {/* Period selection dropdown or fixed 1 year for premium */}
                        {domainItem.isPremium ? (
                          <span className="ml-2 p-1 text-sm font-medium text-gray-700">
                            1 {t ? t("domain.year") : "Year"}
                          </span>
                        ) : (
                          <select
                            className="ml-2 p-1 border rounded-md text-sm"
                            value={selectedPeriods[domainItem.name] || 1} // Default to 1 year
                            onChange={(e) =>
                              setSelectedPeriods({
                                ...selectedPeriods,
                                [domainItem.name]: parseInt(e.target.value),
                              })
                            }
                          >
                            {/* Dynamically generate options from pricing.register keys */}
                            {Object.keys(domainItem.pricing.register).map(
                              (period) => (
                                <option key={period} value={parseInt(period)}>
                                  {period} {t ? t("domain.years") : "Year(s)"}
                                </option>
                              )
                            )}
                          </select>
                        )}
                        <button
                          className={`text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2 ${
                            domainItem.isPremium
                              ? "bg-yellow-600 hover:bg-yellow-700" // Premium button style
                              : "bg-green-600 hover:bg-green-700"
                          }`}
                          onClick={() => {
                            const parts = domainItem.name.split(".");
                            const domainName = parts[0];
                            const tld = parts.slice(1).join("."); // Handles multi-part TLDs
                            // Pass the price for the selected period
                            // Calculate the total price for the selected period
                            const pricePerYear =
                              domainItem.pricing.register[
                                selectedPeriods[domainItem.name] || 1
                              ] || 0;
                            const selectedPeriod =
                              selectedPeriods[domainItem.name] || 1;
                            const totalPrice = pricePerYear * selectedPeriod;

                            handleAddToCart(
                              domainName,
                              tld,
                              totalPrice, // Pass the calculated total price
                              selectedPeriod // Pass selected period
                            );
                          }}
                          disabled={addingToCart}
                        >
                          {addingToCart ? (
                            <span>
                              {t ? t("domain.adding_to_cart") : "Adding..."}
                            </span>
                          ) : (
                            <span>
                              {t ? t("domain.add_to_cart") : "Add to Cart"}
                            </span>
                          )}
                        </button>
                      </>
                    ) : (
                      <span className="text-gray-500 mr-3">
                        {t
                          ? t("domain.pricing_unavailable")
                          : "Pricing unavailable"}
                      </span>
                    )}
                  </div>
                ))}

                {/* Unavailable domains */}
                {searchResults.unavailable.map((domainItem, index) => (
                  <div
                    key={`unavailable-${index}`}
                    className="flex items-center justify-between p-3 border border-gray-300 bg-gray-50 rounded-md mb-2"
                  >
                    <div className="flex items-center">
                      <X className="h-5 w-5 text-red-500 mr-2" />
                      <span className="font-medium">{domainItem.name}</span>
                    </div>
                    <span className="text-gray-600">{domainItem.reason}</span>
                  </div>
                ))}

                {/* Suggestions section */}
                {searchResults.suggestions &&
                  searchResults.suggestions.length > 0 && (
                    <div className="mt-6">
                      <h4 className="font-medium text-lg mb-3">
                        {t ? t("domain.suggestions") : "Suggestions"}
                      </h4>
                      <div className="grid grid-cols-1 gap-2">
                        {searchResults.suggestions.map((suggestion, index) => (
                          <div
                            key={`suggestion-${index}`}
                            className="flex items-center justify-between p-3 border border-blue-200 bg-blue-50 rounded-md mb-2"
                          >
                            <div className="flex items-center">
                              <Check className="h-5 w-5 text-blue-500 mr-2" />
                              <span className="font-medium">
                                {suggestion.name}
                              </span>
                            </div>
                            {suggestion.pricing &&
                            suggestion.pricing.register !== null ? (
                              <>
                                <div className="flex flex-col items-end mr-3">
                                  <div className="font-medium text-blue-700">
                                    {/* Display price for the currently selected period * selected period */}
                                    {(
                                      (suggestion.pricing.register[
                                        selectedPeriods[suggestion.name] || 1
                                      ] || 0) *
                                      (selectedPeriods[suggestion.name] || 1)
                                    )?.toFixed(2) || ""}{" "}
                                    {suggestion.pricing.currency || "MAD"}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {t ? t("domain.renewal") : "Renewal"}:{" "}
                                    {/* Display renewal price for the currently selected period */}
                                    {suggestion.pricing.renewal[
                                      selectedPeriods[suggestion.name] || 1
                                    ]?.toFixed(2) || ""}{" "}
                                    {suggestion.pricing.currency || "MAD"}
                                  </div>
                                </div>
                                {/* Period selection dropdown */}
                                <select
                                  className="ml-2 p-1 border rounded-md text-sm"
                                  value={selectedPeriods[suggestion.name] || 1} // Default to 1 year
                                  onChange={(e) =>
                                    setSelectedPeriods({
                                      ...selectedPeriods,
                                      [suggestion.name]: parseInt(
                                        e.target.value
                                      ),
                                    })
                                  }
                                >
                                  {/* Dynamically generate options from pricing.register keys */}
                                  {Object.keys(suggestion.pricing.register).map(
                                    (period) => (
                                      <option
                                        key={period}
                                        value={parseInt(period)}
                                      >
                                        {period}{" "}
                                        {t ? t("domain.years") : "Year(s)"}
                                      </option>
                                    )
                                  )}
                                </select>
                                <button
                                  className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2"
                                  onClick={() =>
                                    handleAddToCart(
                                      suggestion.name.split(".")[0],
                                      suggestion.name
                                        .split(".")
                                        .slice(1)
                                        .join("."), // Handles multi-part TLDs
                                      // Pass the price for the selected period
                                      suggestion.pricing.register[
                                        selectedPeriods[suggestion.name] || 1
                                      ],
                                      selectedPeriods[suggestion.name] || 1 // Pass selected period
                                    )
                                  }
                                  disabled={addingToCart}
                                >
                                  {addingToCart ? (
                                    <span>
                                      {t
                                        ? t("domain.adding_to_cart")
                                        : "Adding..."}
                                    </span>
                                  ) : (
                                    <span>
                                      {t
                                        ? t("domain.add_to_cart")
                                        : "Add to Cart"}
                                    </span>
                                  )}
                                </button>
                              </>
                            ) : (
                              <span className="text-gray-500 mr-3">
                                {t
                                  ? t("domain.pricing_unavailable")
                                  : "Pricing unavailable"}
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default DomainSearch;
