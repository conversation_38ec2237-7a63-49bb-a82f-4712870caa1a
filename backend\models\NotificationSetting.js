const mongoose = require('mongoose');

/**
 * Schema for notification settings
 * Stores configuration for different types of notifications
 */
const notificationSettingSchema = new mongoose.Schema({
  // Type of notification (e.g., abandoned_cart)
  type: {
    type: String,
    required: true,
    enum: ['abandoned_cart'], // Add more types as needed
    unique: true
  },
  
  // Whether this notification type is enabled
  enabled: {
    type: Boolean,
    default: true
  },
  
  // Time threshold in seconds (how long to wait before sending notification)
  // For abandoned_cart, this is how long a cart must be inactive before it's considered abandoned
  timeThreshold: {
    type: Number,
    required: true,
    min: 1
  },
  
  // Cron schedule for when to check and send notifications
  // Uses standard cron format (e.g., '0 0 * * *' for daily at midnight)
  cronSchedule: {
    type: String,
    required: true
  },
  
  // Whether to send email notifications
  emailEnabled: {
    type: Boolean,
    default: true
  },
  
  // Whether to send in-app notifications
  inAppEnabled: {
    type: Boolean,
    default: true
  },
  
  // Custom notification title
  notificationTitle: {
    type: String,
    default: 'Your Cart is Waiting'
  },
  
  // Custom notification message template
  // Can include placeholders like {{cartCount}} that will be replaced with actual values
  notificationMessage: {
    type: String,
    default: 'You have {{cartCount}} item(s) in your cart. Complete your purchase!'
  },
  
  // Last updated timestamp
  updatedAt: {
    type: Date,
    default: Date.now
  },
  
  // Who last updated this setting
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, { timestamps: true });

// Create a model from the schema
const NotificationSetting = mongoose.model('NotificationSetting', notificationSettingSchema);

module.exports = NotificationSetting;
