import React from "react";
import "../styles/globals.css";
import { AuthProvider } from "./context/AuthContext";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { ToastContainer } from "react-toastify";
import { GoogleTagManager } from "@next/third-parties/google";
// Dynamic metadata generation from database
export async function generateMetadata() {
  console.log("generateMetadata function called");
  try {
    // Direct fetch to backend API for server-side rendering using public endpoint
    const BACKEND_URL =
      process.env.NODE_ENV === "production"
        ? "https://api.ztechengineering.com"
        : "http://localhost:5002";

    const response = await fetch(`${BACKEND_URL}/api/public/site-settings`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      cache: "no-store", // Ensure fresh data
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    const siteSettings = result.data;
    console.log("siteSettings: ", siteSettings);

    const {
      general: { siteName },
      seo: {
        defaultTitle,
        defaultDescription,
        defaultKeywords,
        favicon,
        googleSiteVerification,
        bingVerification,
      },
    } = siteSettings;

    return {
      title:
        defaultTitle ||
        "ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco",
      description:
        defaultDescription ||
        "ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success.",
      keywords:
        defaultKeywords ||
        "web development Morocco, mobile applications Morocco, cloud hosting Morocco, digital agency, ZtechEngineering, website creation, cloud solutions, custom development, web mobile, PWA, digital transformation, web agency Morocco",
      robots: "index, follow",
      verification: {
        google:
          googleSiteVerification ||
          "RokYIbdh-kKoq7cMq7qJURkC43dc7JgI3ojch4CL0RQ",
        other: {
          "msvalidate.01": bingVerification || "",
        },
      },
      openGraph: {
        title:
          defaultTitle ||
          "ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco",
        description:
          defaultDescription ||
          "ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success.",
        url: "https://ztechengineering.com/",
        siteName: siteName || "Ztechengineering",
        images: [
          {
            url: "https://ztechengineering.com/images/home/<USER>",
            width: 800,
            height: 600,
            alt: `${siteName || "ZtechEngineering"} Logo`,
          },
        ],
        locale: "en_US",
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title:
          defaultTitle ||
          "ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco",
        description:
          defaultDescription ||
          "ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success.",
        images: ["https://ztechengineering.com/images/home/<USER>"],
        site: "@ztechengineering",
        creator: "@ztechengineering",
      },
      alternates: {
        languages: {
          en: "https://ztechengineering.com/en",
          fr: "https://ztechengineering.com/fr",
        },
      },
      icons: {
        icon: favicon || "/favicon.png",
      },
    };
  } catch (error) {
    console.error("Error fetching site settings for metadata:", error);
    // Fallback to default metadata if database fetch fails
    return {
      title:
        "ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco",
      description:
        "ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success.",
      keywords:
        "web development Morocco, mobile applications Morocco, cloud hosting Morocco, digital agency, ZtechEngineering, website creation, cloud solutions, custom development, web mobile, PWA, digital transformation, web agency Morocco",
      robots: "index, follow",
      verification: {
        google: "RokYIbdh-kKoq7cMq7qJURkC43dc7JgI3ojch4CL0RQ",
      },
      openGraph: {
        title:
          "ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco",
        description:
          "ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success.",
        url: "https://ztechengineering.com/",
        siteName: "Ztechengineering",
        images: [
          {
            url: "https://ztechengineering.com/images/home/<USER>",
            width: 800,
            height: 600,
            alt: "ZtechEngineering Logo",
          },
        ],
        locale: "en_US",
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title:
          "ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco",
        description:
          "ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success.",
        images: ["https://ztechengineering.com/images/home/<USER>"],
        site: "@ztechengineering",
        creator: "@ztechengineering",
      },
      alternates: {
        languages: {
          en: "https://ztechengineering.com/en",
          fr: "https://ztechengineering.com/fr",
        },
      },
      icons: {
        icon: "/favicon.png",
      },
    };
  }
}

export default async function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>
        {/* Dynamic Google Tag Manager from Database */}
        {/* <DynamicGTM /> */}
        <GoogleTagManager gtmId="GTM-WBVG4FCK" />
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
        <GoogleOAuthProvider clientId={process.env.GOOGLE_OAUTH_API_KEY}>
          <AuthProvider>{children}</AuthProvider>
        </GoogleOAuthProvider>
      </body>
    </html>
  );
}
