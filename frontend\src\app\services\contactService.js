import apiService from '../lib/apiService';

const contactService = {
  // Contact management
  addContact: (data) =>
    apiService.post('/contact/add', data, {
      withCredentials: true,
    }),

  modifyContact: (data) =>
    apiService.put('/contact/modify', data, {
      withCredentials: true,
    }),

  getContactDetails: (contactId) =>
    apiService.get(`/contact/${contactId}`, {
      withCredentials: true,
    }),

  searchContacts: (params) =>
    apiService.get('/contact/search', {
      params,
      withCredentials: true,
    }),

  getDefaultContact: (customerId) =>
    apiService.get(`/contact/default/${customerId}`, {
      withCredentials: true,
    }),

  associateExtraDetails: (data) =>
    apiService.post('/contact/associate-extra', data, {
      withCredentials: true,
    }),

  deleteContact: (contactId) =>
    apiService.delete(`/contact/${contactId}`, {
      withCredentials: true,
    }),

  // Customer management
  createCustomer: (data) =>
    apiService.post('/contact/customer/create', data, {
      withCredentials: true,
    }),
};

export default contactService;
