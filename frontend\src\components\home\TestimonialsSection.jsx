import React, { useEffect, useRef, useState } from "react";
import { useTranslations } from "next-intl";
import { Carousel, IconButton, Typography } from "@material-tailwind/react";
import Image from "next/image";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

// Placeholder for WhoTrustUs2 component
import WhoTrustUs2 from "./WhoTrustUs2";
import { useParams } from "next/navigation";

const testimonialsData = {
  fr: [
    {
      TestTitle: "Équipe réactive et pro",
      name: "Fawwaz Alshammari",
      company: "Galaxy Engineering",
      position: "Founder & CEO",
      website: "https://galaxy-engineering.com/",
      testimonial:
        "je suis ravi de partager mon expérience avec les services de développement web de ZTEKENGINEERING. Leur équipe a démontré une expertise exceptionnelle en créant un site web personnalisé et professionnel qui met efficacement en valeur nos produits et services. Le design est à la fois moderne et convivial, facilitant une navigation fluide pour nos clients et partenaires. Tout au long du processus de développement, ZTEKENGINEERING a maintenu une communication claire et rapide, veillant à ce que nos exigences spécifiques soient satisfaites avec précision. Leur souci du détail et leur engagement à fournir un produit de haute qualité ont considérablement renforcé notre présence en ligne. Je recommande vivement ZTEKENGINEERING à toute entreprise recherchant des solutions de développement web de premier ordre.",
    },
    {
      TestTitle: "Service et support irréprochables",
      name: "Mohamed LMOUSSAOUI",
      company: "Conseil Régional Souss Massa",
      position: "Chef du Département Informatique",
      website: "https://www.soussmassa.ma/",
      testimonial:
        "Collaborer avec ZTEKENGINEERING a été extrêmement satisfaisant. Leur équipe a constamment offert un service irréprochable que ça soit pour l'hébergement de notre site web ainsi que la résolution des problèmes de la messagerie. Ils ont tenu leurs engagements en termes de délais et de qualité de service ce qui a permis rapidement de remédier aux problèmes de dysfonctionnements de notre site internet et ont fourni une assistance exemplaire. Leur savoir-faire et leur rapidité d'intervention ont grandement amélioré le fonctionnement de nos activités.",
    },
    // {
    //   TestTitle: "Toujours utiles et présents",
    //   name: "Hicham Amakhlouf",
    //   company: "Agence Nationale pour le Développement de l'Aquaculture",
    //   position: "Responsable Informatique",
    //   website: "https://anda.gov.ma/",
    //   testimonial:
    //     "J'apprécie énormément le professionnalisme et le savoir-faire de ZTEKENGINEERING. Leur équipe est compétente, proactive et toujours très réactive face à nos besoins. Que ce soit pour l'hébergement, le support informatique ou le dépannage, ils fournissent des solutions efficaces et fiables. C'est un réel plaisir de collaborer avec eux.",
    // },
    {
      TestTitle: "Développement rapide et propre",
      name: "Tawfiq Gouach",
      company: "Kaalix",
      position: "Directeur de la Livraison SSA, GE Vernova & Fondateur",
      website: "https://kaalix.com/",
      testimonial:
        "L'équipe de ZTEKENGINEERING a démontré une expertise exceptionnelle en architecture cloud. Leur soutien a été crucial pour nous aider à réussir un audit très exigeant pour notre startup. De plus, ils ont géré notre présence sur AWS avec efficacité, garantissant performance et sécurité. Je recommande vivement leurs services à toute entreprise ayant des besoins en solutions cloud.",
    },
    {
      TestTitle: "Toujours là si besoin",
      name: "Youness Bani",
      company: "Glide Robotics",
      position: "Fondateur",
      website: "https://glide-robotics.com/",
      testimonial:
        "Je suis extrêmement reconnaissant pour le soutien et l'expertise apportés par ZTEKENGINEERING. Leur équipe a dépassé nos attentes en nous accompagnant sur tous nos besoins IT, avec un savoir-faire approfondi et un support constant. Leur engagement et leur dévouement ont joué un rôle clé dans notre succès. Je recommande vivement leurs services à toute entreprise à la recherche d'excellence en informatique.",
    },
  ],
  en: [
    {
      TestTitle: "Responsive and professional team",
      name: "Fawwaz Alshammari",
      company: "Galaxy Engineering",
      position: "Owner",
      website: "https://galaxy-engineering.com/",
      testimonial:
        "As the owner of Galaxy Engineering, I am delighted to share my experience with ZTEKENGINEERING's web development services. Their team demonstrated exceptional expertise in creating a customized, professional website that effectively showcases our products and services. The design is both modern and user-friendly, facilitating seamless navigation for our clients and partners. Throughout the development process, ZTEKENGINEERING maintained clear and prompt communication, ensuring that our specific requirements were met with precision. Their attention to detail and commitment to delivering a high-quality product have significantly enhanced our online presence. I highly recommend ZTEKENGINEERING to any business seeking top-tier web development solutions.",
    },
    {
      TestTitle: "Service and Support impeccable",
      name: "Mohamed LMOUSSAOUI",
      company: "Souss Massa Regional Council",
      position: "Head of the IT Department",
      website: "https://www.soussmassa.ma/",
      testimonial:
        "Collaborating with ZTEKENGINEERING has been extremely satisfying. Their team consistently provided impeccable service, both in hosting our website and resolving email issues. They honored their commitments in terms of deadlines and quality of service, which quickly remedied the malfunctions of our website and provided exemplary assistance. Their expertise and swift intervention have greatly improved the functioning of our activities.",
    },
    // {
    //   TestTitle: "Helpful every single time",
    //   name: "Hicham Amakhlouf",
    //   company: "National Agency for the Development of Aquaculture",
    //   position: "IT Manager",
    //   website: "https://anda.gov.ma/",
    //   testimonial:
    //     "I greatly appreciate the professionalism and expertise of ZTEKENGINEERING. Their team is competent, proactive, and always very responsive to our needs. Whether it's for hosting, IT support, or troubleshooting, they provide effective and reliable solutions. It's a real pleasure to collaborate with them.",
    // },
    {
      TestTitle: "Clean, fast web dev",
      name: "Tawfiq Gouach",
      company: "Kaalix",
      position: "Delivery Director SSA, GE Vernova & Founder",
      website: "https://kaalix.com/",
      testimonial:
        "The team at ZTEKENGINEERING demonstrated exceptional expertise in cloud architecture. Their support was crucial in helping us pass a very demanding audit for our startup. Additionally, they managed our presence on AWS efficiently, ensuring performance and security. I highly recommend their services to any company with cloud solution needs.",
    },
    {
      TestTitle: "Always there when needed",
      name: "Youness Bani",
      company: "Glide Robotics",
      position: "Founder",
      website: "https://glide-robotics.com/",
      testimonial:
        "I am extremely grateful for the support and expertise provided by ZTEKENGINEERING. Their team exceeded our expectations by assisting us with all our IT needs, offering in-depth knowledge and constant support. Their commitment and dedication have played a key role in our success. I highly recommend their services to any company seeking excellence in IT.",
    },
  ],
};

// Map testimonials based on language
const getTestimonials = (locale) => {
  const data = testimonialsData[locale] || testimonialsData.english;
  return data?.map((testimonial) => ({
    TestTitle: testimonial.TestTitle,
    text: testimonial.testimonial,
    name: testimonial.name,
    title: `${testimonial.position} @ ${testimonial.company}`,
    logo: getCompanyLogo(testimonial.company),
    avatar: getAvatar(testimonial.name),
    stars: 5,
  }));
};

// Helper functions for logos and avatars
const getCompanyLogo = (companyName) => {
  switch (companyName) {
    case "Galaxy Engineering":
      return "/images/galaxy_en.png";
    case "Souss Massa Regional Council":
      return "/images/home/<USER>/Figure-5.png";
    case "Conseil Régional Souss Massa":
      return "/images/home/<USER>/Figure-5.png";
    case "National Agency for the Development of Aquaculture":
      return "/images/home/<USER>/Figure-1.png";
    case "Agence Nationale pour le Développement de l'Aquaculture":
      return "/images/home/<USER>/Figure-1.png";
    case "Kaalix":
      return "/images/home/<USER>/Figure-4.png";
    case "Glide Robotics":
      return "/images/home/<USER>/Figure-6.png";
    default:
      return "/logos/default.png";
  }
};

const getAvatar = (name) => {
  switch (name) {
    case "Fawwaz Alshammari":
      return "/images/fawaz.png";
    case "Mohamed LMOUSSAOUI":
      return "/images/mohamed.png";
    // case "Hicham Amakhlouf":
    //   return "/images/hicham.png";
    case "Tawfiq Gouach":
      return "/images/tawfiq.png";
    case "Youness Bani":
      return "/images/younes.png";
    default:
      return "/images/default.jpg";
  }
};

const avatars = [
  "/images/fawaz.png",
  "/images/mohamed.png",
  "/images/hicham.png",
  "/images/tawfiq.png",
  "/images/younes.png",
  "/images/fawaz.png",
  "/images/mohamed.png",
  // "/images/hicham.png",
  "/images/tawfiq.png",
  "/images/younes.png",
];

const StarRating = ({ count }) => (
  <div className="flex items-center gap-1.5">
    {[...Array(count)].map((_, i) => (
      <svg
        key={i}
        className="w-5 h-5 text-yellow-600 transition-transform hover:scale-110"
        fill="currentColor"
        viewBox="0 0 20 20"
        aria-hidden="true"
      >
        <polygon points="10,1 12.59,7.36 19.51,7.36 13.96,11.64 16.55,18 10,13.72 3.45,18 6.04,11.64 0.49,7.36 7.41,7.36" />
      </svg>
    ))}
  </div>
);

const TestimonialCard = ({ testimonial, t }) => {
  const [expanded, setExpanded] = useState(false);
  const [needsTruncation, setNeedsTruncation] = useState(false);
  const textRef = useRef(null);

  useEffect(() => {
    if (textRef.current) {
      // Check if text height exceeds 12rem (192px)
      setNeedsTruncation(textRef.current.scrollHeight > 192);
    }
  }, [testimonial.text]);

  return (
    <div
      className="relative bg-white/80 backdrop-blur-lg border border-white/20 rounded-xl shadow-lg p-6 md:p-8 flex flex-col max-w-md min-h-[550px] h-full mx-auto transition-all duration-300 hover:shadow-xl hover:-translate-y-1"
      role="article"
    >
      <div className="mb-6 text-center">
        <Typography variant="h6" className="font-semibold text-gray-800">
          {testimonial.TestTitle}
        </Typography>
      </div>
      <div className="relative">
        <Typography
          variant="paragraph"
          ref={textRef}
          className={`text-sm md:text-base text-gray-600 mb-6 flex-grow text-center transition-all ${
            expanded ? "max-h-none" : "max-h-48 overflow-hidden"
          }`}
        >
          {testimonial.text}
        </Typography>
        {needsTruncation && (
          <button
          name="expand"
            onClick={() => setExpanded(!expanded)}
            className="absolute bottom-0 left-0 right-0 text-center bg-transparent pt-6 pb-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            {expanded ? "See Less" : "See More"}
          </button>
        )}
      </div>
      <div className="flex flex-col items-center gap-4 mt-8 justify-center">
        <div className="relative">
          <Image
            src={testimonial.avatar}
            alt={testimonial.name}
            width={120}
            height={120}
            quality={100}
            className="rounded-full border-2 border-white shadow-sm object-cover"
          />
          <div className="absolute w-14 h-14 flex items-center justify-center -top-4 -right-4 bg-white rounded-full p-1 shadow-md">
            <Image
              src={testimonial.logo}
              alt={`${testimonial.title.split("@")[1]} logo`}
              width={40}
              height={40}
              quality={100}
            />
          </div>
        </div>
        <div className="flex-1 text-center">
          <Typography variant="small" className="font-bold text-gray-900">
            {testimonial.name}
          </Typography>
          <Typography
            variant="small"
            className="text-gray-500 text-xs md:text-sm"
          >
            {testimonial.title}
          </Typography>
          <div className="flex mt-2 justify-center">
            <StarRating count={testimonial.stars} />
          </div>
        </div>
      </div>
    </div>
  );
};

const AvatarsSlider = () => {
  const avatarSliderSettings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 2000,
    arrows: false,
    centerMode: true,
    centerPadding: "0px",
  };

  return (
    <div className="mx-auto px-10 w-[25rem] overflow-hidden relative rounded-md">
      <Slider {...avatarSliderSettings}>
        {avatars.map((src, idx) => (
          <div key={idx} className="flex justify-center items-center px-1">
            <Image
              src={src}
              alt={`Avatar ${idx + 1}`}
              width={60}
              height={60}
              className="rounded-full border-2 border-white shadow-sm object-cover"
              quality={100}
            />
          </div>
        ))}
      </Slider>
    </div>
  );
};

const TestimonialsSection = () => {
  const t = useTranslations("Home");
  const [isMobile, setIsMobile] = useState(false);
  const params = useParams();
  const locale = params.locale || "en";
  const testimonials = getTestimonials(locale);

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth < 768);
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const CustomArrow = ({ onClick, direction }) => {
    return (
      <button
      name="Arrow"
        onClick={onClick}
        className={`absolute ${
          direction === "left" ? "left-0" : "right-0"
        } top-1/2 transform -translate-y-1/2 z-10 bg-white p-2 rounded-full shadow-md hover:bg-gray-100`}
      >
        <svg
          className="w-6 h-6 text-black"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          {direction === "left" ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          )}
        </svg>
      </button>
    );
  };

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: isMobile ? 1 : 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 5000,
    cssEase: "linear",
    arrows: !isMobile,
    prevArrow: <CustomArrow direction="left" />,
    nextArrow: <CustomArrow direction="right" />,
    appendDots: (dots) => (
      <div>
        <ul style={{ display: "flex", margin: "0px" }}> {dots} </ul>
      </div>
    ),
    customPaging: (i) => (
      <div
        style={{
          width: "24px",
          height: "5px",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <div className="custom-dot"></div>
      </div>
    ),
    responsive: [
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
          arrows: false,
        },
      },
    ],
  };

  return (
    <section className="w-[97%] mx-auto rounded-lg my-4 py-12 md:py-20 bg-gradient-to-br from-[#F5F6FF8C] via-[#D7E0FDC6] to-[#E7F5FB] backdrop-blur-lg">
      <div className="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="text-center mb-12 md:mb-16 relative flex flex-col items-center">
          {/* Desktop: Quotes left/right of testimonials */}
          <div className="hidden md:flex w-full justify-between absolute left-0 right-0 top-1/2 -translate-y-1/2 pointer-events-none z-0">
            <span className="ml-2">
              {/* Left yellow quote SVG */}
              <svg
                width="90"
                height="68"
                viewBox="0 0 90 68"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M86.6839 6.45806C80.0282 9.8678 74.6987 13.8228 70.7479 18.328L70.7472 18.3288C66.4357 23.2518 63.958 29.2013 63.2873 36.1253L63.128 37.7699H64.7803H70.1719C74.0444 37.7699 77.0801 38.2171 79.3713 39.0393C81.7037 39.8845 83.5122 41.0263 84.8604 42.448C86.1946 43.859 87.1254 45.3879 87.6581 47.0282L87.6592 47.0316C88.2221 48.7514 88.5007 50.5389 88.5007 52.3843C88.5007 56.0495 86.9986 59.2924 83.8363 62.1456L83.8362 62.1457C80.6715 65.0015 76.9843 66.4186 72.6973 66.4186C65.7024 66.4186 60.5181 64.2087 56.9194 59.926C53.2755 55.5895 51.3743 49.4767 51.3743 41.4174C51.3743 34.1721 54.5359 26.7322 61.1505 19.0643C67.4959 11.7181 75.0745 6.08624 83.8954 2.13708L86.6839 6.45806ZM37.3894 6.45821C30.7375 9.86774 25.4037 13.8225 21.4506 18.3277L21.4494 18.3291C17.1403 23.2523 14.6654 29.2016 13.9922 36.1248L13.8323 37.7699H15.4852H20.8792C24.7545 37.7699 27.7896 38.2172 30.0754 39.0391C32.4121 39.8849 34.2225 41.0271 35.5649 42.4477L35.5661 42.4489C36.9003 43.8577 37.8298 45.3862 38.3679 47.0288C38.9255 48.7533 39.2079 50.5421 39.2079 52.3843C39.2079 56.0501 37.7078 59.2928 34.5459 62.1456L34.5458 62.1457C31.3813 65.0013 27.6895 66.4186 23.4046 66.4186C16.4044 66.4186 11.2208 64.2085 7.6272 59.9267L7.62633 59.9256C3.98067 55.5897 2.08398 49.4775 2.08398 41.4174C2.08398 34.1719 5.2433 26.7319 11.8577 19.0643C18.2057 11.7179 25.78 6.08612 34.6023 2.13712L37.3894 6.45821Z"
                  fill="#F4BA07"
                  stroke="#F4BA07"
                  stroke-width="3"
                />
              </svg>
            </span>
            <span className="mr-2">
              {/* Right yellow quote SVG (mirrored) */}
              <svg
                width="90"
                height="68"
                viewBox="0 0 90 68"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3.31609 6.45806C9.97177 9.8678 15.3013 13.8228 19.2521 18.328L19.2528 18.3288C23.5643 23.2518 26.042 29.2013 26.7127 36.1253L26.872 37.7699H25.2197H19.8281C15.9556 37.7699 12.9199 38.2171 10.6287 39.0393C8.29634 39.8845 6.48776 41.0263 5.13958 42.448C3.80544 43.859 2.87459 45.3879 2.34193 47.0282L2.34084 47.0316C1.77791 48.7514 1.49934 50.5389 1.49934 52.3843C1.49934 56.0495 3.00136 59.2924 6.16371 62.1456L6.1638 62.1457C9.32853 65.0015 13.0157 66.4186 17.3027 66.4186C24.2976 66.4186 29.4819 64.2087 33.0806 59.926C36.7245 55.5895 38.6257 49.4767 38.6257 41.4174C38.6257 34.1721 35.4641 26.7322 28.8495 19.0643C22.5041 11.7181 14.9255 6.08624 6.10458 2.13708L3.31609 6.45806ZM52.6106 6.45821C59.2625 9.86774 64.5963 13.8225 68.5494 18.3277L68.5506 18.3291C72.8597 23.2523 75.3346 29.2016 76.0078 36.1248L76.1677 37.7699H74.5148H69.1208C65.2455 37.7699 62.2104 38.2172 59.9246 39.0391C57.5879 39.8849 55.7775 41.0271 54.4351 42.4477L54.4339 42.4489C53.0997 43.8577 52.1702 45.3862 51.6321 47.0288C51.0745 48.7533 50.7921 50.5421 50.7921 52.3843C50.7921 56.0501 52.2922 59.2928 55.4541 62.1456L55.4542 62.1457C58.6187 65.0013 62.3105 66.4186 66.5954 66.4186C73.5956 66.4186 78.7792 64.2085 82.3728 59.9267L82.3737 59.9256C86.0193 55.5897 87.916 49.4775 87.916 41.4174C87.916 34.1719 84.7567 26.7319 78.1423 19.0643C71.7943 11.7179 64.22 6.08612 55.3977 2.13712L52.6106 6.45821Z"
                  fill="#F4BA07"
                  stroke="#F4BA07"
                  stroke-width="3"
                />
              </svg>
            </span>
          </div>
          {/* Mobile: Quotes next to title */}
          <div className="flex md:hidden items-center justify-center gap-2 mb-2">
            <span>
              <svg
                width="40"
                height="38"
                viewBox="0 0 90 68"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M86.6839 6.45806C80.0282 9.8678 74.6987 13.8228 70.7479 18.328L70.7472 18.3288C66.4357 23.2518 63.958 29.2013 63.2873 36.1253L63.128 37.7699H64.7803H70.1719C74.0444 37.7699 77.0801 38.2171 79.3713 39.0393C81.7037 39.8845 83.5122 41.0263 84.8604 42.448C86.1946 43.859 87.1254 45.3879 87.6581 47.0282L87.6592 47.0316C88.2221 48.7514 88.5007 50.5389 88.5007 52.3843C88.5007 56.0495 86.9986 59.2924 83.8363 62.1456L83.8362 62.1457C80.6715 65.0015 76.9843 66.4186 72.6973 66.4186C65.7024 66.4186 60.5181 64.2087 56.9194 59.926C53.2755 55.5895 51.3743 49.4767 51.3743 41.4174C51.3743 34.1721 54.5359 26.7322 61.1505 19.0643C67.4959 11.7181 75.0745 6.08624 83.8954 2.13708L86.6839 6.45806ZM37.3894 6.45821C30.7375 9.86774 25.4037 13.8225 21.4506 18.3277L21.4494 18.3291C17.1403 23.2523 14.6654 29.2016 13.9922 36.1248L13.8323 37.7699H15.4852H20.8792C24.7545 37.7699 27.7896 38.2172 30.0754 39.0391C32.4121 39.8849 34.2225 41.0271 35.5649 42.4477L35.5661 42.4489C36.9003 43.8577 37.8298 45.3862 38.3679 47.0288C38.9255 48.7533 39.2079 50.5421 39.2079 52.3843C39.2079 56.0501 37.7078 59.2928 34.5459 62.1456L34.5458 62.1457C31.3813 65.0013 27.6895 66.4186 23.4046 66.4186C16.4044 66.4186 11.2208 64.2085 7.6272 59.9267L7.62633 59.9256C3.98067 55.5897 2.08398 49.4775 2.08398 41.4174C2.08398 34.1719 5.2433 26.7319 11.8577 19.0643C18.2057 11.7179 25.78 6.08612 34.6023 2.13712L37.3894 6.45821Z"
                  fill="#F4BA07"
                  stroke="#F4BA07"
                  stroke-width="3"
                />
              </svg>
            </span>
            <Typography
              variant="h2"
              className="text-2xl sm:text-3xl font-bold text-gray-900 mb-0"
            >
              {t("testimonialsTitle") || "Trusted by Industry Leaders"}
            </Typography>
            <span>
              <svg
                width="40"
                height="38"
                viewBox="0 0 90 68"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3.31609 6.45806C9.97177 9.8678 15.3013 13.8228 19.2521 18.328L19.2528 18.3288C23.5643 23.2518 26.042 29.2013 26.7127 36.1253L26.872 37.7699H25.2197H19.8281C15.9556 37.7699 12.9199 38.2171 10.6287 39.0393C8.29634 39.8845 6.48776 41.0263 5.13958 42.448C3.80544 43.859 2.87459 45.3879 2.34193 47.0282L2.34084 47.0316C1.77791 48.7514 1.49934 50.5389 1.49934 52.3843C1.49934 56.0495 3.00136 59.2924 6.16371 62.1456L6.1638 62.1457C9.32853 65.0015 13.0157 66.4186 17.3027 66.4186C24.2976 66.4186 29.4819 64.2087 33.0806 59.926C36.7245 55.5895 38.6257 49.4767 38.6257 41.4174C38.6257 34.1721 35.4641 26.7322 28.8495 19.0643C22.5041 11.7181 14.9255 6.08624 6.10458 2.13708L3.31609 6.45806ZM52.6106 6.45821C59.2625 9.86774 64.5963 13.8225 68.5494 18.3277L68.5506 18.3291C72.8597 23.2523 75.3346 29.2016 76.0078 36.1248L76.1677 37.7699H74.5148H69.1208C65.2455 37.7699 62.2104 38.2172 59.9246 39.0391C57.5879 39.8849 55.7775 41.0271 54.4351 42.4477L54.4339 42.4489C53.0997 43.8577 52.1702 45.3862 51.6321 47.0288C51.0745 48.7533 50.7921 50.5421 50.7921 52.3843C50.7921 56.0501 52.2922 59.2928 55.4541 62.1456L55.4542 62.1457C58.6187 65.0013 62.3105 66.4186 66.5954 66.4186C73.5956 66.4186 78.7792 64.2085 82.3728 59.9267L82.3737 59.9256C86.0193 55.5897 87.916 49.4775 87.916 41.4174C87.916 34.1719 84.7567 26.7319 78.1423 19.0643C71.7943 11.7179 64.22 6.08612 55.3977 2.13712L52.6106 6.45821Z"
                  fill="#F4BA07"
                  stroke="#F4BA07"
                  stroke-width="3"
                />
              </svg>
            </span>
          </div>
          {/* Testimonials label (always visible) */}
          <span className="inline-block bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-semibold mb-4 shadow-sm relative z-10">
            {t("testimonialsLabel") || "Client Testimonials"}
          </span>
          {/* Desktop: Title and subtitle */}
          <div className="hidden md:block relative z-10">
            <Typography
              variant="h2"
              className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-4"
            >
              {t("testimonialsTitle") || "Trusted by Industry Leaders"}
            </Typography>
            <Typography
              variant="paragraph"
              className="max-w-3xl mx-auto text-base md:text-lg text-gray-600"
            >
              {t("testimonialsSubtitle") ||
                "Hear from our clients about their experience working with us."}
            </Typography>
          </div>
          {/* Rating and Avatars */}
          <div className="mt-8 flex flex-col items-center relative z-10">
            <Typography variant="h4">{t("Excellent")}</Typography>
            <div className="flex items-center gap-0.5">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="p-1 bg-yellow-600">
                  <svg
                    className="w-5 h-5 text-black transition-transform hover:scale-110"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    aria-hidden="true"
                  >
                    <polygon points="10,1 12.59,7.36 19.51,7.36 13.96,11.64 16.55,18 10,13.72 3.45,18 6.04,11.64 0.49,7.36 7.41,7.36" />
                  </svg>
                </div>
              ))}
            </div>
            <div className="flex items-center justify-center gap-3 mb-3">
              <Typography
                variant="small"
                className="font-semibold text-gray-700"
              >
                4.9/5
              </Typography>
              <Typography variant="small" className="text-gray-500">
                {t("testimonialsReviews", { count: testimonials?.length }) ||
                  `Based on ${testimonials?.length} reviews`}
              </Typography>
            </div>
            <div className="flex items-center justify-center">
              <Image src={"/images/review_google.png"} alt="Google Review" width={60} height={12} />
            </div>
          </div>
          {/* Avatars slider at the top */}
          <AvatarsSlider />
        </div>

        {/* Carousel Section */}
        <div className="relative py-8 max-w-full overflow-hidden">
          <Slider {...settings}>
            {testimonials?.map((testimonial, idx) => (
              <div key={idx} className="px-4">
                <TestimonialCard testimonial={testimonial} t={t} />
              </div>
            ))}
          </Slider>
          <style jsx global>{`
            .slick-dots {
              display: flex !important;
              justify-content: center;
              align-items: center;
              gap: 1rem !important;
              bottom: -32px;
              left: 0;
              right: 0;
              width: 100%;
              position: absolute;
              padding: 0;
              margin: 0;
              list-style: none;
            }
            .slick-dots li {
              margin: 0;
              padding: 0;
              width: 35px;
              height: 5px;
              display: flex;
              align-items: center;
            }
            .slick-dots li .custom-dot {
              background: grey;
              width: 30px;
              height: 4px;
              margin-right: 20px;
              border-radius: 10px;
              transition: background 0.3s, width 0.3s;
            }
            .slick-dots li.slick-active .custom-dot {
              background: #1363df;
            }
          `}</style>
        </div>
      </div>

      {/* Trusted by Logos */}
      <div className="mt-12">
        <WhoTrustUs2 t={t} />
      </div>
    </section>
  );
};

export default TestimonialsSection;
