"use client";

import React, {
  createContext,
  useState,
  useEffect,
  useContext,
  useMemo,
} from "react";
import { useRouter } from "next/navigation";
import authService from "../services/authService";

// Create the Auth Context
const AuthContext = createContext();

// Create a Provider Component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [cartCount, setCartCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Check if user exists in localStorage on mount
  useEffect(() => {
    const initializeAuth = async () => {
      await checkAuth();
      setLoading(false);
    };

    initializeAuth();
  }, []);

  const checkAuth = async () => {
    setLoading(true);
    try {
      const response = await authService.checkAuth();
      const updatedUser = response.data.user;
      console.log("Fetched user:", updatedUser);

      // Update only if the data is different
      if (JSON.stringify(updatedUser) !== localStorage.getItem("user")) {
        localStorage.setItem("user", JSON.stringify(updatedUser));
        document.cookie = `role=${updatedUser.role}; max-age=604800; path=/; secure`;
      }

      setUser(updatedUser);
      return updatedUser;
    } catch (err) {
      console.error("Auth check failed:", err);
      setUser(null);
      localStorage.removeItem("user");
    } finally {
      setLoading(false);
    }
  };

  // Handle authentication error
  const handleAuthError = (error) => {
    const message =
      error.response?.data?.message || "An unexpected error occurred";
    console.error(error);
    return error;
  };

  // Login function
  const login = async (credentials) => {
    setLoading(true);
    try {
      const loginRes = await authService.login(credentials);
      const userData = loginRes.data.user;
      setUser(userData);

      // Store user in localStorage
      localStorage.setItem("user", JSON.stringify(userData));
      document.cookie = `role=${userData.role}; max-age=604800; path=/; secure`;

      // Check for pending cart items
      const pendingItemJson = localStorage.getItem("pendingCartItem");
      if (pendingItemJson) {
        try {
          // We'll handle this in a separate function after login completes
          // Just mark that we have a pending item
          loginRes.data.hasPendingCartItem = true;
        } catch (cartError) {
          console.error("Error handling pending cart item:", cartError);
        }
      }

      return loginRes;
    } catch (error) {
      const detailedError = {
        status: error.response?.status,
        data: error.response?.data,
      };
      throw detailedError;
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    setLoading(true);
    try {
      await authService.logout();
      // Clear user from localStorage
      localStorage.removeItem("user");
      // Clear cookies on logout
      document.cookie = "role=; Max-Age=0; path:/";
      document.cookie = "refresh_token=; Max-Age=0; path=/;";
      document.cookie = "token=; Max-Age=0; path=/;";

      setUser(null);
      router.refresh();
      router.push("/auth/login"); // Redirect to login page after logout
    } catch (error) {
      console.log(
        "Logout error:",
        error.response?.data?.message || error.message
      );
    } finally {
      setLoading(false);
    }
  };

  // Compute if user is authenticated
  const isAuthenticated = !!user;

  const value = useMemo(
    () => ({
      user,
      loading,
      login,
      logout,
      checkAuth,
      cartCount,
      setCartCount,
      isAuthenticated,
    }),
    [user, loading, cartCount, checkAuth, isAuthenticated]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook for using AuthContext
export const useAuth = () => useContext(AuthContext);
