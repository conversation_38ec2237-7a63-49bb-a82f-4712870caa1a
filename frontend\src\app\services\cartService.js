import apiService from "../lib/apiService";

const cartService = {
  // Get the user's cart
  getCart: () => apiService.get("/cart/get-cart", { withCredentials: true }),

  // Add an item to the cart
  addItemToCart: (data) =>
    apiService.post("/cart/add-item", data, { withCredentials: true }),

  // Remove an item from the cart
  removeItemFromCart: (data) =>
    apiService.post("/cart/remove-item", data, { withCredentials: true }),

  // Clear the user's cart
  clearCart: () => apiService.post("/cart/clear", { withCredentials: true }),

  // Update item quantity in the cart
  updateCartItemQuantity: (data) =>
    apiService.put("/cart/update-item", data, { withCredentials: true }),

  // Update item period in the cart
  updateItemPeriod: (data) =>
    apiService.put("/cart/update-item-period", data, { withCredentials: true }),

  // Add method to remove domain from cart
  removeDomainFromCart: (data) =>
    apiService.post("/cart/remove-domain", data, { withCredentials: true }),

  // Add method to update domain period
  updateDomainPeriod: (data) =>
    apiService.post("/cart/update-domain-period", data, {
      withCredentials: true,
    }),

  // Add method to update domain options
  updateDomainOptions: (data) =>
    apiService.post("/cart/update-domain-options", data, {
      withCredentials: true,
    }),
};

export default cartService;
