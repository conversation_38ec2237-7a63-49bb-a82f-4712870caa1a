const axios = require("axios");

// const DOMAIN_CHECK_API_BASE_URL = "https://domaincheck.httpapi.com/api";
// const API_BASE_URL = "https://httpapi.com/api";

const DOMAIN_CHECK_API_BASE_URL =
  "https://domain-name-api.dynv6.net/domaincheck";
const API_BASE_URL = "https://domain-name-api.dynv6.net/";
const API_BASE_URL_TEST = "https://domain-name-api.dynv6.net/test";

const AUTH_PARAMS = {
  "auth-userid": process.env.AUTH_USERID_PROD,
  "api-key": process.env.API_KEY_PROD,
};

const AUTH_PARAMS_TEST = {
  "auth-userid": process.env.AUTH_USERID_TEST,
  "api-key": process.env.API_KEY_TEST,
};

// Check Domain Availability
exports.checkDomainAvailability = async (req, res) => {
  const { domain, tld } = req.query.params;
  console.log("req.query: ", req.query.params);
  try {
    const response = await axios.get(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/available.json`,
      {
        params: { ...AUTH_PARAMS, "domain-name": domain, tlds: tld },
      }
    );
    console.log("check domain availability: ", response.data);
    res.json(response.data);
  } catch (error) {
    console.log("error: ", error);
    res.status(500).json({ error: error.message });
  }
};

// Check IDN Domain Availability
exports.checkIdnDomainAvailability = async (req, res) => {
  const { domains, tld, idnLanguageCode } = req.query.params;
  console.log("checkidnavailability: ", req.query);

  try {
    const params = {
      ...AUTH_PARAMS,
      tld, // Changed from tlds to tld
      idnLanguageCode,
    };

    // Handle multiple domain names
    if (Array.isArray(domains)) {
      // Add each domain as a separate domain-name parameter
      domains.forEach((domain) => {
        params["domain-name"] = domain;
      });
    } else {
      params["domain-name"] = domains;
    }

    const response = await axios.get(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/idn-available.json`,
      { params }
    );

    // Process the response
    const result = response.data;
    console.log("check idn domain availability: ", result);
    const formattedResponse = {
      domain: Object.keys(result)[0],
      available: result[Object.keys(result)[0]].status !== "regthroughothers",
      status: result[Object.keys(result)[0]].status,
      classKey: result[Object.keys(result)[0]].classkey,
    };
    res.json(formattedResponse);
  } catch (error) {
    console.log("error: ", error);
    res.status(500).json({ error: error.message });
  }
};

// Check Premium Domain Availability
exports.checkPremiumDomainAvailability = async (req, res) => {
  console.log("req.query: ", req.query);
  const { keyword, tlds, priceHigh, priceLow, numberOfResults } = req.query;

  try {
    const response = await axios.get(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/premium/available.json`,
      {
        params: {
          ...AUTH_PARAMS,
          "key-word": keyword,
          tlds: Array.isArray(tlds) ? tlds.join(",") : tlds,
          "price-high": priceHigh,
          "price-low": priceLow,
          "no-of-results": numberOfResults || 10,
        },
      }
    );
    res.json(response.data);
  } catch (error) {
    console.log("error: ", error);
    res.status(500).json({ error: error.message });
  }
};

// Suggest Domain Names
exports.suggestDomainNames = async (req, res) => {
  const {
    keyword,
    tldOnly,
    exactMatch = true,
    adult = false,
  } = req.query.params;

  console.log("req.query params:", req.query.params);

  try {
    // Input validation
    if (!keyword) {
      return res.status(400).json({
        error: "Keyword is required",
      });
    }

    // Validate keyword format (a-z, A-Z, 0-9, space and hyphen)
    const keywordRegex = /^[a-zA-Z0-9\s-]+$/;
    if (!keywordRegex.test(keyword)) {
      return res.status(400).json({
        error: "Keyword can only contain letters, numbers, spaces, and hyphens",
      });
    }

    const params = {
      ...AUTH_PARAMS,
      keyword,
      "exact-match": exactMatch ? true : false,
      adult: adult ? true : false,
    };

    // Add tld-only parameter if provided
    if (tldOnly) {
      params["tld-only"] = tldOnly;
    }

    // console.log("Sending suggestion request with params:", params);

    const response = await axios.get(
      `${API_BASE_URL}/domains/v5/suggest-names.json`,
      { params }
    );

    // console.log("Suggestion API raw response:", response.data);

    // Return the raw suggestions data
    res.json(response.data);
  } catch (error) {
    console.error(
      "Suggestion API error:",
      error.response?.data || error.message
    );
    res.status(500).json({ error: error.message });
  }
};

// Get Domain Pricing
const tldMapping = require("../utils/tldMapping");

exports.getDNPricing = async (req, res) => {
  const { customerId } = req.query;
  console.log("customerId: ", customerId);
  try {
    const params = {
      ...AUTH_PARAMS,
    };

    if (customerId) {
      params["customer-id"] = customerId;
    }

    const response = await axios.get(
      `${API_BASE_URL}/products/customer-price.json`,
      { params }
    );

    // Create a standardized response with TLD mapping
    const pricingData = response.data;
    const enhancedResponse = {
      rawData: pricingData,
      mappedData: {},
    };

    // Add mapped pricing data for easier frontend consumption
    const allTlds = tldMapping.getAllTlds();
    for (const tld in allTlds) {
      const pricing = tldMapping.getTldPricing(tld, pricingData);
      if (pricing) {
        enhancedResponse.mappedData[tld] = pricing;
      }
    }

    console.log("getDNPricing sample:", enhancedResponse.mappedData.com);
    res.json(enhancedResponse);
  } catch (error) {
    console.log("error: ", error);
    res.status(500).json({ error: error.message });
  }
};

// Add domain to cart
exports.addDomainToCart = async (req, res) => {
  const { domain, tld, price, period = 1 } = req.body;
  console.log("Request body:", req.body);

  try {
    // Validate input
    if (!domain || !tld) {
      return res.status(400).json({
        success: false,
        message: "Domain name and TLD are required",
      });
    }

    // Check if domain is available before adding to cart
    const availabilityResponse = await axios.get(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/available.json`,
      {
        params: { ...AUTH_PARAMS, "domain-name": domain, tlds: tld },
      }
    );

    const domainKey = `${domain}.${tld}`;
    const isAvailable =
      availabilityResponse.data[domainKey]?.status === "available";

    if (!isAvailable) {
      return res.status(400).json({
        success: false,
        message: "Domain is not available for registration",
      });
    }

    // If price is not provided, get it from the API
    let domainPrice = parseFloat(price) || 0;
    if (!domainPrice) {
      return res.status(400).json({
        success: false,
        message: "Price is required",
      });
    }

    // Create a cart item for the domain
    const Cart = require("../models/Cart");

    // Use req.user._id if available, otherwise create a temporary ID for guest users
    const userId = req.user?._id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }

    let cart = await Cart.findOne({ user: userId });

    if (!cart) {
      cart = new Cart({
        user: userId,
        isGuest: !req.user?._id,
        items: [],
      });
    }

    // Add domain to cart using the new method
    await cart.addDomain(domainKey, tld, domainPrice, period);

    res.status(200).json({
      success: true,
      message: "Domain added to cart",
      cart,
    });
  } catch (error) {
    console.error("Error adding domain to cart:", error);
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
};

// Get Reseller Pricing
exports.getResellerPricing = async (req, res) => {
  // const { resellerId } = req.query.params;
  // console.log("getResellerPricing: ", req.query.params);
  try {
    const params = {
      ...AUTH_PARAMS,
    };

    params["reseller-id"] = AUTH_PARAMS["auth-userid"];
    // if (resellerId) {
    // }

    const response = await axios.get(
      `${API_BASE_URL}/products/reseller-price.json`,
      { params }
    );

    res.json(response.data);
  } catch (error) {
    console.log("error: ", error);
    res.status(500).json({ error: error.message });
  }
};
// Customers Authentication
exports.customerSignup = async (req, res) => {
  const {
    username,
    passwd,
    name,
    company,
    addressLine1,
    city,
    state,
    country,
    zipcode,
    phoneCc,
    phone,
    langPref = "en",
  } = req.body;

  try {
    const response = await axios.post(
      `${API_BASE_URL}/customers/v2/signup.json`,
      null,
      {
        params: {
          ...AUTH_PARAMS,
          username,
          passwd,
          name,
          company,
          "address-line-1": addressLine1,
          city,
          state,
          country,
          zipcode,
          "phone-cc": phoneCc,
          phone,
          "lang-pref": langPref,
        },
      }
    );
    res.json(response.data);
  } catch (error) {
    console.error(
      "Customer signup error:",
      error.response?.data || error.message
    );
    res.status(500).json({
      error: error.response?.data?.message || error.message,
      details: error.response?.data,
    });
  }
};

// Register Domain
exports.registerDomain = async (req, res) => {
  const {
    domain, // domain name to register
    years, // number of years
    customerId, // customer ID
    ns1,
    ns2, // nameservers
    regContactId, // registrant contact ID
    adminContactId, // admin contact ID
    techContactId, // technical contact ID
    billingContactId, // billing contact ID
    autoRenew, // enable/disable auto renewal
    privacyProtection, // enable/disable privacy protection
  } = req.body;

  try {
    // Base parameters required for all domain registrations
    const params = {
      ...AUTH_PARAMS,
      "domain-name": domain,
      years,
      "customer-id": customerId,
      "reg-contact-id": regContactId,
      "admin-contact-id": adminContactId,
      "tech-contact-id": techContactId,
      "billing-contact-id": billingContactId,
      "invoice-option": "KeepInvoice",
      "auto-renew": autoRenew || false,
      ns: [ns1, ns2],
    };

    // Add privacy protection if requested
    if (privacyProtection) {
      params["purchase-privacy"] = true;
      params["protect-privacy"] = true;
    }

    const response = await axios.post(
      `${API_BASE_URL}/domains/register.json`,
      null,
      { params }
    );

    res.json(response.data);
  } catch (error) {
    console.error(
      "Domain registration error:",
      error.response?.data || error.message
    );
    res.status(500).json({
      error: error.response?.data?.message || error.message,
      details: error.response?.data,
    });
  }
};

// Transfer Domain
exports.transferDomain = async (req, res) => {
  const { domain, tld, customerId, authCode } = req.body;
  try {
    const response = await axios.post(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/transfer.json`,
      null,
      {
        params: {
          ...AUTH_PARAMS,
          "domain-name": domain,
          tlds: tld,
          "customer-id": customerId,
          "auth-code": authCode,
          "invoice-option": "KeepInvoice",
        },
      }
    );
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Renew Domain
exports.renewDomain = async (req, res) => {
  const { orderId, years } = req.body;
  try {
    const response = await axios.post(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/renew.json`,
      null,
      {
        params: {
          ...AUTH_PARAMS,
          "order-id": orderId,
          years,
          "invoice-option": "KeepInvoice",
        },
      }
    );
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get Domain Details
exports.getDomainDetails = async (req, res) => {
  const { domain, tld } = req.query;
  try {
    const response = await axios.get(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/details-by-name.json`,
      {
        params: { ...AUTH_PARAMS, "domain-name": domain, tlds: tld },
      }
    );
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Modify Name Servers
exports.modifyNameServers = async (req, res) => {
  const { orderId, ns1, ns2 } = req.body;
  try {
    const response = await axios.post(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/modify-ns.json`,
      null,
      {
        params: {
          ...AUTH_PARAMS,
          "order-id": orderId,
          ns: [ns1, ns2],
        },
      }
    );
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Enable Privacy Protection
exports.enablePrivacyProtection = async (req, res) => {
  const { orderId } = req.body;
  try {
    const response = await axios.post(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/modify-privacy-protection.json`,
      null,
      {
        params: {
          ...AUTH_PARAMS,
          "order-id": orderId,
          "protect-privacy": true,
        },
      }
    );
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Disable Privacy Protection
exports.disablePrivacyProtection = async (req, res) => {
  const { orderId } = req.body;
  try {
    const response = await axios.post(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/modify-privacy-protection.json`,
      null,
      {
        params: {
          ...AUTH_PARAMS,
          "order-id": orderId,
          "protect-privacy": false,
        },
      }
    );
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Enable Theft Protection Lock
exports.enableTheftProtectionLock = async (req, res) => {
  const { orderId } = req.body;
  try {
    const response = await axios.post(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/enable-theft-protection.json`,
      null,
      {
        params: { ...AUTH_PARAMS, "order-id": orderId },
      }
    );
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Disable Theft Protection Lock
exports.disableTheftProtectionLock = async (req, res) => {
  const { orderId } = req.body;
  try {
    const response = await axios.post(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/disable-theft-protection.json`,
      null,
      {
        params: { ...AUTH_PARAMS, "order-id": orderId },
      }
    );
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Delete Domain
exports.deleteDomain = async (req, res) => {
  const { orderId } = req.body;
  try {
    const response = await axios.post(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/delete.json`,
      null,
      {
        params: { ...AUTH_PARAMS, "order-id": orderId },
      }
    );
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Restore Domain
exports.restoreDomain = async (req, res) => {
  const { orderId } = req.body;
  try {
    const response = await axios.post(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/restore.json`,
      null,
      {
        params: { ...AUTH_PARAMS, "order-id": orderId },
      }
    );
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Update Domain Contacts
exports.updateDomainContacts = async (req, res) => {
  const { orderId, registrant, admin, tech, billing } = req.body;
  try {
    const response = await axios.post(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/modify-contact.json`,
      null,
      {
        params: {
          ...AUTH_PARAMS,
          "order-id": orderId,
          "registrant-contact-id": registrant,
          "admin-contact-id": admin,
          "tech-contact-id": tech,
          "billing-contact-id": billing,
        },
      }
    );
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
