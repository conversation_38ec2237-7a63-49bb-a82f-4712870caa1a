"use client";

import { useEffect, useState } from 'react';
import Script from 'next/script';

export default function DynamicGTM() {
  const [gtmId, setGtmId] = useState('GTM-WBVG4FCK'); // Default fallback
  const [loading, setLoading] = useState(true);
  const [gtmLoaded, setGtmLoaded] = useState(false);

  useEffect(() => {
    // Check if GTM is already loaded to prevent duplicates
    if (window.dataLayer && window.gtag) {
      console.log('GTM already loaded, skipping...');
      setLoading(false);
      setGtmLoaded(true);
      return;
    }

    const fetchGTMId = async () => {
      try {
        const BACKEND_URL = process.env.NODE_ENV === 'production'
          ? 'https://api.ztechengineering.com'
          : 'http://localhost:5002';

        const response = await fetch(`${BACKEND_URL}/api/public/site-settings`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          cache: 'no-store',
        });

        if (response.ok) {
          const result = await response.json();
          const googleTagManagerId = result.data?.seo?.googleTagManagerId;

          if (googleTagManagerId && googleTagManagerId !== gtmId) {
            console.log('GTM ID from database:', googleTagManagerId);
            setGtmId(googleTagManagerId);
          }
        }
      } catch (error) {
        console.error('Error fetching GTM ID:', error);
        // Keep default GTM ID
      } finally {
        setLoading(false);
      }
    };

    fetchGTMId();
  }, []);

  if (loading || gtmLoaded) {
    return null; // Don't render anything while loading or if already loaded
  }

  return (
    <>
      {/* Google Tag Manager Script */}
      <Script
        id="dynamic-gtm-script"
        strategy="afterInteractive"
        onLoad={() => {
          console.log('GTM loaded successfully with ID:', gtmId);
          setGtmLoaded(true);
        }}
        dangerouslySetInnerHTML={{
          __html: `
            // Prevent duplicate GTM loading
            if (!window.dataLayer) {
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());

              (function(w,d,s,l,i){
                if (w[l] && w[l].length > 0) return; // Already loaded
                w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','${gtmId}');

              console.log('GTM initialized with ID: ${gtmId}');
            }
          `,
        }}
      />

      {/* Google Tag Manager NoScript */}
      <noscript>
        <iframe
          src={`https://www.googletagmanager.com/ns.html?id=${gtmId}`}
          height="0"
          width="0"
          style={{ display: "none", visibility: "hidden" }}
        ></iframe>
      </noscript>
    </>
  );
}
