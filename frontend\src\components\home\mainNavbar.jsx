"use client";
import React, { useState, useRef, useEffect } from "react";
import SiteBranding from "./siteBranding";
import { Button } from "@material-tailwind/react";
import Link from "next/link";
import NavDropDown from "../shared/navDropDown";
import {
  FaFacebook,
  FaInstagram,
  FaLinkedin,
  FaTiktok,
  FaYoutube,
} from "react-icons/fa";
import { AvatarWithUserDropdown } from "../auth/avatarWithUserDropdown";
import { usePathname, useRouter } from "next/navigation";

const navItems = [
  {
    id: 0,
    title: "home",
    url: "/",
    items: [],
  },
  {
    id: 1,
    title: "services.web_creation",
    icon: (
      <svg
        width="25"
        height="25"
        viewBox="0 0 28 28"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          x="1"
          y="1"
          width="26"
          height="26"
          rx="2"
          stroke="black"
          strokeWidth="2"
        />
        <circle cx="5.125" cy="4.5" r="1.125" fill="black" />
        <circle cx="8.5" cy="4.5" r="1.125" fill="black" />
        <circle cx="11.875" cy="4.5" r="1.125" fill="black" />
        <line x1="28" y1="9" y2="9" stroke="black" strokeWidth="2" />
        <path
          d="M16.918 20.4167L19.8346 17.5L16.918 14.5833M11.0846 14.5833L8.16797 17.5L11.0846 20.4167M15.168 12.25L12.8346 22.75"
          stroke="black"
          strokeWidth="1.16667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    rightTitle: "launch_with_confidence",
    description: "contact_for_quote",
    url: "/web-development",
    items: [
      {
        title: "items.0.title",
        pricing: "items.0.pricing",
      },
      {
        title: "items.1.title",
      },
      {
        title: "items.2.title",
      },
    ],
  },
  {
    id: 2,
    title: "services.hosting",
    icon: (
      <svg
        width="27"
        height="27"
        viewBox="0 0 28 28"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clip-path="url(#clip0_884_5636)">
          <path d="M26.25 17.5H17.5V19.25H26.25V17.5Z" fill="black" />
          <path d="M22.75 21H17.5V22.75H22.75V21Z" fill="black" />
          <path
            d="M26.2509 14.8749V13.9999C26.2485 11.4587 25.4552 8.98125 23.981 6.91135C22.5069 4.84146 20.4249 3.28177 18.0242 2.44874C15.6234 1.6157 13.0228 1.55064 10.5834 2.26257C8.14398 2.9745 5.98665 4.42812 4.4108 6.4217C2.83496 8.41528 1.91875 10.85 1.78933 13.3879C1.6599 15.9257 2.32368 18.441 3.68855 20.5845C5.05341 22.7281 7.05167 24.3936 9.40603 25.35C11.7604 26.3064 14.354 26.5062 16.8271 25.9218L16.4246 24.2187C15.6303 24.406 14.8169 24.5004 14.0009 24.4999C13.8346 24.4999 13.6727 24.4836 13.5082 24.476C11.6096 21.6298 10.5732 18.2959 10.5235 14.8749H26.2509ZM24.4646 13.1249H19.2291C19.1318 9.90101 18.3018 6.74159 16.8021 3.88611C18.869 4.46104 20.7107 5.65415 22.0802 7.30546C23.4498 8.95677 24.2817 10.9873 24.4646 13.1249ZM14.4935 3.52377C16.3921 6.36999 17.4285 9.70389 17.4782 13.1249H10.5235C10.5732 9.70389 11.6096 6.36999 13.5082 3.52377C13.6727 3.51616 13.8346 3.49989 14.0009 3.49989C14.1671 3.49989 14.329 3.51616 14.4935 3.52377ZM11.1996 3.88611C9.69994 6.74159 8.86997 9.90101 8.77265 13.1249H3.53718C3.71997 10.9873 4.55193 8.95677 5.9215 7.30546C7.29106 5.65415 9.13276 4.46104 11.1996 3.88611ZM11.1996 24.1137C9.13276 23.5387 7.29106 22.3456 5.9215 20.6943C4.55193 19.043 3.71997 17.0124 3.53718 14.8749H8.77265C8.86997 18.0988 9.69994 21.2582 11.1996 24.1137Z"
            fill="black"
          />
        </g>
        <defs>
          <clipPath id="clip0_884_5636">
            <rect width="28" height="28" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    rightTitle: "launch_with_confidence",
    description: "contact_for_quote",
    url: "/hosting",
    items: [
      {
        title: "items.3.title",
        pricing: "items.3.pricing",
      },
      {
        title: "items.4.title",
        pricing: "items.4.pricing",
      },
      {
        title: "items.5.title",
        pricing: "items.5.pricing",
      },
    ],
  },
  {
    id: 3,
    title: "services.ssl",
    icon: (
      <svg
        width="30"
        height="30"
        viewBox="0 0 32 32"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.99919 25.2188C5.39089 24.6893 3.42773 22.3833 3.42773 19.6188C3.42773 16.8542 5.39089 14.5482 7.99919 14.0188C7.99917 14.0125 7.99916 14.0061 7.99916 13.9998C7.99916 9.94974 11.2824 6.6665 15.3325 6.6665C19.3825 6.6665 22.6659 9.94974 22.6659 13.9998C22.6659 14.2245 22.6557 14.4469 22.6359 14.6665H22.6659C25.6113 14.6665 27.9992 17.0544 27.9992 19.9998C27.9992 22.4849 26.2995 24.573 23.9992 25.1652"
          stroke="black"
          strokeWidth="1.8"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12 21.9049C12 21.2737 12.5117 20.762 13.1429 20.762H18.8572C19.4883 20.762 20 21.2737 20 21.9049V25.524C20 26.155 19.4883 26.6668 18.8572 26.6668H13.1429C12.5117 26.6668 12 26.155 12 25.524V21.9049Z"
          stroke="black"
          strokeWidth="1.8"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M14.2852 19.0478C14.2852 18.101 15.0526 17.3335 15.9994 17.3335C16.9462 17.3335 17.7137 18.101 17.7137 19.0478V20.762H14.2852V19.0478Z"
          stroke="black"
          strokeWidth="1.8"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    url: "/ssl",
    rightTitle: "launch_with_confidence",
    description: "contact_for_quote",
    items: [
      {
        title: "items.18.title",
      },
      {
        title: "items.19.title",
      },
      {
        title: "items.20.title",
      },
    ],
  },
  {
    id: 4,
    title: "services.cloud_morocco",
    icon: (
      <svg
        width="26"
        height="26"
        viewBox="0 0 26 26"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M25.5897 10.2747L15.7655 10.2625L12.7301 0.650391L9.88795 10.1791L0 10.1752L8.2314 15.986L8.21126 15.9801L5.42956 25.6308L12.778 19.5538L20.3236 25.5942L17.4163 16.0021L25.5897 10.2747ZM12.7301 7.20279L13.6893 10.2605H11.8284L12.7301 7.20279ZM6.13508 12.1807L9.29036 12.1866L8.75083 13.997L6.13508 12.1807ZM9.04484 20.2057L9.92634 17.1826L11.4289 18.2455L9.04484 20.2057ZM10.3915 15.1385L11.2611 12.1846L14.1987 12.1783L15.1501 15.1873L13.0035 16.951L10.3915 15.1385ZM14.3487 18.2592L15.7535 17.0968L16.7209 20.1628L14.3487 18.2592ZM16.2551 12.1739L19.4206 12.1676L16.8086 13.9941L16.2551 12.1739Z"
          fill="black"
        />
      </svg>
    ),
    rightTitle: "launch_with_confidence",
    description: "contact_for_quote",
    url: "/cloud-maroc",
    items: [
      {
        title: "items.6.title",
        pricing: "items.6.pricing",
      },
      {
        title: "items.7.title",
      },
      {
        title: "items.8.title",
      },
    ],
  },
  {
    id: 5,
    title: "services.cloud_security",
    icon: (
      <svg
        width="30"
        height="30"
        viewBox="0 0 32 32"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.99919 25.2188C5.39089 24.6893 3.42773 22.3833 3.42773 19.6188C3.42773 16.8542 5.39089 14.5482 7.99919 14.0188C7.99917 14.0125 7.99916 14.0061 7.99916 13.9998C7.99916 9.94974 11.2824 6.6665 15.3325 6.6665C19.3825 6.6665 22.6659 9.94974 22.6659 13.9998C22.6659 14.2245 22.6557 14.4469 22.6359 14.6665H22.6659C25.6113 14.6665 27.9992 17.0544 27.9992 19.9998C27.9992 22.4849 26.2995 24.573 23.9992 25.1652"
          stroke="black"
          strokeWidth="1.8"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12 21.9049C12 21.2737 12.5117 20.762 13.1429 20.762H18.8572C19.4883 20.762 20 21.2737 20 21.9049V25.524C20 26.155 19.4883 26.6668 18.8572 26.6668H13.1429C12.5117 26.6668 12 26.155 12 25.524V21.9049Z"
          stroke="black"
          strokeWidth="1.8"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M14.2852 19.0478C14.2852 18.101 15.0526 17.3335 15.9994 17.3335C16.9462 17.3335 17.7137 18.101 17.7137 19.0478V20.762H14.2852V19.0478Z"
          stroke="black"
          strokeWidth="1.8"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    rightTitle: "launch_with_confidence",
    description: "contact_for_quote",
    url: "/cloud-security",
    items: [
      {
        title: "items.9.title",
      },
      {
        title: "items.10.title",
      },
      {
        title: "items.11.title",
      },
    ],
  },
  {
    id: 6,
    title: "services.managed_services",
    icon: (
      <svg
        width="28"
        height="28"
        viewBox="0 0 28 28"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clip-path="url(#clip0_1381_6575)">
          <path
            d="M18.4222 15.2588C17.3943 15.2588 16.4289 15.6584 15.7043 16.384C14.9783 17.1095 14.5781 18.0745 14.5781 19.1018C14.5781 20.1282 14.9777 21.0937 15.7043 21.8202C16.4303 22.5457 17.3953 22.9453 18.4222 22.9453C19.4486 22.9453 20.414 22.5457 21.14 21.8202C21.8661 21.0936 22.2657 20.1282 22.2657 19.1018C22.2657 18.0749 21.8661 17.1096 21.1406 16.3845C20.415 15.6584 19.4495 15.2588 18.4222 15.2588ZM20.6755 21.3551C20.0733 21.9569 19.2735 22.2882 18.4222 22.2882C17.5713 22.2882 16.7705 21.9569 16.1688 21.3551C15.5667 20.7535 15.2353 19.9532 15.2353 19.1018C15.2353 18.2505 15.5666 17.4502 16.1688 16.849C16.7705 16.2468 17.5713 15.9155 18.4222 15.9155C19.2735 15.9155 20.0733 16.2468 20.6755 16.849C21.2772 17.4502 21.6085 18.2505 21.6085 19.1018C21.6085 19.9532 21.2772 20.7535 20.6755 21.3551Z"
            fill="black"
          />
          <path
            d="M27.2903 17.8424L25.242 17.5456C25.1137 17.5357 25.0031 17.4551 24.9534 17.3357L24.2898 15.7337C24.2402 15.6147 24.2608 15.479 24.3448 15.3817L25.5698 13.7354C25.765 13.5069 25.7522 13.1671 25.5394 12.9548L24.57 11.9854C24.4585 11.8733 24.311 11.8168 24.1635 11.8168C24.0308 11.8168 23.8976 11.8626 23.7899 11.955L22.1432 13.1805C22.0808 13.2341 22.0016 13.2621 21.922 13.2621C21.8778 13.2621 21.8335 13.2532 21.7918 13.236L20.1888 12.5714C20.0703 12.5227 19.9891 12.4106 19.9789 12.2833L19.6815 10.235C19.6584 9.9351 19.4086 9.7041 19.1088 9.7041H17.7373C17.437 9.7041 17.1872 9.9351 17.1641 10.235L16.8663 12.2833C16.8565 12.4106 16.7758 12.5228 16.6569 12.5719L15.0539 13.236C15.0116 13.2532 14.9674 13.262 14.9232 13.262C14.8435 13.262 14.7648 13.234 14.702 13.1804L13.0562 11.9554C12.9485 11.863 12.8153 11.8168 12.6831 11.8168C12.5351 11.8168 12.3877 11.8738 12.2756 11.9859L11.3062 12.9548C11.0938 13.1676 11.0811 13.5068 11.2757 13.7354L12.5007 15.3816C12.5838 15.4789 12.6054 15.6146 12.5563 15.7336L11.8922 17.3361C11.844 17.4551 11.7319 17.5357 11.6046 17.5455L9.55526 17.8429C9.25645 17.8665 9.02539 18.1162 9.02539 18.4166V19.7871C9.02539 20.0879 9.25645 20.3376 9.55526 20.3612L11.6046 20.6581C11.7319 20.6685 11.844 20.7496 11.8922 20.868L12.5563 22.471C12.6059 22.589 12.5838 22.7256 12.5007 22.8229L11.2757 24.4692C11.0811 24.6967 11.0938 25.0369 11.3062 25.2493L12.2756 26.2187C12.3877 26.3307 12.5351 26.3872 12.6826 26.3872C12.8153 26.3872 12.9486 26.3415 13.0562 26.2491L14.702 25.0236C14.7649 24.97 14.8436 24.9425 14.9232 24.9425C14.9675 24.9425 15.0116 24.9508 15.0539 24.9685L16.6568 25.6328C16.7758 25.6819 16.8569 25.793 16.8667 25.9213L17.1641 27.9697C17.1872 28.2685 17.4369 28.5 17.7373 28.5H19.1087C19.4091 28.5 19.6583 28.2685 19.6819 27.9697L19.9788 25.9213C19.9891 25.7931 20.0702 25.682 20.1887 25.6328L21.7917 24.9686C21.834 24.9509 21.8782 24.9426 21.9224 24.9426C22.002 24.9426 22.0812 24.9701 22.1436 25.0237L23.7899 26.2492C23.8975 26.3416 24.0307 26.3873 24.1635 26.3873C24.311 26.3873 24.4584 26.3308 24.57 26.2188L25.5394 25.2494C25.7527 25.0371 25.765 24.6968 25.5698 24.4693L24.3448 22.823C24.2623 22.7257 24.2401 22.589 24.2898 22.4711L24.9533 20.8681C25.0031 20.7496 25.1136 20.6685 25.2419 20.6582L27.2903 20.3613C27.5896 20.3377 27.8207 20.088 27.8207 19.7881V18.4167C27.8207 18.1162 27.5897 17.8665 27.2903 17.8424ZM27.1635 19.7153L25.1633 20.0053C24.8001 20.0441 24.4894 20.2757 24.3464 20.6168L23.6832 22.2173C23.5411 22.5575 23.5967 22.9419 23.8272 23.228L25.0237 24.8359L24.1566 25.703L22.5492 24.5065C22.3723 24.3635 22.1506 24.2848 21.9225 24.2848C21.7892 24.2848 21.6595 24.3114 21.5405 24.3615L19.936 25.0261C19.5964 25.1677 19.3649 25.4788 19.326 25.8425L19.036 27.8427H17.8101L17.5196 25.843C17.4812 25.4793 17.2502 25.1677 16.9085 25.0251L15.3075 24.3624C15.1851 24.3108 15.0558 24.2848 14.9232 24.2848C14.696 24.2848 14.4748 24.3634 14.2974 24.5059L12.689 25.7029L11.8223 24.8358L13.0183 23.2279C13.2493 22.9418 13.3049 22.5569 13.1634 22.2191L12.5007 20.6196C12.3611 20.2775 12.0494 20.0445 11.6837 20.0051L9.68257 19.7151V18.4881L11.6828 18.1981C12.0499 18.1598 12.3621 17.9258 12.4992 17.5876L13.1623 15.987C13.3049 15.6469 13.2493 15.262 13.0183 14.9764L11.8218 13.368L12.6895 12.5009L14.2963 13.6973C14.4738 13.8408 14.695 13.919 14.9231 13.919C15.0549 13.919 15.1836 13.8934 15.3055 13.8429L16.908 13.1787C17.2487 13.0381 17.4812 12.726 17.519 12.3613L17.81 10.3616H19.036L19.3259 12.3622C19.3653 12.727 19.5979 13.0387 19.937 13.1782L21.54 13.8429C21.6614 13.8929 21.7902 13.919 21.9219 13.919C22.1505 13.919 22.3722 13.8404 22.5492 13.6973L24.1565 12.5009L25.0237 13.368L23.8282 14.9744C23.5962 15.2596 23.5401 15.6449 23.6827 15.9846L24.3463 17.5876C24.4878 17.9277 24.7996 18.1597 25.1638 18.1981L27.1634 18.4881L27.1635 19.7153Z"
            fill="black"
          />
          <path
            d="M9.33247 14.7416L10.3422 14.3729C10.5639 14.2923 10.6853 14.0549 10.6223 13.8282L10.2906 12.2396C10.264 12.1412 10.294 12.038 10.3678 11.9702L11.3701 11.05C11.4197 11.0043 11.4846 10.9802 11.55 10.9802C11.5819 10.9802 11.6139 10.9856 11.6438 10.9964L13.1859 11.457C13.2385 11.4767 13.2926 11.486 13.3457 11.4855C13.5157 11.485 13.6775 11.3872 13.7532 11.225L14.2059 10.2502C14.3067 10.0364 14.2241 9.78228 14.0191 9.66673L12.6746 8.78385C12.5871 8.73425 12.5345 8.64035 12.5395 8.53907L12.5975 7.17992C12.6019 7.07913 12.6624 6.99065 12.7528 6.94843L14.1823 6.17865C14.3971 6.0813 14.4998 5.83455 14.4192 5.61383L14.051 4.6037C13.9846 4.42126 13.8116 4.30675 13.6258 4.3073C13.586 4.30779 13.5457 4.31266 13.5058 4.32403L11.9171 4.65483C11.8934 4.66173 11.8694 4.66517 11.8458 4.66517C11.7721 4.66566 11.7003 4.63422 11.6482 4.57822L10.729 3.57634C10.6601 3.50219 10.639 3.39642 10.6748 3.30258L11.135 1.76001C11.2171 1.53934 11.1168 1.29352 10.9024 1.19372L9.92769 0.74003C9.86573 0.711483 9.80032 0.697756 9.73596 0.697756C9.57818 0.698686 9.42675 0.782303 9.34472 0.927772L8.46135 2.2727C8.41367 2.35626 8.32518 2.40783 8.22932 2.40833H8.21756L6.85786 2.34937C6.75757 2.34445 6.66711 2.28402 6.62588 2.19308L5.85664 0.764475C5.78145 0.598335 5.61673 0.499077 5.44419 0.500006C5.39355 0.500006 5.34198 0.508866 5.29183 0.527022L4.28214 0.896163C4.06043 0.976827 3.93853 1.21373 4.00246 1.44036L4.33376 3.02958C4.36078 3.12643 4.33081 3.23012 4.2566 3.29798L3.25428 4.21821C3.2037 4.26442 3.13977 4.28854 3.07387 4.28903C3.04242 4.28903 3.01049 4.28312 2.97948 4.27131L1.43795 3.81221C1.38539 3.79203 1.33131 3.78273 1.27821 3.78273C1.10862 3.78366 0.947347 3.88106 0.871222 4.04326L0.418464 5.01851C0.318167 5.23184 0.400253 5.48548 0.605277 5.60197L1.94971 6.48533C2.03771 6.5345 2.08982 6.6284 2.08485 6.7282L2.02688 8.08833C2.02294 8.18863 1.96147 8.27761 1.87102 8.31988L0.441597 9.08966C0.227277 9.18695 0.124519 9.43326 0.205128 9.65398L0.573339 10.6641C0.640167 10.8465 0.813198 10.962 0.999027 10.9615C1.03835 10.961 1.07816 10.9551 1.11797 10.9438L2.70768 10.6125C2.73082 10.6061 2.75439 10.6027 2.77752 10.6027C2.85173 10.6027 2.92397 10.6332 2.9756 10.6892L3.89533 11.692C3.96413 11.7657 3.98425 11.8719 3.94942 11.9663L3.48928 13.5079C3.40725 13.7286 3.50798 13.9748 3.72181 14.0752L4.69607 14.5283C4.75846 14.5569 4.82387 14.5706 4.88824 14.5706C5.04607 14.5696 5.19744 14.4866 5.27903 14.3411L6.1624 12.9966C6.20954 12.9131 6.2971 12.8609 6.39192 12.8609H6.40669L7.76589 12.9194C7.86663 12.9234 7.95659 12.9838 7.99843 13.0752L8.76771 14.5047C8.8434 14.6699 9.00757 14.7692 9.1806 14.7687C9.23075 14.7681 9.28232 14.7598 9.33247 14.7416ZM8.58429 12.7769C8.43439 12.4726 8.13356 12.2764 7.79384 12.2631L6.43961 12.2052C6.42288 12.2042 6.4062 12.2032 6.38946 12.2032C6.07042 12.2052 5.77155 12.3742 5.60535 12.6466L4.81107 13.8563L4.16122 13.554L4.57515 12.1693C4.68376 11.8473 4.6095 11.4959 4.37997 11.2481L3.46177 10.2468C3.28622 10.0541 3.03608 9.94448 2.775 9.94591C2.70227 9.94591 2.62948 9.95476 2.55921 9.97248L1.12924 10.2704L0.884402 9.59787L2.17032 8.90575C2.4746 8.75388 2.67077 8.4525 2.68307 8.11584L2.74159 6.75965C2.75778 6.41949 2.58819 6.10291 2.29769 5.92692L1.08942 5.13351L1.39125 4.48366L2.77796 4.89754C2.87432 4.93002 2.97456 4.94572 3.07682 4.94572C3.30684 4.94473 3.52663 4.85926 3.69867 4.70192L4.69803 3.78465C4.95118 3.55463 5.05542 3.21054 4.97333 2.88017L4.67594 1.45168L5.34838 1.20591L6.03996 2.49178C6.1904 2.79513 6.49025 2.99124 6.82997 3.00546L8.19109 3.06447L8.23189 3.06545C8.55339 3.06398 8.85275 2.8939 9.01889 2.62106L9.81328 1.41132L10.4631 1.71358L10.0497 3.09739C9.93961 3.41983 10.0134 3.77081 10.2444 4.01953L11.1641 5.02184C11.3395 5.21401 11.5893 5.32317 11.8483 5.32169C11.9221 5.3212 11.9948 5.3124 12.0666 5.29566L13.4951 4.99729L13.7398 5.67022L12.4534 6.36333C12.1531 6.51279 11.9574 6.81067 11.9407 7.15181L11.8822 8.50702C11.8659 8.84773 12.0356 9.16426 12.3266 9.34123L13.5343 10.1351L13.2325 10.7854L11.8463 10.371C11.7499 10.3386 11.6497 10.3224 11.5469 10.3229C11.3154 10.3233 11.0942 10.4109 10.9256 10.5657L9.92228 11.4864C9.67307 11.7164 9.57031 12.0596 9.65048 12.3879L9.94886 13.8173L9.27587 14.0622L8.58429 12.7769Z"
            fill="black"
          />
          <path
            d="M10.1732 8.96561C10.5287 8.2007 10.5655 7.34342 10.2765 6.55198C9.98698 5.76011 9.40745 5.12792 8.64451 4.77349C8.21838 4.57443 7.76612 4.47462 7.30056 4.47659C6.93684 4.47807 6.57699 4.54293 6.23093 4.66931C5.43807 4.95833 4.80594 5.53791 4.45052 6.30228C4.09511 7.06572 4.05825 7.92245 4.34678 8.71586C4.63684 9.50828 5.21691 10.1404 5.97981 10.4948C6.40549 10.6929 6.8577 10.7932 7.3232 10.7912C7.68748 10.7893 8.04732 10.7244 8.39388 10.5985C9.18477 10.3096 9.81641 9.72949 10.1732 8.96561ZM8.16818 9.98116C7.89245 10.0819 7.60588 10.1326 7.32025 10.1336C6.95745 10.1355 6.59564 10.0568 6.25702 9.89908C5.6519 9.61738 5.19378 9.11693 4.9642 8.49022C4.73616 7.86251 4.76459 7.18417 5.04629 6.57949C5.32799 5.97388 5.82887 5.51478 6.45559 5.28673C6.73181 5.18545 7.01843 5.13481 7.30357 5.13383C7.66637 5.13229 8.02763 5.21099 8.36631 5.36882C8.97094 5.65046 9.43004 6.1509 9.65913 6.77762C9.88772 7.40489 9.85863 8.08274 9.57748 8.68835C9.29485 9.29347 8.79495 9.75213 8.16818 9.98116Z"
            fill="black"
          />
        </g>
        <defs>
          <clipPath id="clip0_1381_6575">
            <rect
              width="28"
              height="28"
              fill="white"
              transform="translate(0 0.5)"
            />
          </clipPath>
        </defs>
      </svg>
    ),
    rightTitle: "launch_with_confidence",
    description: "contact_for_quote",
    url: "/managed-services",
    items: [
      {
        title: "items.12.title",
      },
      {
        title: "items.13.title",
      },
      {
        title: "items.14.title",
      },
    ],
  },
  // {
  //   id: 7,
  //   title: "services.servers",
  //   icon: (
  //     <svg
  //       width="26"
  //       height="26"
  //       viewBox="0 0 26 26"
  //       fill="none"
  //       xmlns="http://www.w3.org/2000/svg"
  //     >
  //       <path
  //         d="M20.5833 9.74935C20.5833 13.9375 17.1882 17.3327 13 17.3327M20.5833 9.74935C20.5833 5.56119 17.1882 2.16602 13 2.16602M20.5833 9.74935H5.41667M13 17.3327C8.81184 17.3327 5.41667 13.9375 5.41667 9.74935M13 17.3327C14.8968 15.2561 15.9757 12.5612 16.0343 9.74935C15.9757 6.93747 14.8968 4.2426 13 2.16602M13 17.3327C11.1032 15.2561 10.0262 12.5612 9.96765 9.74935C10.0262 6.93747 11.1032 4.2426 13 2.16602M13 17.3327V19.4993M13 2.16602C8.81184 2.16602 5.41667 5.56119 5.41667 9.74935M13 19.4993C14.1967 19.4993 15.1667 20.4694 15.1667 21.666M13 19.4993C11.8034 19.4993 10.8333 20.4694 10.8333 21.666M15.1667 21.666C15.1667 22.8627 14.1967 23.8327 13 23.8327C11.8034 23.8327 10.8333 22.8627 10.8333 21.666M15.1667 21.666H22.75M10.8333 21.666H3.25"
  //         stroke="black"
  //         strokeWidth="1.18182"
  //         strokeLinecap="round"
  //         strokeLinejoin="round"
  //       />
  //     </svg>
  //   ),
  //   url: "/servers",
  //   rightTitle: "launch_with_confidence",
  //   description: "contact_for_quote",
  //   items: [
  //     {
  //       title: "items.15.title",
  //     },
  //     {
  //       title: "items.16.title",
  //     },
  //     {
  //       title: "items.17.title",
  //     },
  //   ],
  // },

  {
    id: 8,
    title: "services.ai-services",
    url: "/ai-services",
    // icon: "/images/ai/Chatbot.gif",
    icon: "/images/ai/Chatbot2.svg",
    isImg: true,
    rightTitle: "launch_with_confidence",
    description: "contact_for_quote",
    items: [
      {
        title: "items.21.title",
      },
      {
        title: "items.22.title",
      },
      {
        title: "items.23.title",
      },
    ],
  },
  {
    id: 9,
    title: "services.domains",
    url: "/domains",
    icon: (
      <svg
        width="26"
        height="26"
        viewBox="0 0 26 26"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M13 25.5C19.9036 25.5 25.5 19.9036 25.5 13C25.5 6.09644 19.9036 0.5 13 0.5C6.09644 0.5 0.5 6.09644 0.5 13C0.5 19.9036 6.09644 25.5 13 25.5Z"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M0.5 13H25.5"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13 0.5C16.1826 4.08433 17.9302 8.45334 18 13C17.9302 17.5467 16.1826 21.9157 13 25.5C9.81738 21.9157 8.06981 17.5467 8 13C8.06981 8.45334 9.81738 4.08433 13 0.5Z"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    rightTitle: "launch_with_confidence",
    description: "contact_for_quote",
    items: [
      {
        title: "items.24.title",
        pricing: "items.24.pricing",
      },
      {
        title: "items.25.title",
      },
      {
        title: "items.26.title",
      },
    ],
  },
];

const NAV_ITEMS = [
  { id: 1, title: "home", url: "/" },
  { id: 2, title: "services.web_creation", url: "/web-development" },
  { id: 3, title: "services.hosting", url: "/hosting" },
  { id: 4, title: "services.ssl", url: "/ssl" },
  { id: 5, title: "services.cloud_morocco", url: "/cloud-maroc" },
  { id: 6, title: "services.cloud_security", url: "/cloud-security" },
  { id: 7, title: "services.managed_services", url: "/managed-services" },
  { id: 8, title: "services.ai-services", url: "/ai-services" },
  { id: 9, title: "services.domains", url: "/domains" },
  // { id: 10, title: "guide", url: "/guide" },
  { id: 10, title: "blog", url: "/blog" },
  { id: 11, title: "about_us", url: "/about-us" },
  { id: 12, title: "contact", url: "#contact-nous" },
];

const LAST_SECTION_COUNT = 3;

const SOCIAL_LINKS = [
  {
    href: "https://web.facebook.com/profile.php?id=61551999353576&_rdc=1&_rdr",
    icon: FaFacebook,
    label: "facebook",
  },
  {
    href: "https://www.instagram.com/ztechengineering",
    icon: FaInstagram,
    label: "instagram",
  },
  {
    href: "https://www.linkedin.com/company/ztechengineering",
    icon: FaLinkedin,
    label: "linkedin",
  },
  {
    href: "https://www.tiktok.com/@ztechengineering?is_from_webapp=1&sender_device=pc",
    icon: FaTiktok,
    label: "tiktok",
  },
  {
    href: "https://www.youtube.com/@ztechengineering/",
    icon: FaYoutube,
    label: "youtube",
  },
];

// Components
const NavigationItem = ({ t, item, isActive, onClick }) => (
  <div className="border-b">
    <button
      id={`item-${item.id}`}
      name={item.title}
      className={`w-full p-2 rounded-md flex justify-between uppercase font-semibold text-sm text-left py-2 ${
        isActive ? "bg-secondary text-white" : "text-primary"
      }`}
      onClick={() => onClick(item.url, item.id)}
    >
      {t(item.title)}
    </button>
  </div>
);

const GroupedSection = ({ t, items, normalizedPath, handleItemClick }) => (
  <div className="mt-4 bg-secondary rounded-xl border text-sm p-4 pb-0">
    {items.map((item) => (
      <button
        key={item.id}
        id={`item-${item.id}`}
        name={item.title}
        className={`block w-full uppercase font-semibold text-sm text-left py-2 px-4 rounded-md ${
          normalizedPath === item.url
            ? "bg-white text-secondary"
            : "text-white bg-transparent"
        }`}
        onClick={() => handleItemClick(item.url, item.id)}
      >
        {t(item.title)}
      </button>
    ))}
    <div className="flex justify-center items-center gap-x-4 mx-auto py-4 border-t">
      {SOCIAL_LINKS.map(({ href, icon: Icon, label }) => (
        <Link key={label} href={href} aria-label={label} className="text-white">
          <Icon className="w-[22px] h-auto" />
        </Link>
      ))}
    </div>
  </div>
);

export default function MainNavbar({ t }) {
  const [dropdownPosition, setDropdownPosition] = useState(null);
  const [hoveredNavItem, setHoveredNavItem] = useState(null);
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();
  const path = usePathname();
  const normalizedPath = path.replace(/^\/[a-z]{2}/, "") || "/";

  const ref = useRef(null);

  const toggleNavMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleMouseEnter = (item, element) => {
    const rect = element.getBoundingClientRect();
    setDropdownPosition({
      top: rect.bottom,
      left: rect.left,
      width: rect.width,
    });
    setHoveredNavItem(item);
  };

  const handleItemClick = (url, id) => {
    setIsOpen(false);

    // Handle anchor links differently
    if (url.startsWith("#")) {
      const element = document.querySelector(url);
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
    } else {
      // For regular page navigation
      router.push(url);
    }
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isOpen && !event.target.closest(".mobile-menu-container")) {
        setTimeout(() => setIsOpen(false), 100);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isOpen]);

  return (
    <div className="flex lg:h-[50px] border-none items-center text-black justify-between w-full">
      {/* Desktop Navigation */}
      <div
        className={`hidden relative lg:flex mx-auto text-gray-800 h-full items-center font-medium xl:px-4 w-full py-0 2xl:space-x-6 lg:space-x-4 space-x-2 xl:text-sm text-xs capitalize jusbe`}
        onMouseLeave={() => setHoveredNavItem(null)}
      >
        <div
          key={navItems[0].id}
          onClick={() => handleItemClick(navItems[0].url, navItems[0].id)}
          ref={ref}
          onMouseEnter={() => {
            if (ref.current) {
              handleMouseEnter(navItems[0], ref.current);
            }
          }}
          className={`2xl:block hidden border-b-2 py-1 hover:border-b-2 hover:border-secondary border-transparent`}
        >
          <Link
            href={navItems[0].url}
            aria-label={"Go to " + t(navItems[0].title)}
            className={`relative z-10 cursor-pointer hover:text-[#5d87fb] ${
              normalizedPath === navItems[0].url ? "text-secondary " : ""
            }`}
          >
            {t(navItems[0].title)}
          </Link>
        </div>

        {navItems.slice(1).map((item, index) => (
          <div
            key={item.id}
            onClick={() => handleItemClick(item.url, item.id)}
            ref={ref}
            onMouseEnter={() => {
              if (ref.current) {
                handleMouseEnter(item, ref.current);
              }
            }}
            className={`border-b-2 py-2 hover:border-b-2 hover:border-secondary border-transparent`}
          >
            <Link
              href={item.url}
              title={t(item.title)}
              aria-label={"Go to " + t(item.title)}
              className={`relative z-10 cursor-pointer hover:text-[#5d87fb] ${
                normalizedPath === item.url ? "text-secondary " : ""
              }`}
            >
              {t(item.title)}
            </Link>
          </div>
        ))}

        {/* Dropdown */}
        {hoveredNavItem &&
          dropdownPosition &&
          ![0].includes(hoveredNavItem?.id) && (
            <NavDropDown
              content={hoveredNavItem}
              position={dropdownPosition}
              t={t}
            />
          )}
      </div>
      <div className="lg:flex justify-center items-center hidden h-full max-w-[140px] min-w-[110px]">
        <AvatarWithUserDropdown />
      </div>

      {/* Mobile Navigation */}
      <div className="relative flex lg:hidden justify-between items-center lg:justify-end w-full h-full bg-white my-2 rounded-xl px-0 rounded-b-xl">
        <div className="flex p-2 h-fit my-auto">
          <Button
            className="p-2"
            size="sm"
            variant="outlined"
            color="black"
            name="Menu-btn"
            onClick={() => toggleNavMenu()}
          >
            <svg
              width="22"
              height="16"
              viewBox="0 0 22 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1.84741 8.00004H20.4067M1.84741 1.8136H20.4067M1.84741 14.1865H20.4067"
                stroke="black"
                strokeWidth="2.06215"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </Button>
        </div>
        <div className="lg:hidden flex px-2 w-[180px] py-0 lg:p-1 my-auto">
          <SiteBranding />
        </div>
        {isOpen && (
          <div className="absolute top-14 right-0 w-full bg-white border-t-2 border-gray-400 h-[66.666vh] overflow-y-auto rounded-b-xl shadow-[0_70px_70px_-15px_rgb(1,0,0,0.2)] z-50">
            <div className="flex flex-col p-4 space-y-2">
              {/* Navigation Items */}
              {NAV_ITEMS.map((item, index, array) => {
                const isLastSection =
                  index >= array.length - LAST_SECTION_COUNT;

                // Main navigation items
                if (!isLastSection) {
                  return (
                    <NavigationItem
                      t={t}
                      key={item.id}
                      item={item}
                      isActive={normalizedPath === item.url}
                      onClick={handleItemClick}
                    />
                  );
                }

                // Last section (grouped items)
                if (index === array.length - LAST_SECTION_COUNT) {
                  return (
                    <GroupedSection
                      t={t}
                      key="grouped-section"
                      items={array.slice(-LAST_SECTION_COUNT)}
                      normalizedPath={normalizedPath}
                      handleItemClick={handleItemClick}
                    />
                  );
                }
                return null;
              })}
            </div>
          </div>
        )}
        <AvatarWithUserDropdown />
      </div>
    </div>
  );
}
