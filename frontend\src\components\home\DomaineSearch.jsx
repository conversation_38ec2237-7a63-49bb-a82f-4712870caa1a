import React, { useState, useEffect, useRef } from "react";
import { Search, ShoppingCart } from "lucide-react";
import { useTranslations } from "next-intl";
import DomainExtensionDropdown from "./DomainExtensionDropdown";

const DomainSearch = ({ t }) => {
  const [domain, setDomain] = useState("");
  const [extension, setExtension] = useState(".ma");
  const [results, setResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const searchContainerRef = useRef(null);

  // Dummy domain data
  const dummyDomains = [
    { domain: "example", extension: ".ma", available: true, price: "99.00" },
    { domain: "example", extension: ".com", available: false, price: "12.99" },
    { domain: "test", extension: ".ma", available: true, price: "85.00" },
    { domain: "ztech", extension: ".net", available: true, price: "15.99" },
    { domain: "business", extension: ".org", available: false, price: "25.00" },
  ];

  const handleSearch = () => {
    if (domain.trim() === "") {
      alert(t("domain_search.empty_domain_alert"));
      return;
    }

    setIsSearching(true);
    setTimeout(() => {
      const filtered = dummyDomains.filter(
        (item) =>
          item.domain.includes(domain.toLowerCase()) ||
          item.extension.includes(extension)
      );
      setResults(filtered);
      setIsSearching(false);
    }, 800);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target)
      ) {
        if (!isSearching) {
          setResults([]);
        }
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [searchContainerRef, isSearching]);

  return (
    <div
      ref={searchContainerRef}
      className="w-[95%] md:w-full max-w-4xl mx-auto md:absolute md:-bottom-12 md:left-1/2 z-40 md:transform md:-translate-x-1/2 md:mt-0 mt-4"
    >
      {/* Main Search Container */}
      <div className="relative z-40">
        {/* Search Bar */}
        <div
          className={`flex items-center bg-white rounded-xl shadow-lg p-2 md:p-3 border transition-all duration-300 ${
            isFocused
              ? "border-indigo-500 shadow-xl"
              : "border-gray-200 hover:border-gray-300"
          }`}
        >
          {/* Mobile View */}
          <div className="md:hidden flex items-center w-full h-12">
            <span className="px-2 text-[#606AF5]">
              <Search className="h-5 w-5" />
            </span>
            <input
              type="text"
              placeholder={t("domain_search.placeholder")}
              value={domain}
              onChange={(e) => setDomain(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              className="flex-1 border-none focus:ring-0 pr-2 text-gray-800 text-sm bg-transparent outline-none h-full placeholder-gray-400"
            />
            <button
              onClick={handleSearch}
              className="bg-[#606AF5] hover:bg-indigo-700 text-white font-medium rounded-lg px-4 py-2 text-sm transition-all duration-200 h-full flex items-center"
            >
              <Search className="h-4 w-4" />
            </button>
          </div>

          {/* Desktop View */}
          <div className="hidden md:flex items-center w-full h-14">
            <span className="px-4 text-[#606AF5]">
              <Search className="h-5 w-5" />
            </span>

            <input
              type="text"
              placeholder={t("domain_search.placeholder")}
              value={domain}
              onChange={(e) => setDomain(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              className="flex-1 border-none focus:ring-0 pr-0 text-gray-800 text-lg bg-transparent outline-none placeholder-gray-400"
            />

            <div className="h-10 w-px bg-gray-300 mx-3"></div>

            <DomainExtensionDropdown
              extension={extension}
              setExtension={setExtension}
            />

            <button
              onClick={handleSearch}
              className="ml-4 bg-[#606AF5] hover:from-indigo-700 hover:to-[#606AF5] text-white font-medium rounded-lg px-6 py-3 text-base transition-all duration-200 h-full flex items-center shadow-md hover:shadow-lg"
            >
              {t("domain_search.search_button")}
            </button>
          </div>
        </div>

        {/* Search Results Dropdown */}
        {(isSearching || results.length > 0) && (
          <div className="absolute w-full mt-2 bg-white rounded-xl shadow-xl border border-gray-200 overflow-hidden z-50">
            {isSearching ? (
              <div className="p-4 flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#606AF5] mr-3"></div>
                <span className="text-gray-600">Searching domains...</span>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {results.map((result, index) => (
                  <div
                    key={index}
                    className="p-4 hover:bg-gray-50 transition-colors duration-150"
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <span className="font-medium text-gray-900">
                          {result.domain}
                          <span className="text-[#606AF5]">
                            {result.extension}
                          </span>
                        </span>
                        <span
                          className={`ml-3 px-2.5 py-1 text-xs rounded-full font-medium ${
                            result.available
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {result.available
                            ? t("domain_search.available")
                            : t("domain_search.taken")}
                        </span>
                      </div>
                      {result.available && (
                        <div className="flex items-center gap-3">
                          <span className="text-[#606AF5] font-semibold">
                            ${result.price}
                          </span>
                          <button
                            className="text-[#606AF5] hover:text-indigo-800 p-2 rounded-full hover:bg-indigo-50 transition-colors"
                            aria-label={t("domain_search.add_to_cart")}
                          >
                            <ShoppingCart className="h-5 w-5" />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default DomainSearch;
