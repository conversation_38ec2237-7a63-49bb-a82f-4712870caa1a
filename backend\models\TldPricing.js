// ... existing code ...
const mongoose = require("mongoose");

const TldPricingSchema = new mongoose.Schema({
  tld: { type: String, required: true, unique: true },
  tldKey: { type: String, required: true }, // <-- Add this line
  register: { type: Number, required: true },
  renewal: { type: Number, required: true },
  transfer: { type: Number, required: false },
  period: { type: Number, default: 1 },
  currency: { type: String, default: "MAD" },
  raw: { type: mongoose.Schema.Types.Mixed }, // Store the full raw response for reference
}, { timestamps: true });

module.exports = mongoose.model("TldPricing", TldPricingSchema);
// ... existing code ...