import axios from "axios";
import { BACKEND_URL } from "../config/constant";

const axiosInstance = axios.create({
  baseURL: BACKEND_URL,
  timeout: 10000,
  withCredentials: true,
});

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      console.log("from axios instance, try to redirect user to login");
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
