/**
 * Test script to verify the contact API fixes
 * This script tests the corrected API endpoints and country validation
 */

require('dotenv').config();
const contactService = require('../services/contactService');

// Test contact data with various country formats
const TEST_CONTACTS = [
  {
    name: 'Test Contact 1',
    email: '<EMAIL>',
    company: 'Test Company',
    address: '123 Test Street',
    city: 'Test City',
    country: 'Morocco', // Should map to MA
    zipcode: '12345',
    phoneCountryCode: '212',
    phone: '*********'
  },
  {
    name: 'Test Contact 2',
    email: '<EMAIL>',
    company: 'Test Company',
    address: '456 Test Avenue',
    city: 'Test City',
    country: 'US', // Already valid ISO code
    zipcode: '90210',
    phoneCountryCode: '1',
    phone: '5551234567'
  },
  {
    name: 'Test Contact 3',
    email: '<EMAIL>',
    company: 'Test Company',
    address: '789 Test Boulevard',
    city: 'Test City',
    country: 'France', // Should map to FR
    zipcode: '75001',
    phoneCountryCode: '33',
    phone: '*********'
  },
  {
    name: 'Test Contact 4',
    email: '<EMAIL>',
    company: 'Test Company',
    address: '321 Test Road',
    city: 'Test City',
    country: 'InvalidCountry', // Should fail validation
    zipcode: '12345',
    phoneCountryCode: '1',
    phone: '5551234567'
  }
];

async function testCountryCodeMapping() {
  console.log('🧪 Testing country code mapping...\n');
  
  // Import the getCountryCode function from the controller
  const getCountryCode = (countryInput) => {
    if (!countryInput) return "";

    // If already a 2-letter code, return as is
    if (countryInput.length === 2 && /^[A-Z]{2}$/i.test(countryInput)) {
      return countryInput.toUpperCase();
    }

    // Common country name to ISO code mappings
    const countryMappings = {
      morocco: "MA",
      maroc: "MA",
      france: "FR",
      "united states": "US",
      usa: "US",
      canada: "CA",
      "united kingdom": "GB",
      uk: "GB",
      spain: "ES",
      germany: "DE",
      italy: "IT",
      portugal: "PT",
      algeria: "DZ",
      tunisia: "TN",
      egypt: "EG",
      "saudi arabia": "SA",
      uae: "AE",
      "united arab emirates": "AE",
      netherlands: "NL",
      belgium: "BE",
      switzerland: "CH",
      austria: "AT",
      sweden: "SE",
      norway: "NO",
      denmark: "DK",
      finland: "FI",
      poland: "PL",
      "czech republic": "CZ",
      hungary: "HU",
      romania: "RO",
      bulgaria: "BG",
      greece: "GR",
      turkey: "TR",
      russia: "RU",
      ukraine: "UA",
      india: "IN",
      china: "CN",
      japan: "JP",
      "south korea": "KR",
      australia: "AU",
      "new zealand": "NZ",
      brazil: "BR",
      argentina: "AR",
      mexico: "MX",
      chile: "CL",
      colombia: "CO",
      peru: "PE",
      venezuela: "VE",
      "south africa": "ZA",
      nigeria: "NG",
      kenya: "KE",
      ghana: "GH",
      israel: "IL",
      lebanon: "LB",
      jordan: "JO",
      kuwait: "KW",
      qatar: "QA",
      bahrain: "BH",
      oman: "OM",
    };

    const normalizedInput = countryInput.toLowerCase().trim();
    const mappedCountry = countryMappings[normalizedInput];
    
    if (mappedCountry) {
      return mappedCountry;
    }
    
    // If no mapping found and input is not a valid 2-letter code, return empty string
    console.warn(`Invalid country input: "${countryInput}". Please use a valid country name or ISO code.`);
    return "";
  };

  const testCountries = ['Morocco', 'US', 'France', 'InvalidCountry', 'maroc', 'usa'];
  
  testCountries.forEach(country => {
    const result = getCountryCode(country);
    console.log(`"${country}" -> "${result}" ${result ? '✅' : '❌'}`);
  });
}

async function testContactCreation() {
  console.log('\n🧪 Testing contact creation with fixed API endpoints...\n');
  
  const customerId = process.env.COMPANY_CUSTOMER_ID;
  
  if (!customerId) {
    console.error('❌ COMPANY_CUSTOMER_ID not configured');
    return;
  }
  
  console.log(`Using customer ID: ${customerId}`);
  
  for (let i = 0; i < TEST_CONTACTS.length; i++) {
    const contact = TEST_CONTACTS[i];
    console.log(`\n📝 Testing contact ${i + 1}: ${contact.name}`);
    console.log(`Country: "${contact.country}"`);
    
    try {
      const result = await contactService.addContact(contact, customerId);
      
      // Extract contact ID
      const contactId = result.contactId || result['contact-id'] || result.id || result;
      
      if (contactId && typeof contactId === 'string' && !contactId.startsWith('fallback-')) {
        console.log(`✅ Contact created successfully with ID: ${contactId}`);
      } else {
        console.log(`❌ Invalid contact ID received: ${contactId}`);
      }
      
    } catch (error) {
      console.log(`❌ Contact creation failed: ${error.message}`);
      
      // Check if it's a country validation error
      if (error.message.includes('country') || error.message.includes('Country')) {
        console.log(`   This is expected for invalid country: "${contact.country}"`);
      }
    }
  }
}

async function testContactValidation() {
  console.log('\n🧪 Testing contact validation...\n');
  
  const customerId = process.env.COMPANY_CUSTOMER_ID;
  
  // Test with missing required fields
  const invalidContacts = [
    {
      // Missing name
      email: '<EMAIL>',
      country: 'US'
    },
    {
      name: 'Test User',
      // Missing email
      country: 'US'
    },
    {
      name: 'Test User',
      email: '<EMAIL>',
      // Missing country
    },
    {
      name: 'Test User',
      email: '<EMAIL>',
      country: 'X' // Invalid country (too short)
    }
  ];
  
  for (let i = 0; i < invalidContacts.length; i++) {
    const contact = invalidContacts[i];
    console.log(`\n📝 Testing invalid contact ${i + 1}:`);
    console.log(`Missing: ${!contact.name ? 'name ' : ''}${!contact.email ? 'email ' : ''}${!contact.country ? 'country' : contact.country.length < 2 ? 'valid country' : ''}`);
    
    try {
      await contactService.addContact(contact, customerId);
      console.log(`❌ Validation should have failed but didn't`);
    } catch (error) {
      console.log(`✅ Validation correctly failed: ${error.message}`);
    }
  }
}

async function runTests() {
  try {
    console.log('🚀 Starting contact API fix tests...\n');
    
    // Test country code mapping
    await testCountryCodeMapping();
    
    // Test contact creation with fixed endpoints
    await testContactCreation();
    
    // Test contact validation
    await testContactValidation();
    
    console.log('\n🎉 All tests completed!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testCountryCodeMapping,
  testContactCreation,
  testContactValidation
};
