"use client";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import CloudSecurityIntro from "@/components/cloud-security/cloudSecurityIntro";
import CloudSecurityMain from "@/components/cloud-security/cloudSecurityMain";
import ContactForm2 from "@/components/shared/contactForm2";
import { Alert } from "@material-tailwind/react";

// Animation variants
const animations = {
  fadeInLeft: {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  },
  fadeInRight: {
    hidden: { opacity: 0, x: 50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  },
  fadeInUp: {
    hidden: { 
      opacity: 0, 
      y: 50,
      scale: 0.95 
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { 
        duration: 0.8, 
        ease: [0.4, 0, 0.2, 1],
        delay: 0.4 
      },
    },
  },
};

function CloudSecurity() {
  const t = useTranslations("cloud-security");
  

  const [data, setData] = useState({
    page: "cloud security",
    offerName: "No offer selected",
    id: 0,
    url: "/cloud-security",
  });

  return (
    <div className="flex flex-col gap-y-5 h-full w-full relative">
      
      <CloudSecurityIntro t={t} animations={animations} />
      <CloudSecurityMain t={t} animations={animations} />
      <ContactForm2 data={data} setData={setData} />
    </div>
  );
}

export default CloudSecurity;
