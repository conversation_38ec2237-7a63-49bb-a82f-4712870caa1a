"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@next";
exports.ids = ["vendor-chunks/@next"];
exports.modules = {

/***/ "(ssr)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js":
/*!************************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = ThirdPartyScriptEmbed;\nconst jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\nconst react_1 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nfunction ThirdPartyScriptEmbed({ html, height = null, width = null, children, dataNtpc = \"\" }) {\n    (0, react_1.useEffect)(()=>{\n        if (dataNtpc) {\n            // performance.mark is being used as a feature use signal. While it is traditionally used for performance\n            // benchmarking it is low overhead and thus considered safe to use in production and it is a widely available\n            // existing API.\n            performance.mark(\"mark_feature_usage\", {\n                detail: {\n                    feature: `next-third-parties-${dataNtpc}`\n                }\n            });\n        }\n    }, [\n        dataNtpc\n    ]);\n    return (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {\n        children: [\n            children,\n            html ? (0, jsx_runtime_1.jsx)(\"div\", {\n                style: {\n                    height: height != null ? `${height}px` : \"auto\",\n                    width: width != null ? `${width}px` : \"auto\"\n                },\n                \"data-ntpc\": dataNtpc,\n                dangerouslySetInnerHTML: {\n                    __html: html\n                }\n            }) : null\n        ]\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@next/third-parties/dist/google/ga.js":
/*!************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/ga.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GoogleAnalytics = GoogleAnalytics;\nexports.sendGAEvent = sendGAEvent;\nconst jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// TODO: Evaluate import 'client only'\nconst react_1 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nconst script_1 = __importDefault(__webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\"));\nlet currDataLayerName = undefined;\nfunction GoogleAnalytics(props) {\n    const { gaId, debugMode, dataLayerName = \"dataLayer\", nonce } = props;\n    if (currDataLayerName === undefined) {\n        currDataLayerName = dataLayerName;\n    }\n    (0, react_1.useEffect)(()=>{\n        // performance.mark is being used as a feature use signal. While it is traditionally used for performance\n        // benchmarking it is low overhead and thus considered safe to use in production and it is a widely available\n        // existing API.\n        // The performance measurement will be handled by Chrome Aurora\n        performance.mark(\"mark_feature_usage\", {\n            detail: {\n                feature: \"next-third-parties-ga\"\n            }\n        });\n    }, []);\n    return (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {\n        children: [\n            (0, jsx_runtime_1.jsx)(script_1.default, {\n                id: \"_next-ga-init\",\n                dangerouslySetInnerHTML: {\n                    __html: `\n          window['${dataLayerName}'] = window['${dataLayerName}'] || [];\n          function gtag(){window['${dataLayerName}'].push(arguments);}\n          gtag('js', new Date());\n\n          gtag('config', '${gaId}' ${debugMode ? \",{ 'debug_mode': true }\" : \"\"});`\n                },\n                nonce: nonce\n            }),\n            (0, jsx_runtime_1.jsx)(script_1.default, {\n                id: \"_next-ga\",\n                src: `https://www.googletagmanager.com/gtag/js?id=${gaId}`,\n                nonce: nonce\n            })\n        ]\n    });\n}\nfunction sendGAEvent(..._args) {\n    if (currDataLayerName === undefined) {\n        console.warn(`@next/third-parties: GA has not been initialized`);\n        return;\n    }\n    if (window[currDataLayerName]) {\n        window[currDataLayerName].push(arguments);\n    } else {\n        console.warn(`@next/third-parties: GA dataLayer ${currDataLayerName} does not exist`);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@next/third-parties/dist/google/ga.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@next/third-parties/dist/google/gtm.js":
/*!*************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/gtm.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.sendGTMEvent = void 0;\nexports.GoogleTagManager = GoogleTagManager;\nconst jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// TODO: Evaluate import 'client only'\nconst react_1 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nconst script_1 = __importDefault(__webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\"));\nlet currDataLayerName = \"dataLayer\";\nfunction GoogleTagManager(props) {\n    const { gtmId, gtmScriptUrl = \"https://www.googletagmanager.com/gtm.js\", dataLayerName = \"dataLayer\", auth, preview, dataLayer, nonce } = props;\n    currDataLayerName = dataLayerName;\n    const gtmLayer = dataLayerName !== \"dataLayer\" ? `&l=${dataLayerName}` : \"\";\n    const gtmAuth = auth ? `&gtm_auth=${auth}` : \"\";\n    const gtmPreview = preview ? `&gtm_preview=${preview}&gtm_cookies_win=x` : \"\";\n    (0, react_1.useEffect)(()=>{\n        // performance.mark is being used as a feature use signal. While it is traditionally used for performance\n        // benchmarking it is low overhead and thus considered safe to use in production and it is a widely available\n        // existing API.\n        // The performance measurement will be handled by Chrome Aurora\n        performance.mark(\"mark_feature_usage\", {\n            detail: {\n                feature: \"next-third-parties-gtm\"\n            }\n        });\n    }, []);\n    return (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {\n        children: [\n            (0, jsx_runtime_1.jsx)(script_1.default, {\n                id: \"_next-gtm-init\",\n                dangerouslySetInnerHTML: {\n                    __html: `\n      (function(w,l){\n        w[l]=w[l]||[];\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});\n        ${dataLayer ? `w[l].push(${JSON.stringify(dataLayer)})` : \"\"}\n      })(window,'${dataLayerName}');`\n                },\n                nonce: nonce\n            }),\n            (0, jsx_runtime_1.jsx)(script_1.default, {\n                id: \"_next-gtm\",\n                \"data-ntpc\": \"GTM\",\n                src: `${gtmScriptUrl}?id=${gtmId}${gtmLayer}${gtmAuth}${gtmPreview}`,\n                nonce: nonce\n            })\n        ]\n    });\n}\nconst sendGTMEvent = (data, dataLayerName)=>{\n    // special case if we are sending events before GTM init and we have custom dataLayerName\n    const dataLayer = dataLayerName || currDataLayerName;\n    // define dataLayer so we can still queue up events before GTM init\n    window[dataLayer] = window[dataLayer] || [];\n    window[dataLayer].push(data);\n};\nexports.sendGTMEvent = sendGTMEvent;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@next/third-parties/dist/google/gtm.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js":
/*!************************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

/* __next_internal_client_entry_do_not_use__  cjs */ 
const { createProxy } = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");
module.exports = createProxy("C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\node_modules\\@next\\third-parties\\dist\\ThirdPartyScriptEmbed.js");


/***/ }),

/***/ "(rsc)/./node_modules/@next/third-parties/dist/google/ga.js":
/*!************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/ga.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

/* __next_internal_client_entry_do_not_use__  cjs */ 
const { createProxy } = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");
module.exports = createProxy("C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\node_modules\\@next\\third-parties\\dist\\google\\ga.js");


/***/ }),

/***/ "(rsc)/./node_modules/@next/third-parties/dist/google/google-maps-embed.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/google-maps-embed.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = GoogleMapsEmbed;\nconst jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nconst third_party_capital_1 = __webpack_require__(/*! third-party-capital */ \"(rsc)/./node_modules/third-party-capital/lib/cjs/index.js\");\nconst ThirdPartyScriptEmbed_1 = __importDefault(__webpack_require__(/*! ../ThirdPartyScriptEmbed */ \"(rsc)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js\"));\nfunction GoogleMapsEmbed(props) {\n    const { apiKey, ...restProps } = props;\n    const formattedProps = {\n        ...restProps,\n        key: apiKey\n    };\n    const { html } = (0, third_party_capital_1.GoogleMapsEmbed)(formattedProps);\n    return (0, jsx_runtime_1.jsx)(ThirdPartyScriptEmbed_1.default, {\n        height: formattedProps.height || null,\n        width: formattedProps.width || null,\n        html: html,\n        dataNtpc: \"GoogleMapsEmbed\"\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@next/third-parties/dist/google/google-maps-embed.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@next/third-parties/dist/google/gtm.js":
/*!*************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/gtm.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

/* __next_internal_client_entry_do_not_use__  cjs */ 
const { createProxy } = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");
module.exports = createProxy("C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\node_modules\\@next\\third-parties\\dist\\google\\gtm.js");


/***/ }),

/***/ "(rsc)/./node_modules/@next/third-parties/dist/google/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.sendGAEvent = exports.GoogleAnalytics = exports.sendGTMEvent = exports.GoogleTagManager = exports.YouTubeEmbed = exports.GoogleMapsEmbed = void 0;\nvar google_maps_embed_1 = __webpack_require__(/*! ./google-maps-embed */ \"(rsc)/./node_modules/@next/third-parties/dist/google/google-maps-embed.js\");\nObject.defineProperty(exports, \"GoogleMapsEmbed\", ({\n    enumerable: true,\n    get: function() {\n        return __importDefault(google_maps_embed_1).default;\n    }\n}));\nvar youtube_embed_1 = __webpack_require__(/*! ./youtube-embed */ \"(rsc)/./node_modules/@next/third-parties/dist/google/youtube-embed.js\");\nObject.defineProperty(exports, \"YouTubeEmbed\", ({\n    enumerable: true,\n    get: function() {\n        return __importDefault(youtube_embed_1).default;\n    }\n}));\nvar gtm_1 = __webpack_require__(/*! ./gtm */ \"(rsc)/./node_modules/@next/third-parties/dist/google/gtm.js\");\nObject.defineProperty(exports, \"GoogleTagManager\", ({\n    enumerable: true,\n    get: function() {\n        return gtm_1.GoogleTagManager;\n    }\n}));\nObject.defineProperty(exports, \"sendGTMEvent\", ({\n    enumerable: true,\n    get: function() {\n        return gtm_1.sendGTMEvent;\n    }\n}));\nvar ga_1 = __webpack_require__(/*! ./ga */ \"(rsc)/./node_modules/@next/third-parties/dist/google/ga.js\");\nObject.defineProperty(exports, \"GoogleAnalytics\", ({\n    enumerable: true,\n    get: function() {\n        return ga_1.GoogleAnalytics;\n    }\n}));\nObject.defineProperty(exports, \"sendGAEvent\", ({\n    enumerable: true,\n    get: function() {\n        return ga_1.sendGAEvent;\n    }\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@next/third-parties/dist/google/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@next/third-parties/dist/google/youtube-embed.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/youtube-embed.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = YouTubeEmbed;\nconst jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nconst script_1 = __importDefault(__webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\"));\nconst third_party_capital_1 = __webpack_require__(/*! third-party-capital */ \"(rsc)/./node_modules/third-party-capital/lib/cjs/index.js\");\nconst ThirdPartyScriptEmbed_1 = __importDefault(__webpack_require__(/*! ../ThirdPartyScriptEmbed */ \"(rsc)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js\"));\nconst scriptStrategy = {\n    server: \"beforeInteractive\",\n    client: \"afterInteractive\",\n    idle: \"lazyOnload\",\n    worker: \"worker\"\n};\nfunction YouTubeEmbed(props) {\n    const { html, scripts, stylesheets } = (0, third_party_capital_1.YouTubeEmbed)(props);\n    return (0, jsx_runtime_1.jsx)(ThirdPartyScriptEmbed_1.default, {\n        height: props.height || null,\n        width: props.width || null,\n        html: html,\n        dataNtpc: \"YouTubeEmbed\",\n        children: scripts === null || scripts === void 0 ? void 0 : scripts.map((script)=>(0, jsx_runtime_1.jsx)(script_1.default, {\n                src: script.url,\n                strategy: scriptStrategy[script.strategy],\n                stylesheets: stylesheets\n            }, script.url))\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@next/third-parties/dist/google/youtube-embed.js\n");

/***/ })

};
;