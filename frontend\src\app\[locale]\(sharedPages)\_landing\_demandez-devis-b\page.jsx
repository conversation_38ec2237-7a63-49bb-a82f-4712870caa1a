"use client";
import React, { useState } from "react";
import {
  LIGHTNINGsvg,
  LOCKsvg,
  MALEPERSONsvg,
  PERSONWITHSTARSsvg,
  ZTECHLOGOsvg,
} from "@/icons/svgIcons";
import { useTranslations } from "next-intl";
import PopularOffers from "@/components/landing/landing-a/PopularOffers";
import DiscountOffer from "@/components/landing/landing-b/discountOffer";
import Banner from "@/components/landing/landing-b/banner";
import ServicesPense from "@/components/landing/landing-b/servicesPense";
import ContactForm2 from "@/components/shared/contactForm2";
import Testimonials from "@/components/home/<USER>";

const services = [
  {
    id: 1,
    icon: <PERSONWITHSTARSsvg />,
    title: "services.0.title",
    description: "services.0.description",
  },
  {
    id: 2,
    icon: <ZTECHLOGOsvg size={40} />,
    title: "services.1.title",
    description: "services.1.description",
  },
  {
    id: 3,
    icon: <LOCKsvg width={26} />,
    title: "services.2.title",
    description: "services.2.description",
  },
];

const NosEngagement = [
  {
    id: 1,
    icon: <PERSONWITHSTARSsvg />,
    title: "NosEngagement.0.title",
    description: "NosEngagement.0.description",
  },
  {
    id: 2,
    icon: <LIGHTNINGsvg />,
    title: "NosEngagement.1.title",
    description: "NosEngagement.1.description",
  },
  {
    id: 3,
    icon: <MALEPERSONsvg width={26} />,
    title: "NosEngagement.2.title",
    description: "NosEngagement.2.description",
  },
];

const sectionData = {
  title: "sectionData.title",
  focusWord: "sectionData.focusWord",
  sideImg: "/images/landing/services-penses-pour.webp",
  bgColor: "bg-[#F5F6F8]",
  flexPositioning: "md:flex-row",
};

const sectionData2 = {
  title: "sectionData2.title",
  focusWord: "sectionData2.focusWord",
  sideImg: "/images/landing/landing-b-side-img.webp",
  bgColor: "bg-white",
  flexPositioning: "md:flex-row-reverse",
};
function LandingPageB() {
  const t = useTranslations("landing");

  const [data, setData] = useState({
    page: "Landing page B",
    offerName: "No offer selected",
    id: 0,
    url: "/landing/demandez-devis-b",
  });

  return (
    <div className="w-full min-h-screen p-0 m-0 font-inter mb-0 bg-white">
      <PopularOffers t={t} />
      <DiscountOffer setData={setData} t={t} />
      <Banner t={t} />
      <ServicesPense services={services} sectionData={sectionData} t={t} />
      <ServicesPense
        services={NosEngagement}
        sectionData={sectionData2}
        t={t}
      />
      <ContactForm2 data={data} setData={setData} t={t} />
      <Testimonials t={t} />
    </div>
  );
}

export default LandingPageB;
