import React from "react";
import {
  Code,
  Globe,
  ServerIcon,
  ShieldIcon,
  ShoppingCart,
} from "lucide-react";
import {
  TrashIcon,
  ChevronUpIcon,
  ChevronDownIcon,
} from "@heroicons/react/24/solid";
import {
  CART_ITEM_TYPE,
  CATEGORY_DOMAINS,
  CATEGORY_HOSTING,
  CATEGORY_SSL,
  CATEGORY_WEB_CREATION,
} from "@/app/config/Categories";

// Mapping of category names to their corresponding icon components.
const categoryIcons = {
  [CATEGORY_HOSTING]: ServerIcon,
  [CATEGORY_SSL]: ShieldIcon,
  [CATEGORY_WEB_CREATION]: Code,
  [CATEGORY_DOMAINS]: Globe,
};

// A small wrapper for the icon container.
const IconWrapper = ({ children }) => (
  <div className="h-12 w-12 flex-shrink-0 bg-blue-100 rounded-lg flex items-center justify-center">
    {children}
  </div>
);

const CartItemDisplay = ({ item, imgError, handleQuantityChange, t }) => {
  // Domain item (type: "domain")

  const DomainNameIcon = categoryIcons[CATEGORY_DOMAINS] || ShoppingCart;

  if (item.type === CART_ITEM_TYPE.DOMAIN_NAME) {
    return (
      <div className="flex items-center gap-4 py-2 px-2 border-b border-gray-200">
        <IconWrapper>
          <DomainNameIcon className="h-6 w-6 text-blue-600" />
        </IconWrapper>
        <div className="flex flex-col flex-grow">
          <p className="font-medium text-sm text-gray-800">
            {item.domainName} (Domain)
          </p>
          {/* <p className="text-xs text-gray-500">
            {item.tld && `TLD: .${item.tld}`}
          </p> */}
          <p className="text-xs text-gray-500">
            {t("period")}: {item.period} {t("years")}
          </p>
          <div className="flex items-center gap-2 mt-1">
            <span className="text-sm font-semibold text-gray-700">
              {`MAD ${item.price?.toFixed(2)}`}
            </span>
            <span className="text-xs text-gray-400">(HT)</span>
          </div>
        </div>
        {/* Remove quantity controls for domain */}
        <div className="flex flex-col items-center gap-1">
          <button
            className="p-1 mt-2"
            onClick={() =>
              handleQuantityChange(
                item._id, // Use item._id for domains
                -1,
                item?.quantity == 1 ? "delete" : item?.quantity
              )
            }
            title={t("remove_from_cart")}
          >
            <TrashIcon className="w-4 h-4 text-red-500" />
          </button>
        </div>
      </div>
    );
  }

  const IconComponent =
    categoryIcons[item?.package?.brand?.category?.name] || ShoppingCart;

  // Example: customize for Hosting
  if (item?.package?.brand?.category?.name === CATEGORY_HOSTING) {
    return (
      <div className="flex items-center gap-4 py-2 px-2 border-b border-gray-200">
        <IconWrapper>
          <IconComponent className="h-6 w-6 text-blue-600" />
        </IconWrapper>
        <div className="flex flex-col flex-grow">
          <p className="font-medium text-sm text-gray-800 uppercase">
            {item.package?.reference} (Hosting)
          </p>
          <p className="text-xs text-gray-500">
            {item.package?.brand?.name + ": " + item.package?.name}
          </p>

          <p className="text-xs text-gray-500">{item.package?.description}</p>
          {/* <p className="text-xs text-gray-500">{item.package?._id}</p> */}
          <div className="flex items-center gap-2 mt-1">
            <span className="text-sm font-semibold text-gray-700">
              {`MAD ${item.package?.price?.toFixed(2)}`}
            </span>
            <span className="text-xs text-gray-500">x {item.quantity}</span>
            <span className="text-xs text-gray-400">(HT)</span>
          </div>
        </div>
        {/* Quantity controls */}
        <div className="flex flex-col items-center gap-1">
          <button
            className="p-1"
            onClick={() => handleQuantityChange(item.package?._id, 1, 1)}
            disabled={item.quantity >= 10}
            title={t("increase_quantity")}
          >
            <ChevronUpIcon className="w-4 h-4 text-gray-600" />
          </button>
          <span className="text-sm">{item.quantity}</span>
          <button
            className="p-1"
            onClick={() => handleQuantityChange(item.package._id, -1, 1)}
            disabled={item.quantity <= 1}
            title={t("decrease_quantity")}
          >
            <ChevronDownIcon className="w-4 h-4 text-gray-600" />
          </button>
          <button
            className="p-1 mt-2"
            onClick={() =>
              handleQuantityChange(
                item?.package._id,
                -1,
                item?.quantity == 1 ? "delete" : item?.quantity
              )
            }
            title={t("remove_from_cart")}
          >
            <TrashIcon className="w-4 h-4 text-red-500" />
          </button>
        </div>
      </div>
    );
  }

  // SSL Service
  if (item?.package?.brand?.category?.name === CATEGORY_SSL) {
    return (
      <div className="flex items-center gap-4 py-2 px-2 border-b border-gray-200">
        <IconWrapper>
          <IconComponent className="h-6 w-6 text-blue-600" />
        </IconWrapper>
        <div className="flex flex-col flex-grow">
          <p className="font-medium text-sm text-gray-800 uppercase">
            {item.package?.reference} (SSL certificate)
          </p>
          {/* <p className="text-xs text-gray-500">{item.package?.brand?.name}</p> */}
          <p className="text-xs text-gray-500">{item.package?.name}</p>
          <p className="text-xs text-gray-500">{item.package?.description}</p>
          {/* Example: SSL-specific field */}
          <p className="text-xs text-gray-500">
            {item.package?.validation_level &&
              `Validation: ${item.package.validation_level}`}
          </p>
          <div className="flex items-center gap-2 mt-1">
            <span className="text-sm font-semibold text-gray-700">
              {`MAD ${item.package?.price?.toFixed(2)}`}
            </span>
            <span className="text-xs text-gray-500">x {item.quantity}</span>
            <span className="text-xs text-gray-400">(HT)</span>
          </div>
        </div>
        {/* Quantity controls */}
        <div className="flex flex-col items-center gap-1">
          <button
            className="p-1"
            onClick={() => handleQuantityChange(item.package._id, 1, 1)}
            disabled={item.quantity >= 10}
            title={t("increase_quantity")}
          >
            <ChevronUpIcon className="w-4 h-4 text-gray-600" />
          </button>
          <span className="text-sm">{item.quantity}</span>
          <button
            className="p-1"
            onClick={() => handleQuantityChange(item.package._id, -1, 1)}
            disabled={item.quantity <= 1}
            title={t("decrease_quantity")}
          >
            <ChevronDownIcon className="w-4 h-4 text-gray-600" />
          </button>
          <button
            className="p-1 mt-2"
            onClick={() =>
              handleQuantityChange(
                item?.package._id,
                -1,
                item?.quantity == 1 ? "delete" : item?.quantity
              )
            }
            title={t("remove_from_cart")}
          >
            <TrashIcon className="w-4 h-4 text-red-500" />
          </button>
        </div>
      </div>
    );
  }

  // Web Creation Service
  if (item?.package?.brand?.category?.name === CATEGORY_WEB_CREATION) {
    return (
      <div className="flex items-center gap-4 py-2 px-2 border-b border-gray-200">
        <IconWrapper>
          <IconComponent className="h-6 w-6 text-blue-600" />
        </IconWrapper>
        <div className="flex flex-col flex-grow">
          <p className="font-medium text-sm text-gray-800 uppercase">
            {item.package?.reference} (Web Development)
          </p>
          {/* <p className="text-xs text-gray-500">{item.package?.brand?.name}</p> */}
          <p className="text-xs text-gray-500">{item.package?.name}</p>
          <p className="text-xs text-gray-500">{item.package?.description}</p>
          {/* Example: Web creation-specific field */}
          <p className="text-xs text-gray-500">
            {item.package?.project_type && `Type: ${item.package.project_type}`}
          </p>
          <div className="flex items-center gap-2 mt-1">
            <span className="text-sm font-semibold text-gray-700">
              {`MAD ${item.package?.price?.toFixed(2)}`}
            </span>
            <span className="text-xs text-gray-500">x {item.quantity}</span>
            <span className="text-xs text-gray-400">(HT)</span>
          </div>
        </div>
        {/* Quantity controls */}
        <div className="flex flex-col items-center gap-1">
          {/* <button
            className="p-1"
            onClick={() => handleQuantityChange(item.package._id, 1, 1)}
            disabled={item.quantity >= 10}
            title={t("increase_quantity")}
          >
            <ChevronUpIcon className="w-4 h-4 text-gray-600" />
          </button>
          <span className="text-sm">{item.quantity}</span>
          <button
            className="p-1"
            onClick={() => handleQuantityChange(item.package._id, -1, 1)}
            disabled={item.quantity <= 1}
            title={t("decrease_quantity")}
          >
            <ChevronDownIcon className="w-4 h-4 text-gray-600" />
          </button> */}
          <button
            className="p-1 mt-2"
            onClick={() =>
              handleQuantityChange(
                item?.package._id,
                -1,
                item?.quantity == 1 ? "delete" : item?.quantity
              )
            }
            title={t("remove_from_cart")}
          >
            <TrashIcon className="w-4 h-4 text-red-500" />
          </button>
        </div>
      </div>
    );
  }
};

export default CartItemDisplay;
