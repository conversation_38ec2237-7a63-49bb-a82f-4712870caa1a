"use client";

import React, { useState } from "react";
import {
  Save,
  AlertCircle,
  CheckCircle,
  Search,
  Settings as SettingsIcon,
  Globe,
  Mail,
  Phone,
  MapPin,
  ExternalLink,
  Eye,
  Loader2,
  RefreshCw,
  Sparkles,
} from "lucide-react";

/**
 * Demo component showcasing the improved settings design
 * This can be used to preview the design without backend integration
 */
export default function SettingsDemo() {
  const [activeTab, setActiveTab] = useState("general");
  const [hasChanges, setHasChanges] = useState(true);
  const [saving, setSaving] = useState(false);

  const tabs = [
    {
      id: "general",
      label: "General",
      icon: SettingsIcon,
      description: "Basic site information and contact details",
    },
    {
      id: "seo",
      label: "SEO & Analytics",
      icon: Search,
      description: "Search engine optimization and tracking",
    },
  ];

  const handleSave = () => {
    setSaving(true);
    setTimeout(() => {
      setSaving(false);
      setHasChanges(false);
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center mb-2">
                <Sparkles className="h-8 w-8 text-blue-600 mr-3" />
                <h1 className="text-3xl font-bold text-gray-900">
                  Site Settings
                </h1>
              </div>
              <p className="text-gray-600 text-lg">
                Manage your website&apos;s configuration and SEO settings
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {hasChanges && (
                <div className="flex items-center text-amber-600 bg-amber-50 px-3 py-2 rounded-lg border border-amber-200 animate-pulse">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <span className="text-sm font-medium">Unsaved changes</span>
                </div>
              )}
              <button
                className="flex items-center px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                title="Refresh settings"
              >
                <RefreshCw className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Success Alert Demo */}
        <div className="bg-green-50 text-green-600 p-4 rounded-lg flex items-center mb-6 alert-slide-in">
          <CheckCircle className="h-5 w-5 mr-2" />
          Settings saved successfully!
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 inline-flex items-center border-b-2 font-medium text-sm tab-button ${
                    activeTab === tab.id
                      ? "border-blue-500 text-blue-600 active"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <tab.icon className="h-5 w-5 mr-2" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === "general" && (
              <div className="space-y-8 fade-in-up">
                {/* Site Information Section */}
                <div className="form-section blue">
                  <div className="flex items-center mb-4">
                    <Globe className="h-5 w-5 text-blue-600 mr-2" />
                    <h3 className="text-lg font-semibold text-gray-900">
                      Site Information
                    </h3>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Site Name *
                      </label>
                      <input
                        type="text"
                        defaultValue="International Responder Systems"
                        className="w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 settings-input"
                        placeholder="Enter your site name"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Site Description
                      </label>
                      <textarea
                        defaultValue="Healthcare Emergency Response Solutions"
                        rows={3}
                        className="w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 settings-input"
                        placeholder="Brief description of your website"
                      />
                    </div>
                  </div>
                </div>

                {/* Contact Information Section */}
                <div className="form-section green">
                  <div className="flex items-center mb-4">
                    <Mail className="h-5 w-5 text-green-600 mr-2" />
                    <h3 className="text-lg font-semibold text-gray-900">
                      Contact Information
                    </h3>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Contact Email *
                      </label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <input
                          type="email"
                          defaultValue="<EMAIL>"
                          className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200 settings-input"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <input
                          type="tel"
                          defaultValue="+****************"
                          className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200 settings-input"
                          placeholder="+****************"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Social Media Section */}
                <div className="form-section purple">
                  <div className="flex items-center mb-4">
                    <ExternalLink className="h-5 w-5 text-purple-600 mr-2" />
                    <h3 className="text-lg font-semibold text-gray-900">
                      Social Media Links
                    </h3>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        LinkedIn Profile
                      </label>
                      <div className="relative">
                        <ExternalLink className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <input
                          type="url"
                          defaultValue="https://linkedin.com/company/example"
                          className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200 settings-input"
                          placeholder="https://linkedin.com/company/..."
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "seo" && (
              <div className="space-y-8 fade-in-up">
                {/* Meta Tags Section */}
                <div className="form-section blue">
                  <div className="flex items-center mb-4">
                    <Search className="h-5 w-5 text-indigo-600 mr-2" />
                    <h3 className="text-lg font-semibold text-gray-900">
                      Meta Tags & SEO
                    </h3>
                  </div>
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Default Meta Title *
                      </label>
                      <input
                        type="text"
                        defaultValue="International Responder Systems - Healthcare Solutions"
                        className="w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200 settings-input"
                        placeholder="Your Site Title - Tagline"
                        maxLength={60}
                      />
                      <div className="flex justify-between mt-1">
                        <p className="text-sm text-gray-500">
                          Recommended length: 50-60 characters
                        </p>
                        <p className="text-sm text-gray-400 char-counter">
                          55/60
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="bg-gray-50 px-6 py-4 flex justify-end">
            <button
              onClick={handleSave}
              disabled={saving}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50 flex items-center save-button transition-all duration-200"
            >
              {saving ? (
                <Loader2 className="h-5 w-5 mr-2 spinner" />
              ) : (
                <Save className="h-5 w-5 mr-2" />
              )}
              {saving ? "Saving..." : "Save Changes"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
