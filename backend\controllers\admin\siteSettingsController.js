const SiteSettings = require('../../models/SiteSettings');

/**
 * Get site settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getSiteSettings = async (req, res) => {
  try {
    const settings = await SiteSettings.getSettings();

    return res.status(200).json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Error fetching site settings:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch site settings',
      error: error.message
    });
  }
};

/**
 * Update site settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateSiteSettings = async (req, res) => {
  try {
    const { general, seo } = req.body;

    // Validate that we have at least one section
    if (!general && !seo) {
      return res.status(400).json({
        success: false,
        message: 'At least one section (general or seo) is required'
      });
    }

    // Get existing settings or create new ones
    let settings = await SiteSettings.findOne();

    if (settings) {
      // Update existing settings by merging with current data
      if (general) {
        settings.general = { ...settings.general, ...general };
        settings.markModified('general');
      }

      if (seo) {
        settings.seo = { ...settings.seo, ...seo };
        settings.markModified('seo');
      }

      // Add updatedBy if user is authenticated
      if (req.user && req.user._id) {
        settings.updatedBy = req.user._id;
      }

      // Save the updated settings
      await settings.save();
    } else {
      // Create new settings with the provided data
      const newSettingsData = {};

      if (general) newSettingsData.general = general;
      if (seo) newSettingsData.seo = seo;

      // Add updatedBy if user is authenticated
      if (req.user && req.user._id) {
        newSettingsData.updatedBy = req.user._id;
      }

      settings = await SiteSettings.create(newSettingsData);
    }

    return res.status(200).json({
      success: true,
      message: 'Site settings updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating site settings:', error);

    // Handle validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => ({
        field: err.path,
        message: err.message,
        value: err.value
      }));
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Failed to update site settings',
      error: error.message
    });
  }
};

/**
 * Reset site settings to default values
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const resetSiteSettings = async (req, res) => {
  try {
    // Delete existing settings
    await SiteSettings.deleteMany({});

    // Create new default settings
    const defaultSettings = await SiteSettings.getSettings();

    return res.status(200).json({
      success: true,
      message: 'Site settings reset to default values',
      data: defaultSettings
    });
  } catch (error) {
    console.error('Error resetting site settings:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to reset site settings',
      error: error.message
    });
  }
};

/**
 * Get specific section of site settings (general or seo)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getSiteSettingsSection = async (req, res) => {
  try {
    const { section } = req.params;

    if (!['general', 'seo'].includes(section)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid section. Must be "general" or "seo"'
      });
    }

    const settings = await SiteSettings.getSettings();

    return res.status(200).json({
      success: true,
      data: settings[section]
    });
  } catch (error) {
    console.error(`Error fetching ${req.params.section} settings:`, error);
    return res.status(500).json({
      success: false,
      message: `Failed to fetch ${req.params.section} settings`,
      error: error.message
    });
  }
};

/**
 * Update specific section of site settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateSiteSettingsSection = async (req, res) => {
  try {
    const { section } = req.params;
    const updateData = req.body;

    if (!['general', 'seo'].includes(section)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid section. Must be "general" or "seo"'
      });
    }

    // Get existing settings
    let settings = await SiteSettings.findOne();

    if (!settings) {
      settings = await SiteSettings.getSettings();
    }

    // Update the specific section by merging with existing data
    settings[section] = { ...settings[section], ...updateData };

    // Mark the section as modified for Mixed schema
    settings.markModified(section);

    // Add updatedBy if user is authenticated
    if (req.user && req.user._id) {
      settings.updatedBy = req.user._id;
    }

    // Save the updated settings
    await settings.save();

    return res.status(200).json({
      success: true,
      message: `${section.charAt(0).toUpperCase() + section.slice(1)} settings updated successfully`,
      data: settings
    });
  } catch (error) {
    console.error(`Error updating ${req.params.section} settings:`, error);

    // Handle validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => ({
        field: err.path,
        message: err.message,
        value: err.value
      }));
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors
      });
    }

    return res.status(500).json({
      success: false,
      message: `Failed to update ${req.params.section} settings`,
      error: error.message
    });
  }
};

module.exports = {
  getSiteSettings,
  updateSiteSettings,
  resetSiteSettings,
  getSiteSettingsSection,
  updateSiteSettingsSection
};
