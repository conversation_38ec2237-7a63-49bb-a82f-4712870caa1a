/**
 * Mapping of TLDs to their product keys based on the API documentation
 */
const tldProductKeyMap = {
  date: "dotdate",
  io: "dotio",
  online: "dotonline",
  store: "dotstore",
  site: "dotsite",
  xyz: "dotxyz",
  tech: "dottech",
  top: "dottop",
  space: "dotspace",
  website: "dotwebsite",
  host: "dothost",
  life: "dotlife",
  // Standard and 2nd/3rd Level TLDs
  asia: "dotasia",
  "com.au": "thirdleveldotau",
  "net.au": "thirdleveldotau",
  berlin: "dotberlin",
  bid: "dotbid",
  biz: "dombiz",
  best: "dotbest",
  buzz: "dotbuzz",
  bz: "dotbz",
  "com.bz": "thirdleveldotbz",
  "net.bz": "thirdleveldotbz",
  "org.bz": "thirdleveldotbz",
  "co.bz": "thirdleveldotbz",
  ca: "dotca",
  cc: "dotcc",
  club: "dotclub",
  cn: "dotcn",
  "com.cn": "thirdleveldotcn",
  "net.cn": "thirdleveldotcn",
  "org.cn": "thirdleveldotcn",
  "cn.com": "centralniccncom",
  co: "dotco",
  "com.co": "thirdleveldotco",
  "net.co": "thirdleveldotco",
  "nom.co": "thirdleveldotco",
  "co.com": "centralniccocom",
  com: "domcno", // Note: Based on your example, this seems to be a typo for "domcom". Please clarify if "domcno" is intentional.
  "com.de": "centralniccomde",
  coop: "dotcoop",
  de: "dotde",
  desi: "dotdesi",
  es: "dotes",
  eu: "doteu",
  hn: "dothn",
  "com.hn": "thirdleveldothn",
  "net.hn": "thirdleveldothn",
  "org.hn": "thirdleveldothn",
  in: "dotin",
  "co.in": "thirdleveldotin",
  "net.in": "thirdleveldotin",
  "org.in": "thirdleveldotin",
  "gen.in": "thirdleveldotin",
  "firm.in": "thirdleveldotin",
  "ind.in": "thirdleveldotin",
  "in.net": "indotnet",
  info: "dominfo",
  me: "dotme",
  "co.me": "thirdleveldotme",
  "net.me": "thirdleveldotme",
  "org.me": "thirdleveldotme",
  "its.me": "thirdleveldotme",
  "priv.me": "thirdleveldotme",
  mn: "dotmn",
  mobi: "dotmobi",
  name: "dotname",
  net: "dotnet",
  nl: "dotnl",
  nyc: "dotnyc",
  "co.nz": "thirdleveldotnz",
  "net.nz": "thirdleveldotnz",
  "org.nz": "thirdleveldotnz",
  ooo: "dotooo",
  org: "domorg",
  pro: "dotpro",
  pw: "dotpw",
  quebec: "dotquebec",
  ru: "dotru",
  "com.ru": "thirdleveldotru",
  "net.ru": "thirdleveldotru",
  "org.ru": "thirdleveldotru",
  sc: "dotsc",
  sx: "dotsx",
  tel: "dottel",
  trade: "dottrade",
  tv: "dottv",
  uk: "dotuk",
  "co.uk": "thirdleveldotuk",
  "me.uk": "thirdleveldotuk",
  "org.uk": "thirdleveldotuk",
  uno: "dotuno",
  us: "domus",
  vc: "dotvc",
  webcam: "dotwebcam",
  "web.in": "premiumdotin",
  ws: "dotws",
  xxx: "dotxxx",
  // CentralNic Premium/Standard/Other
  "jpn.com": "centralnicpremium",
  "sa.com": "centralnicpremium",
  "se.net": "centralnicpremium",
  "uk.net": "centralnicpremium",
  "ru.com": "centralnicpremium",
  "eu.com": "centralnicstandard",
  "de.com": "centralnicstandard",
  "ae.org": "centralnicstandard",
  "us.com": "centralnicuscom",
  "br.com": "centralnicbrcom",
  "gb.net": "centralnicgbnet",
  "za.com": "centralniczacom",
  "uk.com": "centralnicukcom",
  // Donuts Group 1
  reisen: "donutsgroup1",
  equipment: "donutsgroup1",
  gallery: "donutsgroup1",
  graphics: "donutsgroup1",
  lighting: "donutsgroup1",
  management: "donutsgroup1",
  support: "donutsgroup1",
  technology: "donutsgroup1",
  directory: "donutsgroup1",
  photos: "donutsgroup1",
  international: "donutsgroup1",
  agency: "donutsgroup1",
  report: "donutsgroup1",
  education: "donutsgroup1",
  institute: "donutsgroup1",
  exposed: "donutsgroup1",
  supplies: "donutsgroup1",
  supply: "donutsgroup1",
  gratis: "donutsgroup1",
  schule: "donutsgroup1",
  business: "donutsgroup1",
  network: "donutsgroup1",
  football: "donutsgroup1",
  run: "donutsgroup1",
  soccer: "donutsgroup1",
  fyi: "donutsgroup1",
  // Donuts Group 2
  fish: "donutsgroup2",
  chat: "donutsgroup2",
  media: "donutsgroup2",
  singles: "donutsgroup2",
  vision: "donutsgroup2",
  farm: "donutsgroup2",
  cab: "donutsgroup2",
  domains: "donutsgroup2",
  clothing: "donutsgroup2",
  estate: "donutsgroup2",
  watch: "donutsgroup2",
  computer: "donutsgroup2",
  enterprises: "donutsgroup2",
  construction: "donutsgroup2",
  contractors: "donutsgroup2",
  land: "donutsgroup2",
  events: "donutsgroup2",
  marketing: "donutsgroup2",
  builders: "donutsgroup2",
  town: "donutsgroup2",
  training: "donutsgroup2",
  rentals: "donutsgroup2",
  productions: "donutsgroup2",
  gripe: "donutsgroup2",
  boutique: "donutsgroup2",
  wtf: "donutsgroup2",
  fail: "donutsgroup2",
  florist: "donutsgroup2",
  repair: "donutsgroup2",
  house: "donutsgroup2",
  limited: "donutsgroup2",
  community: "donutsgroup2",
  catering: "donutsgroup2",
  cards: "donutsgroup2",
  cheap: "donutsgroup2",
  vacations: "donutsgroup2",
  foundation: "donutsgroup2",
  care: "donutsgroup2",
  properties: "donutsgroup2",
  industries: "donutsgroup2",
  parts: "donutsgroup2",
  services: "donutsgroup2",
  exchange: "donutsgroup2",
  digital: "donutsgroup2",
  direct: "donutsgroup2",
  place: "donutsgroup2",
  deals: "donutsgroup2",
  discount: "donutsgroup2",
  fitness: "donutsgroup2",
  church: "donutsgroup2",
  guide: "donutsgroup2",
  gifts: "donutsgroup2",
  immo: "donutsgroup2",
  money: "donutsgroup2",
  style: "donutsgroup2",
  school: "donutsgroup2",
  mba: "donutsgroup2",
  cafe: "donutsgroup2",
  express: "donutsgroup2",
  team: "donutsgroup2",
  show: "donutsgroup2",
  sarl: "donutsgroup2",
  plus: "donutsgroup2",
  // Donuts Group 3
  lease: "donutsgroup3",
  ventures: "donutsgroup3",
  holdings: "donutsgroup3",
  codes: "donutsgroup3",
  limo: "donutsgroup3",
  viajes: "donutsgroup3",
  diamonds: "donutsgroup3",
  voyage: "donutsgroup3",
  careers: "donutsgroup3",
  recipes: "donutsgroup3",
  university: "donutsgroup3",
  dating: "donutsgroup3",
  partners: "donutsgroup3",
  holiday: "donutsgroup3",
  financial: "donutsgroup3",
  expert: "donutsgroup3",
  cruises: "donutsgroup3",
  flights: "donutsgroup3",
  villas: "donutsgroup3",
  clinic: "donutsgroup3",
  surgery: "donutsgroup3",
  dental: "donutsgroup3",
  tienda: "donutsgroup3",
  condos: "donutsgroup3",
  maison: "donutsgroup3",
  capital: "donutsgroup3",
  engineering: "donutsgroup3",
  finance: "donutsgroup3",
  insure: "donutsgroup3",
  claims: "donutsgroup3",
  coach: "donutsgroup3",
  memorial: "donutsgroup3",
  tax: "donutsgroup3",
  fund: "donutsgroup3",
  furniture: "donutsgroup3",
  healthcare: "donutsgroup3",
  restaurant: "donutsgroup3",
  pizza: "donutsgroup3",
  legal: "donutsgroup3",
  bingo: "donutsgroup3",
  tennis: "donutsgroup3",
  delivery: "donutsgroup3",
  golf: "donutsgroup3",
  tours: "donutsgroup3",
  apartments: "donutsgroup3",
  wine: "donutsgroup3",
  hockey: "donutsgroup3",
  taxi: "donutsgroup3",
  theater: "donutsgroup3",
  jewelry: "donutsgroup3",
  vin: "donutsgroup3",
  salon: "donutsgroup3",
  // Other specific TLDs
  accountants: "dotaccountants",
  casino: "dotcasino",
  credit: "dotcredit",
  creditcard: "dotcreditcard",
  energy: "dotenergy",
  gold: "dotgold",
  investments: "dotinvestments",
  loans: "dotloans",
  pictures: "dotpictures",
  tires: "dottires",
  actor: "dotactor",
  airforce: "dotairforce",
  army: "dotarmy",
  attorney: "dotattorney",
  auction: "dotauction",
  band: "dotband",
  consulting: "dotconsulting",
  dance: "dotdance",
  degree: "dotdegree",
  democrat: "dotdemocrat",
  dentist: "dotdentist",
  engineer: "dotengineer",
  forsale: "dotforsale",
  futbol: "dotfutbol",
  gives: "dotgives",
  haus: "dothaus",
  immobilien: "dotimmobilien",
  kaufen: "dotkaufen",
  lawyer: "dotlawyer",
  live: "dotlive",
  market: "dotmarket",
  moda: "dotmoda",
  mortgage: "dotmortgage",
  navy: "dotnavy",
  news: "dotnews",
  ninja: "dotninja",
  pub: "dotpub",
  rehab: "dotrehab",
  republican: "dotrepublican",
  reviews: "dotreviews",
  rip: "dotrip",
  rocks: "dotrocks",
  sale: "dotsale",
  social: "dotsocial",
  // Additional TLDs from tldData
  academy: "dotacademy",
  accountant: "dotaccountant",
  adult: "dotadult",
  africa: "dotafrica",
  ai: "dotai",
  amsterdam: "dotamsterdam",
  app: "dotapp",
  archi: "dotarchi",
  art: "dotart",
  associates: "dotassociates",
  audio: "dotaudio",
  auto: "dotauto",
  bar: "dotbar",
  bargains: "dotbargains",
  beer: "dotbeer",
  bet: "dotbet",
  bharat: "dotbharat",
  bike: "dotbike",
  bio: "dotbio",
  black: "dotblack",
  blackfriday: "dotblackfriday",
  blog: "dotblog",
  blue: "dotblue",
  br: "dotbr",
  "com.br": "thirdlevelcomdotbr",
  build: "dotbuild",
  cam: "dotcam",
  camera: "dotcamera",
  camp: "dotcamp",
  capetown: "dotcapetown",
  car: "dotcar",
  career: "dotcareer",
  cars: "dotcars",
  casa: "dotcasa",
  cash: "dotcash",
  center: "dotcenter",
  chineseonline: "dotchineseonline",
  chineseorg: "dotchineseorg",
  chinesewebsite: "dotchinesewebsite",
  christmas: "dotchristmas",
  city: "dotcity",
  cl: "dotcl",
  cleaning: "dotcleaning",
  click: "dotclick",
  cloud: "dotcloud",
  coffee: "dotcoffee",
  college: "dotcollege",
  company: "dotcompany",
  cooking: "dotcooking",
  cool: "dotcool",
  country: "dotcountry",
  coupons: "dotcoupons",
  courses: "dotcourses",
  coza: "dotcoza",
  cricket: "dotcricket",
  cymru: "dotcymru",
  cyrillicorg: "dotcyrillicorg",
  deals: "dotdeals",
  design: "dotdesign",
  dev: "dotdev",
  diet: "dotdiet",
  doctor: "dotdoctor",
  dog: "dotdog",
  download: "dotdownload",
  durban: "dotdurban",
  earth: "dotearth",
  ec: "dotec",
  "com.ec": "thirdleveldotec",
  eco: "doteco",
  email: "dotemail",
  family: "dotfamily",
  fans: "dotfans",
  fashion: "dotfashion",
  feedback: "dotfeedback",
  fishing: "dotfishing",
  fit: "dotfit",
  flowers: "dotflowers",
  fm: "dotfm",
  fr: "dotfr",
  fun: "dotfun",
  furniture: "dotfurniture",
  game: "dotgame",
  games: "dotgames",
  garden: "dotgarden",
  gdn: "dotgdn",
  gift: "dotgift",
  glass: "dotglass",
  global: "dotglobal",
  gmbh: "dotgmbh",
  green: "dotgreen",
  group: "dotgroup",
  guitars: "dotguitars",
  guru: "dotguru",
  health: "dothealth",
  help: "dothelp",
  hiphop: "dothiphop",
  homes: "dothomes",
  horse: "dothorse",
  hospital: "dothospital",
  hosting: "dothosting",
  how: "dothow",
  icu: "doticu",
  id: "dotid",
  ink: "dotink",
  irish: "dotirish",
  jetzt: "dotjetzt",
  jobs: "dotjobs",
  joburg: "dotjoburg",
  juegos: "dotjuegos",
  kim: "dotkim",
  kitchen: "dotkitchen",
  kiwi: "dotkiwi",
  lat: "dotlat",
  law: "dotlaw",
  link: "dotlink",
  loan: "dotloan",
  lol: "dotlol",
  london: "dotlondon",
  lotto: "dotlotto",
  love: "dotlove",
  ltd: "dotltd",
  ltda: "dotltda",
  luxury: "dotluxury",
  markets: "dotmarkets",
  media: "dotmedia",
  men: "dotmen",
  menu: "dotmenu",
  miami: "dotmiami",
  mobile: "dotmobile",
  mom: "dotmom",
  nagoya: "dotnagoya",
  network: "dotnetwork",
  ngo: "dotngo",
  one: "dotone",
  page: "dotpage",
  party: "dotparty",
  pet: "dotpet",
  ph: "dotph",
  photo: "dotphoto",
  photography: "dotphotography",
  physio: "dotphysio",
  pics: "dotpics",
  pink: "dotpink",
  plumbing: "dotplumbing",
  poker: "dotpoker",
  press: "dotpress",
  productions: "dotproductions",
  promo: "dotpromo",
  property: "dotproperty",
  protection: "dotprotection",
  racing: "dotracing",
  red: "dotred",
  rent: "dotrent",
  rest: "dotrest",
  review: "dotreview",
  rodeo: "dotrodeo",
  sangathan: "dotsangathan",
  science: "dotscience",
  scot: "dotscot",
  security: "dotsecurity",
  sexy: "dotsexy",
  shabaka: "dotshabaka",
  shiksha: "dotshiksha",
  shoes: "dotshoes",
  shop: "dotshop",
  shopping: "dotshopping",
  ski: "dotski",
  software: "dotsoftware",
  solar: "dotsolar",
  solutions: "dotsolutions",
  soy: "dotsoy",
  srl: "dotsrl",
  stream: "dotstream",
  studio: "dotstudio",
  study: "dotstudy",
  surf: "dotsurf",
  systems: "dotsystems",
  tattoo: "dottattoo",
  technology: "dottechnology",
  theatre: "dottheatre",
  tips: "dottips",
  today: "dottoday",
  tokyo: "dottokyo",
  tools: "dottools",
  toys: "dottoys",
  trading: "dottrading",
  travel: "dottravel",
  tube: "dottube",
  vegas: "dotvegas",
  vet: "dotvet",
  video: "dotvideo",
  vip: "dotvip",
  vodka: "dotvodka",
  vote: "dotvote",
  voto: "dotvoto",
  wales: "dotwales",
  wang: "dotwang",
  wedding: "dotwedding",
  wiki: "dotwiki",
  win: "dotwin",
  work: "dotwork",
  works: "dotworks",
  world: "dotworld",
  yoga: "dotyoga",
  zone: "dotzone",
  // Other
  premium_dns: "premiumdns",
  privacy_protection: "privacyprotection",
  sunriseafilias: "sunriseafilias",
  "co.de": "codotde", // Based on your original tldData
  "com.mx": "thirdleveldotmx",
  "org.mx": "thirdlevelorgdotmx",
};

/**
 * Gets the product key for a given TLD
 * @param {string} tld - The TLD to look up (with or without leading dot)
 * @returns {string|null} - The product key or null if not found
 */
const getProductKeyForTld = (tld) => {
  if (!tld) return null;

  // Normalize the TLD (remove leading dot if present, convert to lowercase)
  const normalizedTld = tld.startsWith(".")
    ? tld.substring(1).toLowerCase()
    : tld.toLowerCase();

  return tldProductKeyMap[normalizedTld] || null;
};

/**
 * Gets all available TLDs with their product keys
 * @returns {Object} - Map of TLDs to product keys
 */
const getAllTlds = () => {
  return { ...tldProductKeyMap };
};

/**
 * Gets pricing information for a specific TLD from the pricing data
 * @param {string} tld - The TLD to get pricing for (with or without leading dot)
 * @param {Object} pricingData - The pricing data from the API
 * @returns {Object|null} - Pricing information or null if not found
 */
const getTldPricing = (tld, pricingData) => {
  if (!tld || !pricingData) return null;

  const productKey = getProductKeyForTld(tld);

  if (!productKey || !pricingData[productKey]) {
    return null;
  }

  // Return the pricing information
  return {
    tld: tld.startsWith(".") ? tld : `.${tld}`,
    productKey: productKey,
    registerPrice: pricingData[productKey].addnewdomain?.["1"] || null,
    renewPrice: pricingData[productKey].renewdomain?.["1"] || null,
    transferPrice: pricingData[productKey].addtransferdomain?.["1"] || null,
    restorePrice: pricingData[productKey].restoredomain?.["1"] || null,
    currency: "MAD",
    period: "1 year",
  };
};

module.exports = {
  getProductKeyForTld,
  getAllTlds,
  getTldPricing,
  tldProductKeyMap,
};
