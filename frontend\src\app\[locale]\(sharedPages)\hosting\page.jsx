"use client";

import {
  ServerIcon,
  CloudIcon,
  HardDriveIcon,
  ShieldIcon,
  GaugeIcon,
  HeadphonesIcon,
  RocketIcon,
  SparklesIcon,
  ClockIcon,
  UsersIcon,
  GlobeIcon,
  HandCoins,
  ArrowRightIcon,
  ArrowUpRight,
} from "lucide-react";
import { useTranslations } from "next-intl";
import {
  Button,
  <PERSON>,
  Typography,
  Accordion,
  AccordionHeader,
  AccordionBody,
  Carousel,
} from "@material-tailwind/react";
import React from "react";
import { useRouter } from "next/navigation";
import Section from "@/components/home/<USER>";
import { ArrowTurnRightUpIcon } from "@heroicons/react/24/solid";

function Hosting() {
  const t = useTranslations("hosting");
  const router = useRouter();

  // Accordion state management
  const [open, setOpen] = React.useState(0);
  const handleOpen = (value) => setOpen(open === value ? 0 : value);

  return (
    <div className="min-h-screen">
      <div className="">
        <div className="">
          {/* Animated background elements */}
          {/* <div className="absolute inset-0 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-b from-blue-50 to-white"></div>
            <div className="absolute -top-40 -right-40 w-60 sm:w-80 h-60 sm:h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
            <div className="absolute -bottom-40 -left-40 w-60 sm:w-80 h-60 sm:h-80 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
            <div className="absolute top-40 left-40 w-60 sm:w-80 h-60 sm:h-80 bg-indigo-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
          </div> */}

          {/* Floating elements */}
          {/* <div className="absolute inset-0 overflow-hidden pointer-events-none hidden lg:block">
            <div className="absolute top-1/4 left-1/4 transform -translate-x-1/2 -translate-y-1/2 animate-float-slow">
              <ServerIcon className="h-8 w-8 xl:h-12 xl:w-12 text-blue-400 opacity-50" />
            </div>
            <div className="absolute top-1/3 right-1/4 transform translate-x-1/2 -translate-y-1/2 animate-float-slow animation-delay-2000">
              <CloudIcon className="h-8 w-8 xl:h-10 xl:w-10 text-indigo-400 opacity-50" />
            </div>
            <div className="absolute bottom-1/4 left-1/3 transform -translate-x-1/2 translate-y-1/2 animation-delay-4000">
              <ShieldIcon className="h-6 w-6 xl:h-8 xl:w-8 text-purple-400 opacity-50" />
            </div>
          </div> */}

          <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {/* Hero Section */}
            <Section className="text-center mx-auto">
              {/* <div className="inline-block mb-4 sm:mb-6 animate-bounce-slow">
                <RocketIcon className="h-8 w-8 sm:h-10 sm:w-10 xl:h-12 xl:w-12 text-blue-600" />
              </div> */}
              <Typography
                variant="h1"
                className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-extrabold text-gray-900 tracking-tight mb-4 sm:mb-6 animate-fade-in leading-tight"
              >
                {t("headline_start")}
                <span className="ml-4 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent animate-gradient block sm:inline">
                  {t("headline_highlight")}
                </span>
              </Typography>
              <Typography
                variant="paragraph"
                className="text-base sm:text-lg xl:text-xl text-gray-600 mb-6 sm:mb-8 xl:mb-12 leading-relaxed animate-fade-in-up max-w-3xl mx-auto"
              >
                {t("subtext")}
              </Typography>

              <div className="mt-12 sm:mt-16 grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 max-w-6xl mx-auto animate-fade-in-up animation-delay-1000">
                {[
                  {
                    text: t("trust_indic.0"),
                    icon: ClockIcon,
                    bgColor: "bg-blue-50",
                    iconColor: "text-blue-600",
                  },
                  {
                    text: t("trust_indic.1"),
                    icon: UsersIcon,
                    bgColor: "bg-green-50",
                    iconColor: "text-green-600",
                  },
                  {
                    text: t("trust_indic.2"),
                    icon: GlobeIcon,
                    bgColor: "bg-indigo-50",
                    iconColor: "text-indigo-600",
                  },
                  {
                    text: t("trust_indic.3"),
                    icon: HandCoins,
                    bgColor: "bg-teal-50",
                    iconColor: "text-teal-600",
                  },
                ].map((item, index) => (
                  <Card
                    key={index}
                    className={`group flex items-center justify-center gap-3 p-4 ${item.bgColor} backdrop-blur-md border border-gray-200/50 rounded-xl shadow-sm hover:shadow-md hover:-translate-y-1 transition-all duration-300`}
                  >
                    <item.icon
                      className={`h-6 w-6 ${item.iconColor} flex-shrink-0 group-hover:scale-110 transition-transform duration-200`}
                    />
                    <Typography
                      variant="small"
                      className="text-sm sm:text-base text-gray-700 font-semibold leading-tight"
                    >
                      {item.text}
                    </Typography>
                  </Card>
                ))}
              </div>
            </Section>
            {/* Hosting Plans */}
            <div
              id="plans"
              className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 mt-10 sm:mt-18 lg:mt-26 px-4 sm:px-0 max-w-7xl mx-auto"
            >
              {[
                {
                  href: "/hosting/shared",
                  icon: CloudIcon,
                  title: t("hosting_types.0.title"),
                  description: t("hosting_types.0.description"),
                  price: t("hosting_types.0.price"),
                },
                {
                  href: "/hosting/vps",
                  icon: ServerIcon,
                  title: t("hosting_types.1.title"),
                  description: t("hosting_types.1.description"),
                  price: t("hosting_types.1.price"),
                },
                {
                  href: "/hosting/dedicated",
                  icon: HardDriveIcon,
                  title: t("hosting_types.2.title"),
                  description: t("hosting_types.2.description"),
                  price: t("hosting_types.2.price"),
                },
              ].map((plan) => (
                <Card
                  key={plan.title}
                  className="group relative h-full bg-white border border-gray-200/50 rounded-2xl shadow-md hover:shadow-xl hover:-translate-y-2 transition-all duration-300 overflow-hidden"
                >
                  {/* Subtle Gradient Overlay on Hover */}
                  <div className="absolute inset-0 bg-gradient-to-t from-blue-50/0 via-blue-50/0 to-blue-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>

                  <div className="p-6 sm:p-8 flex flex-col items-center relative z-10">
                    <div className="p-4 bg-blue-50 rounded-full mb-6 group-hover:bg-blue-100 group-hover:scale-105 group-hover:shadow-sm transition-all duration-300">
                      <plan.icon className="h-12 w-12 text-blue-600 group-hover:text-blue-700 transition-colors duration-300" />
                    </div>
                    <Typography
                      variant="h3"
                      className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 tracking-tight bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent"
                    >
                      {plan.title}
                    </Typography>
                    <Typography
                      variant="paragraph"
                      className="text-base text-gray-600 text-center mb-8 flex-grow leading-relaxed opacity-90 group-hover:opacity-100 transition-opacity duration-300"
                    >
                      {plan.description}
                    </Typography>
                    <button
                      size="md"
                      className="w-full sm:w-auto rounded-full shadow-md hover:shadow-lg bg-[#606AF5] focus:ring-4 focus:ring-blue-200 transition-all duration-300 flex items-center justify-center gap-2 px-6 py-3 text-white hover:bg-blue-800 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={() => router.push(plan.href)}
                      aria-label={`Start plan at ${plan.price} MAD per month`}
                      disabled={!plan.href} // Optional: disable if no valid href
                    >
                      <RocketIcon
                        className="h-5 w-5 text-white group-hover:rotate-12 transition-transform duration-300"
                        aria-hidden="true"
                      />
                      <span className="text-sm">{t("starting_at")}</span>
                      <span className="text-lg">{plan.price}</span>
                      <span className="text-sm">MAD/mo</span>
                      <ArrowUpRight
                        className="h-5 w-5 text-white group-hover:translate-x-1 transition-transform duration-300"
                        aria-hidden="true"
                      />
                    </button>
                  </div>
                </Card>
              ))}
            </div>
            {/* Features Section */}
            <div className="mt-16 sm:mt-24 lg:mt-32 mb-16 sm:mb-24 lg:mb-32">
              <div className="text-center max-w-3xl mx-auto mb-12 sm:mb-16">
                <Typography
                  variant="h2"
                  className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 sm:mb-6"
                >
                  {t("why_choose_us")}
                </Typography>
                <Typography
                  variant="paragraph"
                  className="text-base sm:text-lg text-gray-600"
                >
                  {t("experience_blend")}
                </Typography>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                {[
                  {
                    icon: ShieldIcon,
                    title: t("hosting_features.0.title"),
                    description: t("hosting_features.0.description"),
                  },
                  {
                    icon: GaugeIcon,
                    title: t("hosting_features.1.title"),
                    description: t("hosting_features.1.description"),
                  },
                  {
                    icon: HeadphonesIcon,
                    title: t("hosting_features.2.title"),
                    description: t("hosting_features.2.description"),
                  },
                ].map((feature) => (
                  <Card
                    key={feature.title}
                    className="p-6 sm:p-8 hover:shadow-lg transition-all duration-300"
                  >
                    <div className="flex justify-center items-center gap-2 p-3 bg-blue-50 rounded-xl mb-4 sm:mb-6">
                      <feature.icon className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
                      <Typography
                        variant="h4"
                        className="text-lg sm:text-xl font-bold text-gray-900"
                      >
                        {feature.title}
                      </Typography>
                    </div>

                    <Typography
                      variant="paragraph"
                      className="text-sm sm:text-base text-gray-600 leading-relaxed"
                    >
                      {feature.description}
                    </Typography>
                  </Card>
                ))}
              </div>
            </div>

            {/* Testimonials */}
            {/* <div className="mb-16 sm:mb-24 lg:mb-32 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <Typography
                variant="h2"
                className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 text-center mb-12 sm:mb-16"
              >
                {t("customer_reviews_heading")}
              </Typography>
              <Carousel
                className="overflow-hidden"
                autoplay
                loop
                navigation={({ setActiveIndex, activeIndex, length }) => (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 z-10">
                    {new Array(length).fill("").map((_, i) => (
                      <span
                        key={i}
                        className={`block h-2 w-2 rounded-full cursor-pointer transition-all duration-300 ${
                          activeIndex === i ? "bg-blue-600 w-4" : "bg-gray-300"
                        }`}
                        onClick={() => setActiveIndex(i)}
                      />
                    ))}
                  </div>
                )}
                swiperParams={{
                  slidesPerView: 1, // Default for mobile
                  spaceBetween: 16,
                  breakpoints: {
                    1024: {
                      slidesPerView: 3,
                      spaceBetween: 32,
                    },
                  },
                }}
              >
                {[
                  {
                    quote: t("testimonials.0.quote"),
                    author: t("testimonials.0.author"),
                    role: t("testimonials.0.role"),
                  },
                  {
                    quote: t("testimonials.1.quote"),
                    author: t("testimonials.1.author"),
                    role: t("testimonials.1.role"),
                  },
                  {
                    quote: t("testimonials.0.quote"), // Duplicate for demo
                    author: t("testimonials.0.author"),
                    role: t("testimonials.0.role"),
                  },
                ].map((testimonial, index) => (
                  <Card
                    key={index}
                    className="p-6 sm:p-8 bg-white border border-gray-100 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 h-full flex flex-col justify-between mx-2"
                  >
                    <div>
                      <QuoteIcon className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600 mb-4 sm:mb-6 opacity-50" />
                      <Typography
                        variant="paragraph"
                        className="text-sm sm:text-base text-gray-600 mb-6 sm:mb-8 italic leading-relaxed"
                      >
                        {testimonial.quote}
                      </Typography>
                    </div>
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 sm:h-12 sm:w-12 rounded-full bg-blue-50 flex items-center justify-center">
                          <StarIcon className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                        </div>
                      </div>
                      <div className="ml-3 sm:ml-4">
                        <Typography
                          variant="small"
                          className="text-sm sm:text-base text-gray-900 font-semibold"
                        >
                          {testimonial.author}
                        </Typography>
                        <Typography
                          variant="small"
                          className="text-xs sm:text-sm text-gray-500"
                        >
                          {testimonial.role}
                        </Typography>
                      </div>
                    </div>
                  </Card>
                ))}
              </Carousel>
            </div> */}

            {/* FAQ Section with Accordion */}
            <div className="mb-20 sm:mb-28 lg:mb-36">
              <Typography
                variant="h2"
                className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 text-center mb-12 sm:mb-16"
              >
                {t("faq_heading")}
              </Typography>
              <div className="space-y-4 max-w-4xl mx-auto">
                {[
                  { question: t("faq.0.question"), answer: t("faq.0.answer") },
                  { question: t("faq.1.question"), answer: t("faq.1.answer") },
                  { question: t("faq.2.question"), answer: t("faq.2.answer") },
                  { question: t("faq.3.question"), answer: t("faq.3.answer") },
                ].map((faq, index) => (
                  <Accordion
                    key={faq.question}
                    open={open === index + 1}
                    className="border border-gray-200/50 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
                  >
                    <AccordionHeader
                      onClick={() => handleOpen(index + 1)}
                      className="p-6 text-left hover:bg-gray-50 rounded-xl transition-colors duration-200"
                    >
                      <Typography
                        variant="h5"
                        className="text-lg sm:text-xl font-semibold text-gray-900"
                      >
                        {faq.question}
                      </Typography>
                    </AccordionHeader>
                    <AccordionBody className="p-6 pt-0">
                      <Typography
                        variant="paragraph"
                        className="text-base text-gray-600 leading-relaxed"
                      >
                        {faq.answer}
                      </Typography>
                    </AccordionBody>
                  </Accordion>
                ))}
              </div>
            </div>
            {/* CTA Section
            <Card className="text-center bg-gradient-to-r from-blue-50 to-indigo-50 p-6 sm:p-8 lg:p-12 mb-8 sm:mb-12 lg:mb-16">
              <Typography variant="h2" className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 sm:mb-6">
                {t('cta.heading')}
              </Typography>
              <Typography variant="paragraph" className="text-base sm:text-lg text-gray-600 mb-6 sm:mb-8 max-w-2xl mx-auto">
                {t('cta.subtext')}
              </Typography>
              <Button
                color="blue"
                size="lg"
                className="inline-block"
                as={Link}
                href="/hosting/vps"
              >
                {t('cta.button_text')}
              </Button>
            </Card> */}
          </div>
        </div>
      </div>
    </div>
  );
}

export default Hosting;
