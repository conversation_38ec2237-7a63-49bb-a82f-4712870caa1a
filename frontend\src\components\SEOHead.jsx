"use client"

import { useEffect, useState } from 'react';
import Head from 'next/head';
import siteSettingsService from '../services/siteSettingsService';

/**
 * SEO Head component that dynamically loads site settings and applies them to the page head
 * This component demonstrates how to use the site settings for SEO purposes
 */
export default function SEOHead({ 
  title, 
  description, 
  keywords, 
  canonical,
  ogImage,
  noIndex = false 
}) {
  const [siteSettings, setSiteSettings] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSiteSettings = async () => {
      try {
        const response = await siteSettingsService.getSiteSettings();
        setSiteSettings(response.data);
      } catch (error) {
        console.error('Error fetching site settings for SEO:', error);
        // Use default values if settings can't be loaded
        setSiteSettings({
          general: {
            siteName: 'Ztechengineering'
          },
          seo: {
            defaultTitle: 'Ztechengineering',
            defaultDescription: 'ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success',
            defaultKeywords: 'web development Morocco, mobile applications Morocco, cloud hosting Morocco, digital agency, ZtechEngineering, website creation, cloud solutions, custom development, web mobile, PWA, digital transformation, web agency Morocco',
            favicon: '',
            googleTagManagerId: '',
            googleSiteVerification: '',
            bingVerification: ''
          }
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSiteSettings();
  }, []);

  if (loading || !siteSettings) {
    return null;
  }

  const {
    general: { siteName },
    seo: {
      defaultTitle,
      defaultDescription,
      defaultKeywords,
      favicon,
      googleTagManagerId,
      googleSiteVerification,
      bingVerification
    }
  } = siteSettings;

  // Use provided props or fall back to site defaults
  const pageTitle = title ? `${title} | ${siteName}` : defaultTitle;
  const pageDescription = description || defaultDescription;
  const pageKeywords = keywords || defaultKeywords;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      {pageKeywords && <meta name="keywords" content={pageKeywords} />}
      
      {/* Favicon */}
      {favicon && <link rel="icon" href={favicon} />}
      
      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:type" content="website" />
      {ogImage && <meta property="og:image" content={ogImage} />}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      {ogImage && <meta name="twitter:image" content={ogImage} />}
      
      {/* Search Engine Verification */}
      {googleSiteVerification && (
        <meta name="google-site-verification" content={googleSiteVerification} />
      )}
      {bingVerification && (
        <meta name="msvalidate.01" content={bingVerification} />
      )}
      
      {/* Robots Meta Tag */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Google Analytics */}
      {googleTagManagerId && (
        <>
          <script
            async
            src={`https://www.googletagmanager.com/gtag/js?id=${googleTagManagerId}`}
          />
          <script
            dangerouslySetInnerHTML={{
              __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${googleTagManagerId}');
              `,
            }}
          />
        </>
      )}
    </Head>
  );
}
