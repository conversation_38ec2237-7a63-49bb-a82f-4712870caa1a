const mongoose = require("mongoose");
const Schema = mongoose.Schema;
const { v4: uuidv4 } = require("uuid");
const crypto = require("crypto");
const AccountRole = require("../constants/enums/account-role");
const AccountState = require("../constants/enums/account-state");
const { console } = require("inspector");

// Sub-schema for Billing Info
const BillingInfoSchema = new Schema(
  {
    isCompany: { type: Boolean, default: false },
    BillToName: { type: String, trim: true, maxlength: 255 },
    email: {
      type: String,
      trim: true,
      match: /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/,
      maxlength: 255,
    },
    phone: {
      type: String,
      trim: true,
      match: /^\+?[0-9]{7,15}$/,
      maxlength: 255,
    },
    address: { type: String, trim: true, maxlength: 255 },
    country: { type: String, trim: true, maxlength: 255 },
    // Company specific fields
    companyICE: { type: String, trim: true, maxlength: 255 },
    companyAddress: { type: String, trim: true, maxlength: 255 },
    companyPhone: {
      type: String,
      trim: true,
      match: /^\+?[0-9]{7,15}$/,
      maxlength: 255,
    },
    companyEmail: {
      type: String,
      trim: true,
      match: /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/,
      maxlength: 255,
    },
    companyLogo: { type: String, trim: true, maxlength: 255 },
  },
  { _id: false }
);

const userSchema = new Schema(
  {
    firstName: { type: String, required: true, maxlength: 255 },
    lastName: { type: String, required: false, maxlength: 255 },
    identifiant: {
      type: String,
      required: false,
      default: "identifiant123",
      maxlength: 255,
    },
    isOAuth: { type: Boolean, required: true, default: false },
    email: {
      type: String,
      unique: true,
      required: true,
      trim: true,
      match: /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/,
      maxlength: 255,
    },
    phone: { type: String, required: false, maxlength: 255 },
    billingInfo: BillingInfoSchema,
    salt: { type: String, select: false },
    hashed_password: { type: String, select: false },
    role: {
      type: String,
      required: true,
      enum: [AccountRole.Admin, AccountRole.Vendor, AccountRole.Customer],
    },
    state: {
      type: String,
      required: true,
      enum: [
        AccountState.NOT_VERIFIED,
        AccountState.VERIFIED,
        AccountState.SUSPENDED,
        AccountState.BLOCKED,
        AccountState.DELETED,
      ],
    },
    photo: {
      type: String,
      default: "/images/user-default-avatar.svg",
      required: true,
    },
    socialMediaData: { type: Object, select: true },
    hashedUniqueStringVerifyEmail: { type: String, select: false },
    hashedUniqueStringResetPassword: { type: String, select: false },
    emailVerifyAt: { type: Date, select: false },
    phonVerifyAt: { type: Date, select: false },
    phoneVerifyCode: { type: String, required: false },
    favoriteLang: { type: String, required: true, default: "fr" },
    // Domain registration contact references
    domainContacts: {
      registrant: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Contact",
        default: null,
      },
      admin: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Contact",
        default: null,
      },
      tech: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Contact",
        default: null,
      },
      billing: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Contact",
        default: null,
      },
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

userSchema
  .virtual("password")
  .set(function (password) {
    this._password = password;
    this.salt = uuidv4();
    this.hashed_password = this.cryptPassword(password);
  })
  .get(function () {
    return this._password;
  });

userSchema.methods.cryptPassword = function (password) {
  if (!password) return "";
  try {
    return crypto.createHash("sha1", this.salt).update(password).digest("hex");
  } catch (error) {
    return "";
  }
};

userSchema.methods.authenticate = async function (password) {
  try {
    const user = await this.model("User")
      .findById(this._id)
      .select("+hashed_password");
    if (!user) {
      return false;
    }
    return user.cryptPassword(password) === user.hashed_password;
  } catch (error) {
    console.error(error);
  }
};

userSchema.methods.hasPassword = async function () {
  try {
    const user = await this.model("User")
      .findById(this._id)
      .select("+hashed_password");
    return !!user.hashed_password && user.hashed_password.length > 0;
  } catch (error) {
    console.error(error);
    return false;
  }
};

const User = mongoose.model("User", userSchema);
module.exports = User;
